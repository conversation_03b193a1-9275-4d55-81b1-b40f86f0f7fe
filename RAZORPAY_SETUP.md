# Razorpay Integration Guide

This guide explains how to properly set up Razorpay payments in your SwipeSense app.

## Current Status

The app has been updated to:
1. Fix Firebase permission issues for the `orders` collection
2. Add a simulation mode for testing payments in Expo Go

## Important Note About Expo Go

**Razorpay requires a development build and will not work in Expo Go.**

The error you're seeing:
```
Invariant Violation: Your JavaScript code tried to access a native module that doesn't exist.
```

This occurs because Razorpay is a native module that requires native code integration, which isn't supported in the Expo Go environment.

## Solution: Create a Development Build

To use Razorpay, you need to create a development build of your app. Here's how:

### Prerequisites

1. Install EAS CLI:
```bash
npm install -g eas-cli
```

2. Log in to your Expo account:
```bash
eas login
```

3. Configure your project:
```bash
eas build:configure
```

### Create a Development Build

#### For Android:

```bash
eas build --profile development --platform android
```

#### For iOS:

```bash
eas build --profile development --platform ios
```

### Install the Development Build

1. For Android: Download the APK from the link provided after the build completes
2. For iOS: Follow the instructions to install via TestFlight or a simulator

## Temporary Solution

While waiting for your development build, the app now includes a simulation mode that will:

1. Create an order in Firestore
2. Simulate a successful payment
3. Update the order status
4. Trigger the success callback

This allows you to test the order flow in Expo Go without the actual Razorpay integration.

## Additional Configuration

### Android Configuration

Add the following to your `app.json` file if not already present:

```json
{
  "expo": {
    "plugins": [
      [
        "react-native-razorpay",
        {
          "merchantId": "your-merchant-id"
        }
      ]
    ]
  }
}
```

### iOS Configuration

For iOS, you need to add URL schemes to your `app.json`:

```json
{
  "expo": {
    "ios": {
      "infoPlist": {
        "LSApplicationQueriesSchemes": [
          "razorpay"
        ]
      }
    }
  }
}
```

## Troubleshooting

If you encounter issues after creating a development build:

1. Ensure your Razorpay API keys in `.env` are correct
2. Verify that the user is authenticated before attempting to create an order
3. Check Firebase security rules if you encounter permission issues
4. Verify that the `react-native-razorpay` package is properly installed:
   ```bash
   npm install react-native-razorpay --save
   ```

## Testing the Integration

After creating a development build:

1. Run your app on a physical device or emulator
2. Navigate to the checkout screen
3. Complete the checkout process
4. Verify that the Razorpay payment flow opens correctly
5. Complete a test payment using Razorpay's test card details
6. Verify that the order is created and updated correctly in Firestore

## References

- [Expo Development Builds Documentation](https://docs.expo.dev/development/introduction/)
- [Razorpay React Native Documentation](https://razorpay.com/docs/payments/payment-gateway/react-native-integration/standard/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
