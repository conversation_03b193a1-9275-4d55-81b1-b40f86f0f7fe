import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Modal,
  TextInput,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  orderBy,
  getDocs,
  doc,
  updateDoc,
  where,
  limit
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';
import { runFullOrderDebug } from '../utils/debugOrderUtils';

const AdminOrderManagement = ({ navigation }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [availableStatuses, setAvailableStatuses] = useState(['all']);

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchOrders();
    }
  }, [isAdmin]);

  useEffect(() => {
    filterOrders();
  }, [orders, selectedFilter, searchQuery]);

  const checkAdminAccess = async () => {
    try {
      console.log('[AdminOrderManagement] Checking admin access for user:', auth.currentUser?.uid);

      if (!auth.currentUser) {
        console.log('[AdminOrderManagement] No authenticated user found');
        Alert.alert('Error', 'You must be logged in to access this area.');
        navigation.goBack();
        return;
      }

      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      console.log('[AdminOrderManagement] Admin status result:', adminStatus);

      if (!adminStatus) {
        console.log('[AdminOrderManagement] User is not an admin');
        Alert.alert('Access Denied', 'You do not have admin privileges.');
        navigation.goBack();
        return;
      }

      console.log('[AdminOrderManagement] Admin access granted');
      setIsAdmin(true);
    } catch (error) {
      console.error('[AdminOrderManagement] Error checking admin status:', error);
      Alert.alert('Error', 'Failed to verify admin status: ' + error.message);
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // Helper function to fetch user information by userId
  const fetchUserInfo = async (userId) => {
    if (!userId) return null;

    try {
      const userDoc = await getDocs(query(collection(db, 'users'), where('__name__', '==', userId), limit(1)));
      if (!userDoc.empty) {
        const userData = userDoc.docs[0].data();
        return {
          name: userData.name || userData.firstName || 'N/A',
          email: userData.email || 'N/A',
          phone: userData.phone || userData.phoneNumber || 'N/A',
          address: userData.address || null
        };
      }
    } catch (error) {
      console.error('[AdminOrderManagement] Error fetching user info for:', userId, error);
    }
    return null;
  };

  const fetchOrders = async () => {
    try {
      console.log('[AdminOrderManagement] Starting to fetch orders...');
      setRefreshing(true);

      // First, try to get a simple count to verify collection access
      try {
        const testQuery = query(collection(db, 'orders'), limit(1));
        const testSnapshot = await getDocs(testQuery);
        console.log('[AdminOrderManagement] Collection access test - found docs:', testSnapshot.size);
      } catch (testError) {
        console.error('[AdminOrderManagement] Collection access test failed:', testError);
        throw new Error('Cannot access orders collection: ' + testError.message);
      }

      // Try different query approaches to handle potential issues
      let ordersSnapshot;

      try {
        // First try with orderBy - this is the preferred approach
        const ordersQuery = query(
          collection(db, 'orders'),
          orderBy('createdAt', 'desc'),
          limit(100)
        );

        console.log('[AdminOrderManagement] Executing orders query with orderBy...');
        ordersSnapshot = await getDocs(ordersQuery);
        console.log('[AdminOrderManagement] Orders query with orderBy completed. Found:', ordersSnapshot.size, 'orders');
      } catch (orderByError) {
        console.warn('[AdminOrderManagement] OrderBy query failed, trying without orderBy:', orderByError);

        // Fallback: try without orderBy in case there's an index issue
        const simpleQuery = query(collection(db, 'orders'), limit(100));
        ordersSnapshot = await getDocs(simpleQuery);
        console.log('[AdminOrderManagement] Simple query completed. Found:', ordersSnapshot.size, 'orders');
      }

      // Process orders and fetch user information
      const ordersData = await Promise.all(ordersSnapshot.docs.map(async (doc) => {
        const data = doc.data();
        console.log('[AdminOrderManagement] Processing order:', doc.id, 'with data keys:', Object.keys(data));

        // Handle different date formats
        let createdAt = new Date();
        if (data.createdAt) {
          if (typeof data.createdAt.toDate === 'function') {
            // Firestore Timestamp
            createdAt = data.createdAt.toDate();
          } else if (data.createdAt instanceof Date) {
            // Already a Date object
            createdAt = data.createdAt;
          } else if (typeof data.createdAt === 'string') {
            // String date
            createdAt = new Date(data.createdAt);
          }
        }

        // Fetch buyer and seller information
        const [buyerInfo, sellerInfo] = await Promise.all([
          fetchUserInfo(data.userId),
          fetchUserInfo(data.sellerId)
        ]);

        return {
          id: doc.id,
          ...data,
          createdAt,
          // Map actual Firestore fields to consistent names
          status: data.orderStatus || data.status || 'pending',
          totalAmount: data.totalAmount || data.amount || 0,
          paymentStatus: data.paymentStatus || 'pending',
          items: data.items || [],
          shippingAddress: data.shippingAddress || null,
          // User information
          userId: data.userId,
          sellerId: data.sellerId,
          // Enhanced buyer information
          buyerEmail: buyerInfo?.email || data.buyerEmail || 'N/A',
          buyerName: buyerInfo?.name || 'N/A',
          buyerPhone: buyerInfo?.phone || 'N/A',
          buyerAddress: buyerInfo?.address || null,
          // Enhanced seller information
          sellerEmail: sellerInfo?.email || data.sellerEmail || 'N/A',
          sellerName: sellerInfo?.name || data.sellerName || 'N/A',
          sellerPhone: sellerInfo?.phone || 'N/A',
          sellerAddress: sellerInfo?.address || null,
          // Payment information
          paymentId: data.paymentId || null,
          razorpayOrderId: data.razorpayOrderId || null,
          // Additional fields
          trackingId: data.trackingId || data.trackingNumber || null,
          deliveryService: data.deliveryService || null,
          cancellationReason: data.cancellationReason || null,
          emailSent: data.emailSent || false
        };
      }));

      console.log('[AdminOrderManagement] Processed orders data:', ordersData.length, 'orders');
      setOrders(ordersData);

      // Extract available statuses from the orders
      updateAvailableStatuses(ordersData);

      if (ordersData.length === 0) {
        console.log('[AdminOrderManagement] No orders found in database');
      }

    } catch (error) {
      console.error('[AdminOrderManagement] Error fetching orders:', error);
      Alert.alert(
        'Error',
        'Failed to fetch orders: ' + error.message + '\n\nPlease check your admin permissions and try again.'
      );
    } finally {
      setRefreshing(false);
    }
  };

  // Function to extract and update available statuses from orders
  const updateAvailableStatuses = (ordersData) => {
    const statusSet = new Set(['all']); // Always include 'all' option

    ordersData.forEach(order => {
      if (order.status) {
        statusSet.add(order.status.toLowerCase());
      }
    });

    // Convert to array and sort for consistent display
    const statusArray = Array.from(statusSet).sort((a, b) => {
      if (a === 'all') return -1; // 'all' always first
      if (b === 'all') return 1;

      // Define order priority for statuses
      const statusOrder = {
        'pending': 1,
        'processing': 2,
        'confirmed': 3,
        'shipped': 4,
        'delivered': 5,
        'cancelled': 6
      };

      return (statusOrder[a] || 999) - (statusOrder[b] || 999);
    });

    setAvailableStatuses(statusArray);
    console.log('[AdminOrderManagement] Available statuses:', statusArray);
  };

  const filterOrders = () => {
    let filtered = orders;

    // Filter by status
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(order => order.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.buyerEmail?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.buyerName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.buyerPhone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.sellerEmail?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.sellerName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.sellerPhone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.paymentId?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.trackingId?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  };

  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      console.log(`[AdminOrderManagement] Updating order ${orderId} status to ${newStatus}`);

      await updateDoc(doc(db, 'orders', orderId), {
        orderStatus: newStatus, // Use orderStatus field as per Firestore structure
        status: newStatus, // Also update status for compatibility
        updatedAt: new Date(),
        updatedBy: auth.currentUser.uid
      });

      // Update local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: newStatus, orderStatus: newStatus, updatedAt: new Date() }
            : order
        )
      );

      Alert.alert('Success', `Order status updated to ${newStatus.toUpperCase()}`);
      setModalVisible(false);

      console.log(`[AdminOrderManagement] Successfully updated order ${orderId} to ${newStatus}`);
    } catch (error) {
      console.error('[AdminOrderManagement] Error updating order status:', error);
      Alert.alert('Error', 'Failed to update order status: ' + error.message);
    }
  };

  const renderOrderItem = ({ item }) => {

    const formatCurrency = (amount) => {
      return `₹${(amount || 0).toFixed(2)}`;
    };

    const getItemsPreview = (items) => {
      if (!items || items.length === 0) return 'No items';
      if (items.length === 1) return items[0].title || 'Item';
      return `${items[0].title || 'Item'} +${items.length - 1} more`;
    };

    return (
      <TouchableOpacity
        style={styles.orderItem}
        onPress={() => {
          setSelectedOrder(item);
          setModalVisible(true);
        }}
      >
        <View style={styles.orderHeader}>
          <View style={styles.orderIdSection}>
            <Text style={styles.orderIdLabel}>Order</Text>
            <Text style={styles.orderId}>#{item.id.slice(-8).toUpperCase()}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Ionicons name={getStatusIcon(item.status)} size={12} color="#fff" />
            <Text style={styles.statusText}>{(item.status || 'pending').toUpperCase()}</Text>
          </View>
        </View>

        <View style={styles.orderContent}>
          <View style={styles.orderMainInfo}>
            <View style={styles.orderRow}>
              <Ionicons name="person-outline" size={16} color="#666" />
              <Text style={styles.orderLabel}>Buyer:</Text>
              <View style={styles.userInfoContainer}>
                <Text style={styles.orderValue}>{item.buyerName || 'N/A'}</Text>
                {item.buyerPhone && item.buyerPhone !== 'N/A' && (
                  <Text style={styles.orderSubValue}>{item.buyerPhone}</Text>
                )}
                {item.buyerEmail && item.buyerEmail !== 'N/A' && (
                  <Text style={styles.orderSubValue}>{item.buyerEmail}</Text>
                )}
              </View>
            </View>

            {((item.sellerName && item.sellerName !== 'N/A') || (item.sellerId)) && (
              <View style={styles.orderRow}>
                <Ionicons name="storefront-outline" size={16} color="#666" />
                <Text style={styles.orderLabel}>Seller:</Text>
                <View style={styles.userInfoContainer}>
                  <Text style={styles.orderValue}>{item.sellerName || 'N/A'}</Text>
                  {item.sellerPhone && item.sellerPhone !== 'N/A' && (
                    <Text style={styles.orderSubValue}>{item.sellerPhone}</Text>
                  )}
                  {item.sellerEmail && item.sellerEmail !== 'N/A' && (
                    <Text style={styles.orderSubValue}>{item.sellerEmail}</Text>
                  )}
                </View>
              </View>
            )}

            <View style={styles.orderRow}>
              <Ionicons name="bag-outline" size={16} color="#666" />
              <Text style={styles.orderLabel}>Items:</Text>
              <Text style={styles.orderValue}>{getItemsPreview(item.items)}</Text>
            </View>

            <View style={styles.orderRow}>
              <Ionicons name="card-outline" size={16} color="#666" />
              <Text style={styles.orderLabel}>Payment:</Text>
              <Text style={[styles.orderValue, {
                color: item.paymentStatus === 'completed' ? '#4CAF50' : '#FFA500'
              }]}>
                {item.paymentStatus || 'pending'}
              </Text>
            </View>
          </View>

          <View style={styles.orderFooter}>
            <View style={styles.totalSection}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalAmount}>{formatCurrency(item.totalAmount)}</Text>
            </View>
            <Text style={styles.orderDate}>
              {item.createdAt instanceof Date
                ? item.createdAt.toLocaleDateString('en-IN', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric'
                })
                : 'N/A'
              }
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Helper function to get status display name
  const getStatusDisplayName = (status) => {
    if (status === 'all') return 'All Orders';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'all': return 'list-outline';
      case 'pending': return 'time-outline';
      case 'processing': return 'refresh-outline';
      case 'confirmed': return 'checkmark-circle-outline';
      case 'shipped': return 'airplane-outline';
      case 'delivered': return 'checkmark-done-outline';
      case 'cancelled': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  };

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'all': return '#666';
      case 'pending': return '#FFA500';
      case 'processing': return '#2196F3';
      case 'confirmed': return '#4CAF50';
      case 'shipped': return '#9C27B0';
      case 'delivered': return '#8BC34A';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  // Render dropdown item
  const renderDropdownItem = (status) => (
    <TouchableOpacity
      key={status}
      style={[
        styles.dropdownItem,
        selectedFilter === status && styles.dropdownItemSelected
      ]}
      onPress={() => {
        setSelectedFilter(status);
        setDropdownVisible(false);
      }}
    >
      <Ionicons
        name={getStatusIcon(status)}
        size={16}
        color={getStatusColor(status)}
        style={styles.dropdownItemIcon}
      />
      <Text style={[
        styles.dropdownItemText,
        selectedFilter === status && styles.dropdownItemTextSelected
      ]}>
        {getStatusDisplayName(status)}
      </Text>
      {selectedFilter === status && (
        <Ionicons name="checkmark" size={16} color="#FF6B6B" />
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading orders...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <SafeAreaHeader
        title="Order Management"
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />



      {/* Debug Info */}
      {__DEV__ && (
        <View style={styles.debugContainer}>
          <Text style={styles.debugText}>
            Debug: {orders.length} orders loaded, {filteredOrders.length} filtered
          </Text>
          <TouchableOpacity
            style={styles.debugButton}
            onPress={() => {
              console.log('[AdminOrderManagement] Manual refresh triggered');
              fetchOrders();
            }}
          >
            <Text style={styles.debugButtonText}>Refresh</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.debugButton, { backgroundColor: '#9C27B0', marginLeft: 8 }]}
            onPress={async () => {
              console.log('[AdminOrderManagement] Running debug analysis...');
              const results = await runFullOrderDebug(auth.currentUser?.uid);
              Alert.alert(
                'Debug Results',
                `Orders found: ${results.tests.orderCollection.totalOrders}\nAdmin access: ${results.tests.adminAccess.isAdmin ? 'Yes' : 'No'}\n\nCheck console for detailed logs.`
              );
            }}
          >
            <Text style={styles.debugButtonText}>Debug</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search orders..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Status Filter Dropdown */}
      <View style={styles.filterContainer}>
        <Text style={styles.filterLabel}>Filter by Status:</Text>
        <View style={styles.dropdownContainer}>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() => setDropdownVisible(!dropdownVisible)}
          >
            <View style={styles.dropdownButtonContent}>
              <Ionicons
                name={getStatusIcon(selectedFilter)}
                size={16}
                color={getStatusColor(selectedFilter)}
                style={styles.dropdownButtonIcon}
              />
              <Text style={styles.dropdownButtonText}>
                {getStatusDisplayName(selectedFilter)}
              </Text>
              <Ionicons
                name={dropdownVisible ? "chevron-up" : "chevron-down"}
                size={16}
                color="#666"
              />
            </View>
          </TouchableOpacity>

          {dropdownVisible && (
            <View style={styles.dropdownMenu}>
              <ScrollView style={styles.dropdownScrollView} nestedScrollEnabled>
                {availableStatuses.map(status => renderDropdownItem(status))}
              </ScrollView>
            </View>
          )}
        </View>
      </View>

      {/* Orders List */}
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={fetchOrders} />
        }
        contentContainerStyle={styles.listContainer}
        onScrollBeginDrag={() => setDropdownVisible(false)} // Close dropdown on scroll
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>No orders found</Text>
          </View>
        }
      />

      {/* Order Details Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaWrapper>
          <SafeAreaHeader
            title="Order Details"
            showBackButton={true}
            onBackPress={() => setModalVisible(false)}
          />

          {selectedOrder && (
            <ScrollView style={styles.modalContent}>
              {/* Order Header */}
              <View style={styles.detailSection}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalOrderId}>Order #{selectedOrder.id.slice(-8).toUpperCase()}</Text>
                  <View style={[styles.modalStatusBadge, {
                    backgroundColor: (() => {
                      switch (selectedOrder.status?.toLowerCase()) {
                        case 'pending': return '#FFA500';
                        case 'processing': return '#2196F3';
                        case 'confirmed': return '#4CAF50';
                        case 'shipped': return '#9C27B0';
                        case 'delivered': return '#8BC34A';
                        case 'cancelled': return '#F44336';
                        default: return '#757575';
                      }
                    })()
                  }]}>
                    <Text style={styles.modalStatusText}>{(selectedOrder.status || 'pending').toUpperCase()}</Text>
                  </View>
                </View>
                <Text style={styles.orderCreatedDate}>
                  Created: {selectedOrder.createdAt instanceof Date
                    ? selectedOrder.createdAt.toLocaleString('en-IN', {
                      day: '2-digit',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })
                    : 'N/A'
                  }
                </Text>
              </View>

              {/* Customer Information */}
              <View style={styles.detailSection}>
                <Text style={styles.detailTitle}>Customer Information</Text>
                <View style={styles.infoRow}>
                  <Ionicons name="person-outline" size={20} color="#666" />
                  <View style={styles.infoContent}>
                    <Text style={styles.infoLabel}>Buyer</Text>
                    <Text style={styles.infoValue}>{selectedOrder.buyerName || 'N/A'}</Text>
                    {selectedOrder.buyerEmail && selectedOrder.buyerEmail !== 'N/A' && (
                      <Text style={styles.infoSubValue}>{selectedOrder.buyerEmail}</Text>
                    )}
                    {selectedOrder.buyerPhone && selectedOrder.buyerPhone !== 'N/A' && (
                      <Text style={styles.infoSubValue}>{selectedOrder.buyerPhone}</Text>
                    )}
                  </View>
                </View>
                {((selectedOrder.sellerName && selectedOrder.sellerName !== 'N/A') || selectedOrder.sellerId) && (
                  <View style={styles.infoRow}>
                    <Ionicons name="storefront-outline" size={20} color="#666" />
                    <View style={styles.infoContent}>
                      <Text style={styles.infoLabel}>Seller</Text>
                      <Text style={styles.infoValue}>{selectedOrder.sellerName || 'N/A'}</Text>
                      {selectedOrder.sellerEmail && selectedOrder.sellerEmail !== 'N/A' && (
                        <Text style={styles.infoSubValue}>{selectedOrder.sellerEmail}</Text>
                      )}
                      {selectedOrder.sellerPhone && selectedOrder.sellerPhone !== 'N/A' && (
                        <Text style={styles.infoSubValue}>{selectedOrder.sellerPhone}</Text>
                      )}
                    </View>
                  </View>
                )}
              </View>

              {/* Order Items */}
              {selectedOrder.items && selectedOrder.items.length > 0 && (
                <View style={styles.detailSection}>
                  <Text style={styles.detailTitle}>Order Items ({selectedOrder.items.length})</Text>
                  {selectedOrder.items.map((item, index) => (
                    <View key={index} style={styles.itemRow}>
                      <View style={styles.itemInfo}>
                        <Text style={styles.itemTitle}>{item.title || 'Untitled Item'}</Text>
                        <Text style={styles.itemDetails}>
                          {item.brand && `${item.brand} • `}
                          {item.gender && `${item.gender} • `}
                          {item.size && `Size: ${item.size} • `}
                          Qty: {item.quantity || 1}
                        </Text>
                      </View>
                      <Text style={styles.itemPrice}>₹{((item.price || 0) * (item.quantity || 1)).toFixed(2)}</Text>
                    </View>
                  ))}
                  <View style={styles.totalRow}>
                    <Text style={styles.totalLabel}>Total Amount</Text>
                    <Text style={styles.totalValue}>₹{(selectedOrder.totalAmount || 0).toFixed(2)}</Text>
                  </View>
                </View>
              )}

              {/* Payment Information */}
              <View style={styles.detailSection}>
                <Text style={styles.detailTitle}>Payment Information</Text>
                <View style={styles.infoRow}>
                  <Ionicons name="card-outline" size={20} color="#666" />
                  <View style={styles.infoContent}>
                    <Text style={styles.infoLabel}>Payment Status</Text>
                    <Text style={[styles.infoValue, {
                      color: selectedOrder.paymentStatus === 'completed' ? '#4CAF50' : '#FFA500'
                    }]}>
                      {(selectedOrder.paymentStatus || 'pending').toUpperCase()}
                    </Text>
                  </View>
                </View>
                {selectedOrder.paymentId && (
                  <View style={styles.infoRow}>
                    <Ionicons name="receipt-outline" size={20} color="#666" />
                    <View style={styles.infoContent}>
                      <Text style={styles.infoLabel}>Payment ID</Text>
                      <Text style={styles.infoValue}>{selectedOrder.paymentId}</Text>
                    </View>
                  </View>
                )}
              </View>

              {/* Shipping Information */}
              {selectedOrder.shippingAddress && (
                <View style={styles.detailSection}>
                  <Text style={styles.detailTitle}>Shipping Address</Text>
                  <View style={styles.addressContainer}>
                    <Text style={styles.addressText}>
                      {selectedOrder.shippingAddress.street || ''}
                      {selectedOrder.shippingAddress.street && '\n'}
                      {selectedOrder.shippingAddress.city || ''} {selectedOrder.shippingAddress.state || ''}
                      {(selectedOrder.shippingAddress.city || selectedOrder.shippingAddress.state) && '\n'}
                      {selectedOrder.shippingAddress.zipCode || ''}
                    </Text>
                  </View>
                  {selectedOrder.trackingId && (
                    <View style={styles.infoRow}>
                      <Ionicons name="location-outline" size={20} color="#666" />
                      <View style={styles.infoContent}>
                        <Text style={styles.infoLabel}>Tracking ID</Text>
                        <Text style={styles.infoValue}>{selectedOrder.trackingId}</Text>
                        {selectedOrder.deliveryService && (
                          <Text style={styles.infoSubValue}>via {selectedOrder.deliveryService}</Text>
                        )}
                      </View>
                    </View>
                  )}
                </View>
              )}

              {/* Action Buttons */}
              <View style={styles.actionSection}>
                <Text style={styles.detailTitle}>Update Order Status</Text>
                <View style={styles.actionButtons}>
                  {selectedOrder.status !== 'processing' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: '#2196F3' }]}
                      onPress={() => updateOrderStatus(selectedOrder.id, 'processing')}
                    >
                      <Text style={styles.actionButtonText}>Process</Text>
                    </TouchableOpacity>
                  )}

                  {selectedOrder.status !== 'confirmed' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
                      onPress={() => updateOrderStatus(selectedOrder.id, 'confirmed')}
                    >
                      <Text style={styles.actionButtonText}>Confirm</Text>
                    </TouchableOpacity>
                  )}

                  {selectedOrder.status !== 'shipped' && selectedOrder.status !== 'delivered' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: '#9C27B0' }]}
                      onPress={() => updateOrderStatus(selectedOrder.id, 'shipped')}
                    >
                      <Text style={styles.actionButtonText}>Ship</Text>
                    </TouchableOpacity>
                  )}

                  {selectedOrder.status === 'shipped' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: '#8BC34A' }]}
                      onPress={() => updateOrderStatus(selectedOrder.id, 'delivered')}
                    >
                      <Text style={styles.actionButtonText}>Deliver</Text>
                    </TouchableOpacity>
                  )}

                  {selectedOrder.status !== 'cancelled' && selectedOrder.status !== 'delivered' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: '#F44336' }]}
                      onPress={() => updateOrderStatus(selectedOrder.id, 'cancelled')}
                    >
                      <Text style={styles.actionButtonText}>Cancel</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </ScrollView>
          )}
        </SafeAreaWrapper>
      </Modal>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    margin: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  dropdownContainer: {
    position: 'relative',
    zIndex: 10,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    paddingHorizontal: 12,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dropdownButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownButtonIcon: {
    marginRight: 8,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginTop: 4,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 11,
  },
  dropdownScrollView: {
    maxHeight: 200,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemSelected: {
    backgroundColor: '#f8f9fa',
  },
  dropdownItemIcon: {
    marginRight: 8,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  dropdownItemTextSelected: {
    fontWeight: '500',
    color: '#FF6B6B',
  },
  listContainer: {
    padding: 16,
  },
  orderItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderIdSection: {
    flex: 1,
  },
  orderIdLabel: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: 'bold',
  },
  orderContent: {
    gap: 8,
  },
  orderMainInfo: {
    gap: 8,
  },
  orderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  orderLabel: {
    fontSize: 14,
    color: '#666',
    minWidth: 60,
  },
  orderValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  userInfoContainer: {
    flex: 1,
  },
  orderSubValue: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  totalSection: {
    alignItems: 'flex-start',
  },
  totalLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  orderDate: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  detailSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalOrderId: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  modalStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  modalStatusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  orderCreatedDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    gap: 12,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  infoSubValue: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemInfo: {
    flex: 1,
    marginRight: 12,
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemDetails: {
    fontSize: 12,
    color: '#666',
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    marginTop: 8,
    borderTopWidth: 2,
    borderTopColor: '#f0f0f0',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  addressContainer: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  addressText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  actionSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  debugContainer: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    margin: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  debugButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  debugButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default AdminOrderManagement;
