import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Alert,
    TextInput,
    Modal,
    ActivityIndicator,
    RefreshControl,
    ScrollView,
    Platform,
    StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db, functions } from '../firebase.config';
import {
    collection,
    query,
    orderBy,
    onSnapshot,
    doc,
    updateDoc,
    serverTimestamp,
    addDoc,
    where,
    getDocs
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';
import { markTransactionAsCompleted, markTransactionInProgress } from '../utils/transactionUtils';
import TransactionAuditTrail from '../components/TransactionAuditTrail';

const AdminTransactionManagementScreen = ({ navigation }) => {
    const [isAdmin, setIsAdmin] = useState(false);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [transactions, setTransactions] = useState([]);
    const [filteredTransactions, setFilteredTransactions] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterStatus, setFilterStatus] = useState('all'); const [selectedTransaction, setSelectedTransaction] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [updating, setUpdating] = useState(false);
    const [dropdownVisible, setDropdownVisible] = useState(false);

    // Performance optimization - cache seller info
    const [sellerCache, setSellerCache] = useState(new Map());
    const [initialLoad, setInitialLoad] = useState(true);

    // Modal form states
    const [newStatus, setNewStatus] = useState('');
    const [adminNotes, setAdminNotes] = useState('');
    const [transactionMethod, setTransactionMethod] = useState('');
    const [referenceNumber, setReferenceNumber] = useState('');
    const [bankName, setBankName] = useState('');

    useEffect(() => {
        checkAdminAccess();
    }, []);

    useEffect(() => {
        if (isAdmin) {
            const unsubscribe = setupRealtimeListener();
            return () => unsubscribe && unsubscribe();
        }
    }, [isAdmin]);

    useEffect(() => {
        filterTransactions();
    }, [transactions, searchQuery, filterStatus]);

    const checkAdminAccess = async () => {
        try {
            console.log('[AdminTransactionManagement] Checking admin status...');
            const adminStatus = await checkAdminStatus(auth.currentUser?.uid);
            console.log('[AdminTransactionManagement] Admin status result:', adminStatus);

            if (!adminStatus) {
                console.log('[AdminTransactionManagement] User is not an admin');
                Alert.alert('Access Denied', 'You do not have admin privileges.');
                navigation.goBack();
                return;
            }

            console.log('[AdminTransactionManagement] Admin access granted');
            setIsAdmin(true);
        } catch (error) {
            console.error('[AdminTransactionManagement] Error checking admin status:', error);
            Alert.alert('Error', 'Failed to verify admin status: ' + error.message);
            navigation.goBack();
        } finally {
            setLoading(false);
        }
    }; const setupRealtimeListener = () => {
        const transactionsRef = collection(db, 'sellerTransactions');
        const q = query(
            transactionsRef,
            orderBy('createdAt', 'desc')
            // Add limit for initial performance - load first 50 transactions
            // Can add pagination later if needed
        );

        const unsubscribe = onSnapshot(q, async (snapshot) => {
            try {
                console.log('[AdminTransactionManagement] Processing', snapshot.docs.length, 'transactions');
                const transactionsData = [];
                const newSellerIds = new Set();

                // First pass: collect unique seller IDs that we don't have cached
                snapshot.docs.forEach(docSnapshot => {
                    const transactionData = docSnapshot.data();
                    if (transactionData.sellerId && !sellerCache.has(transactionData.sellerId)) {
                        newSellerIds.add(transactionData.sellerId);
                    }
                });

                // Batch fetch new seller information if needed
                const newSellerData = new Map(sellerCache);
                if (newSellerIds.size > 0) {
                    console.log('[AdminTransactionManagement] Fetching', newSellerIds.size, 'new sellers');

                    // Convert Set to Array for batching
                    const sellerIdsArray = Array.from(newSellerIds);

                    // Batch fetch sellers (Firestore 'in' query supports up to 10 items)
                    const batchSize = 10;
                    for (let i = 0; i < sellerIdsArray.length; i += batchSize) {
                        const batch = sellerIdsArray.slice(i, i + batchSize);
                        try {
                            const sellersQuery = query(
                                collection(db, 'users'),
                                where('__name__', 'in', batch)
                            );
                            const sellersSnapshot = await getDocs(sellersQuery);

                            sellersSnapshot.docs.forEach(sellerDoc => {
                                const seller = sellerDoc.data();
                                newSellerData.set(sellerDoc.id, {
                                    name: seller.name || 'Unknown Seller',
                                    email: seller.email || 'N/A'
                                });
                            });
                        } catch (error) {
                            console.error('[AdminTransactionManagement] Error fetching seller batch:', error);
                        }
                    }

                    // Update cache
                    setSellerCache(newSellerData);
                }

                // Second pass: build transactions with cached seller info
                for (const docSnapshot of snapshot.docs) {
                    const transactionData = docSnapshot.data();

                    // Get seller information from cache
                    let sellerInfo = { name: 'Unknown Seller', email: 'N/A' };
                    if (transactionData.sellerId) {
                        sellerInfo = newSellerData.get(transactionData.sellerId) || sellerInfo;
                    }

                    transactionsData.push({
                        id: docSnapshot.id,
                        ...transactionData,
                        sellerInfo,
                        createdAt: transactionData.createdAt?.toDate() || new Date(),
                        updatedAt: transactionData.updatedAt?.toDate() || new Date(),
                        transferredAt: transactionData.transferredAt?.toDate() || null
                    });
                }

                setTransactions(transactionsData);

                if (initialLoad) {
                    setInitialLoad(false);
                    console.log('[AdminTransactionManagement] Initial load complete');
                }
            } catch (error) {
                console.error('[AdminTransactionManagement] Error processing transaction updates:', error);
            }
        }, (error) => {
            console.error('[AdminTransactionManagement] Error listening to transactions:', error);
        });

        return unsubscribe;
    };

    const filterTransactions = () => {
        let filtered = transactions;

        // Filter by status
        if (filterStatus !== 'all') {
            filtered = filtered.filter(transaction =>
                transaction.status?.toLowerCase() === filterStatus.toLowerCase()
            );
        }

        // Filter by search query
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase().trim();
            filtered = filtered.filter(transaction =>
                (transaction.sellerInfo?.name && transaction.sellerInfo.name.toLowerCase().includes(query)) ||
                (transaction.sellerInfo?.email && transaction.sellerInfo.email.toLowerCase().includes(query)) ||
                (transaction.orderId && transaction.orderId.toLowerCase().includes(query)) ||
                (transaction.id && transaction.id.toLowerCase().includes(query))
            );
        }

        setFilteredTransactions(filtered);
    };

    const onRefresh = async () => {
        setRefreshing(true);
        // The real-time listener will automatically update the data
        setTimeout(() => setRefreshing(false), 1000);
    }; const formatDate = (date) => {
        if (!date) return 'N/A';

        // Handle Firestore timestamp conversion if needed
        if (date && typeof date.toDate === 'function') {
            date = date.toDate();
        }

        if (!(date instanceof Date) || isNaN(date.getTime())) return 'N/A';

        try {
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.error('[AdminTransactionManagement] Error formatting date:', error);
            return 'N/A';
        }
    };

    const formatAmount = (amount) => {
        if (typeof amount !== 'number') return '₹0.00';
        return `₹${amount.toFixed(2)}`;
    }; const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'transferred':
            case 'completed':
                return '#4CAF50';
            case 'transfer_in_progress':
                return '#FF9800';
            case 'pending_transfer':
                return '#FFC107';
            case 'failed':
                return '#f44336';
            default:
                return '#666';
        }
    };

    const getStatusIcon = (status) => {
        switch (status?.toLowerCase()) {
            case 'transferred':
            case 'completed':
                return 'checkmark-circle';
            case 'transfer_in_progress':
                return 'time';
            case 'pending_transfer':
                return 'hourglass';
            case 'failed':
                return 'close-circle';
            default:
                return 'help-circle';
        }
    };

    const openTransactionModal = (transaction) => {
        setSelectedTransaction(transaction);
        setNewStatus(transaction.status || 'pending');
        setAdminNotes(transaction.adminNotes || '');
        setTransactionMethod(transaction.transactionDetails?.method || '');
        setReferenceNumber(transaction.transactionDetails?.referenceNumber || '');
        setBankName(transaction.transactionDetails?.bankName || '');
        setModalVisible(true);
    }; const updateTransaction = async () => {
        if (!selectedTransaction) return;

        setUpdating(true);
        try {
            const currentStatus = selectedTransaction.status;
            const isNowTransferred = newStatus === 'transferred';
            const isNowCompleted = newStatus === 'completed';
            const isNowInProgress = newStatus === 'transfer_in_progress';

            // Prepare transaction details
            const transactionDetails = {
                method: transactionMethod.trim(),
                referenceNumber: referenceNumber.trim(),
                bankName: bankName.trim(),
                adminId: auth.currentUser?.uid,
                notes: adminNotes.trim()
            };

            if (isNowInProgress && currentStatus === 'pending_transfer') {
                // Mark as in progress
                await markTransactionInProgress(
                    selectedTransaction.id,
                    auth.currentUser?.uid
                );

                Alert.alert(
                    'Success',
                    'Transaction marked as in progress!'
                );
            } else if ((isNowTransferred || isNowCompleted) && (currentStatus === 'transfer_in_progress' || currentStatus === 'pending_transfer' || currentStatus === 'pending')) {
                // Use the utility function to mark as completed/transferred
                await markTransactionAsCompleted(
                    selectedTransaction.id,
                    transactionDetails,
                    newStatus  // Pass the actual status selected by admin
                );

                Alert.alert(
                    'Success',
                    'Transaction marked as completed and seller has been notified!'
                );
            } else {
                // Regular update
                const transactionRef = doc(db, 'sellerTransactions', selectedTransaction.id);

                const updateData = {
                    status: newStatus,
                    adminNotes: adminNotes.trim(),
                    transactionDetails: transactionDetails,
                    updatedAt: serverTimestamp(),
                    adminUpdatedBy: auth.currentUser?.uid
                };

                // If manually setting to completed status, also set transferredAt
                if (isNowCompleted || isNowTransferred) {
                    updateData.transferredAt = serverTimestamp();
                }

                await updateDoc(transactionRef, updateData);                // Send email notification for completed/transferred status
                if (isNowCompleted || isNowTransferred) {
                    try {
                        const sendTransactionStatusUpdateToSeller = httpsCallable(functions, 'sendTransactionStatusUpdateToSeller');
                        await sendTransactionStatusUpdateToSeller({
                            transactionData: {
                                ...selectedTransaction,
                                ...updateData,
                                transactionDetails,
                                id: selectedTransaction.id
                            },
                            newStatus,
                            adminNotes: adminNotes.trim()
                        });
                    } catch (emailError) {
                        console.error('Failed to send notification email:', emailError);
                        // Don't throw error - transaction update is more important than email
                    }
                }

                Alert.alert('Success', 'Transaction updated successfully!');
            }

            setModalVisible(false);
        } catch (error) {
            console.error('Error updating transaction:', error);
            Alert.alert('Error', 'Failed to update transaction: ' + error.message);
        } finally {
            setUpdating(false);
        }
    };

    const sendTransactionNotification = async (transaction, status) => {
        try {
            // This would trigger a cloud function to send email
            await addDoc(collection(db, 'emailNotifications'), {
                type: 'transactionUpdate',
                sellerId: transaction.sellerId,
                sellerEmail: transaction.sellerInfo?.email,
                transactionId: transaction.id,
                orderId: transaction.orderId,
                amount: transaction.amount,
                status: status,
                adminNotes: adminNotes.trim(),
                transactionDetails: {
                    method: transactionMethod.trim(),
                    referenceNumber: referenceNumber.trim(),
                    bankName: bankName.trim()
                },
                createdAt: serverTimestamp()
            });
            console.log('Transaction notification queued for sending');
        } catch (error) {
            console.error('Error queuing transaction notification:', error);
        }
    };

    const createNewTransaction = () => {
        Alert.alert(
            'Create New Transaction',
            'Would you like to create a new transaction for a seller?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Create',
                    onPress: () => navigation.navigate('AdminCreateTransaction')
                }
            ]);
    };

    const renderFilterDropdown = () => {
        const statuses = [
            { value: 'all', label: 'All' },
            { value: 'pending', label: 'Pending' },
            { value: 'processing', label: 'Processing' },
            { value: 'completed', label: 'Completed' },
            { value: 'failed', label: 'Failed' }
        ];

        const currentFilter = statuses.find(s => s.value === filterStatus) || statuses[0];

        return (
            <View style={styles.dropdownContainer}>
                <TouchableOpacity
                    style={styles.dropdownButton}
                    onPress={() => setDropdownVisible(!dropdownVisible)}
                >
                    <Text style={styles.dropdownButtonText}>{currentFilter.label}</Text>
                    <Ionicons
                        name={dropdownVisible ? "chevron-up" : "chevron-down"}
                        size={20}
                        color="#666"
                    />
                </TouchableOpacity>                {dropdownVisible && (
                    <View style={styles.dropdownMenu}>
                        {statuses.map((status, index) => (
                            <TouchableOpacity
                                key={status.value}
                                style={[
                                    styles.dropdownItem,
                                    filterStatus === status.value && styles.activeDropdownItem,
                                    index === statuses.length - 1 && styles.lastDropdownItem
                                ]}
                                onPress={() => {
                                    setFilterStatus(status.value);
                                    setDropdownVisible(false);
                                }}
                            >
                                <Text style={[
                                    styles.dropdownItemText,
                                    filterStatus === status.value && styles.activeDropdownText
                                ]}>
                                    {status.label}
                                </Text>
                                {filterStatus === status.value && (
                                    <Ionicons name="checkmark" size={16} color="#FF6B6B" />
                                )}
                            </TouchableOpacity>
                        ))}
                    </View>
                )}
            </View>
        );
    }; const markAsCompleted = async (transaction) => {
        Alert.alert(
            'Mark as Completed',
            `Are you sure you want to mark this transaction as completed?\n\nAmount: ${formatAmount(transaction.amount)}\nSeller: ${transaction.sellerInfo?.name}`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Mark Completed',
                    style: 'default',
                    onPress: async () => {
                        try {
                            const transactionDetails = {
                                method: 'Bank Transfer', // Default for quick action
                                referenceNumber: 'N/A',    // Default for quick action
                                bankName: 'N/A',           // Default for quick action
                                adminId: auth.currentUser?.uid,
                                notes: 'Marked as transferred by admin (quick action).',
                            }; await markTransactionAsCompleted(
                                transaction.id,
                                transactionDetails,
                                'completed'  // Pass 'completed' as the final status
                            );

                            Alert.alert('Success', 'Transaction marked as transferred!');
                        } catch (error) {
                            console.error('Error marking transaction as completed:', error);
                            Alert.alert('Error', 'Failed to mark transaction as completed: ' + error.message);
                        }
                    }
                }
            ]
        );
    };

    const renderTransactionItem = ({ item }) => {
        const statusColor = getStatusColor(item.status);
        const statusIcon = getStatusIcon(item.status);

        return (
            <TouchableOpacity
                style={styles.transactionCard}
                onPress={() => openTransactionModal(item)}
            >
                <View style={styles.transactionHeader}>
                    <View style={styles.transactionInfo}>
                        <Text style={styles.transactionId}>
                            #{item.id.slice(-8)}
                        </Text>
                        <Text style={styles.sellerName}>
                            {item.sellerInfo?.name || 'Unknown Seller'}
                        </Text>
                        <Text style={styles.sellerEmail}>
                            {item.sellerInfo?.email || 'N/A'}
                        </Text>
                        <Text style={styles.orderInfo}>
                            Order: {item.orderId ? `#${item.orderId.slice(-8)}` : 'N/A'}
                        </Text>
                    </View>
                    <View style={styles.transactionMeta}>
                        <Text style={styles.amount}>
                            {formatAmount(item.amount)}
                        </Text>
                        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
                            <Ionicons name={statusIcon} size={14} color="#fff" />
                            <Text style={styles.statusText}>
                                {item.status || 'Unknown'}
                            </Text>
                        </View>
                        <Text style={styles.dateText}>
                            {formatDate(item.createdAt)}
                        </Text>
                    </View>
                </View>

                {item.adminNotes && (
                    <View style={styles.notesPreview}>
                        <Text style={styles.notesText} numberOfLines={2}>
                            Notes: {item.adminNotes}
                        </Text>
                    </View>
                )}

                {/* Quick Actions */}
                <View style={styles.quickActions}>
                    <TouchableOpacity
                        style={styles.quickActionButton}
                        onPress={() => openTransactionModal(item)}
                    >
                        <Ionicons name="create-outline" size={16} color="#666" />
                        <Text style={styles.quickActionText}>Edit</Text>
                    </TouchableOpacity>

                    {item.status !== 'completed' && (
                        <TouchableOpacity
                            style={[styles.quickActionButton, styles.completeButton]}
                            onPress={() => markAsCompleted(item)}
                        >
                            <Ionicons name="checkmark-circle-outline" size={16} color="#4CAF50" />
                            <Text style={[styles.quickActionText, styles.completeButtonText]}>Mark Completed</Text>
                        </TouchableOpacity>
                    )}                    {(item.status === 'transferred' || item.status === 'completed') && item.transferredAt && (
                        <View style={styles.completedIndicator}>
                            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                            <Text style={styles.completedText}>
                                Transferred {formatDate(item.transferredAt)}
                            </Text>
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    if (loading) {
        return (
            <SafeAreaWrapper>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#FF6B6B" />
                    <Text style={styles.loadingText}>Loading transactions...</Text>
                </View>
            </SafeAreaWrapper>
        );
    }

    if (!isAdmin) {
        return (
            <SafeAreaWrapper>
                <View style={styles.errorContainer}>
                    <Ionicons name="shield-outline" size={60} color="#FF6B6B" />
                    <Text style={styles.errorTitle}>Access Denied</Text>
                    <Text style={styles.errorText}>You don't have permission to view this page.</Text>
                </View>
            </SafeAreaWrapper>
        );
    } return (
        <SafeAreaWrapper>
            <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPress={() => setDropdownVisible(false)}
            >
                <SafeAreaHeader>
                    <TouchableOpacity
                        style={styles.backButton}
                        onPress={() => navigation.goBack()}
                    >
                        <Ionicons name="arrow-back" size={24} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Transaction Management</Text>
                    <TouchableOpacity
                        style={styles.addButton}
                        onPress={createNewTransaction}
                    >
                        <Ionicons name="add" size={24} color="#333" />
                    </TouchableOpacity>
                </SafeAreaHeader>

                {/* Search Bar */}
                <View style={styles.searchContainer}>
                    <Ionicons name="search-outline" size={20} color="#666" style={styles.searchIcon} />
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Search by seller, order ID, or transaction ID..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#999"
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity onPress={() => setSearchQuery('')}>
                            <Ionicons name="close-circle" size={20} color="#666" />
                        </TouchableOpacity>
                    )}
                </View>

                {/* Filter Dropdown */}
                {renderFilterDropdown()}

                {/* Statistics Cards */}
                <View style={styles.statsContainer}>
                    <View style={styles.statCard}>
                        <Text style={styles.statNumber}>{transactions.length}</Text>
                        <Text style={styles.statLabel}>Total</Text>
                    </View>
                    <View style={styles.statCard}>
                        <Text style={styles.statNumber}>
                            {transactions.filter(t => t.status === 'pending').length}
                        </Text>
                        <Text style={styles.statLabel}>Pending</Text>
                    </View>
                    <View style={styles.statCard}>
                        <Text style={styles.statNumber}>
                            {transactions.filter(t => t.status === 'completed').length}
                        </Text>
                        <Text style={styles.statLabel}>Completed</Text>
                    </View>
                    <View style={styles.statCard}>
                        <Text style={styles.statNumber}>
                            {formatAmount(
                                transactions
                                    .filter(t => t.status === 'completed')
                                    .reduce((sum, t) => sum + (t.amount || 0), 0)
                            )}
                        </Text>
                        <Text style={styles.statLabel}>Total Paid</Text>
                    </View>
                </View>                {/* Transactions List */}
                {initialLoad && (
                    <View style={styles.initialLoadingContainer}>
                        <ActivityIndicator size="small" color="#FF6B6B" />
                        <Text style={styles.initialLoadingText}>Loading transactions...</Text>
                    </View>
                )}
                <FlatList
                    data={filteredTransactions}
                    renderItem={renderTransactionItem}
                    keyExtractor={(item) => item.id}
                    contentContainerStyle={styles.listContainer}
                    refreshControl={
                        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                    }
                    ListEmptyComponent={
                        !initialLoad ? (
                            <View style={styles.emptyContainer}>
                                <Ionicons name="card-outline" size={80} color="#ccc" />
                                <Text style={styles.emptyText}>No transactions found</Text>
                                <Text style={styles.emptySubtext}>
                                    {searchQuery || filterStatus !== 'all'
                                        ? 'Try adjusting your search or filters'
                                        : 'No transactions have been created yet'}
                                </Text>
                            </View>
                        ) : null
                    }
                />
            </TouchableOpacity>

            {/* Transaction Update Modal */}
            <Modal
                visible={modalVisible}
                animationType="slide"
                presentationStyle="pageSheet"
                onRequestClose={() => setModalVisible(false)}
            >
                <SafeAreaWrapper>
                    <SafeAreaHeader>
                        <TouchableOpacity
                            style={styles.backButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Ionicons name="close" size={24} color="#333" />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle}>Update Transaction</Text>
                        <TouchableOpacity
                            style={styles.saveButton}
                            onPress={updateTransaction}
                            disabled={updating}
                        >
                            {updating ? (
                                <ActivityIndicator size="small" color="#FF6B6B" />
                            ) : (
                                <Text style={styles.saveButtonText}>Save</Text>
                            )}
                        </TouchableOpacity>
                    </SafeAreaHeader>

                    {selectedTransaction && (
                        <ScrollView style={styles.modalContent}>
                            {/* Transaction Info */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Transaction Details</Text>
                                <Text style={styles.modalInfo}>
                                    Transaction ID: #{selectedTransaction.id.slice(-8)}
                                </Text>
                                <Text style={styles.modalInfo}>
                                    Seller: {selectedTransaction.sellerInfo?.name}
                                </Text>
                                <Text style={styles.modalInfo}>
                                    Amount: {formatAmount(selectedTransaction.amount)}
                                </Text>
                                <Text style={styles.modalInfo}>
                                    Created: {formatDate(selectedTransaction.createdAt)}
                                </Text>
                            </View>                            {/* Status Update */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Status</Text>
                                {newStatus === 'completed' && selectedTransaction.status !== 'completed' && (
                                    <View style={styles.warningBox}>
                                        <Ionicons name="warning" size={20} color="#FF9800" />
                                        <Text style={styles.warningText}>
                                            Marking as completed will notify the seller and record the transfer timestamp.
                                        </Text>
                                    </View>
                                )}
                                <View style={styles.statusButtons}>
                                    {['pending', 'processing', 'completed', 'failed'].map((status) => (
                                        <TouchableOpacity
                                            key={status}
                                            style={[
                                                styles.statusButton,
                                                newStatus === status && styles.activeStatusButton
                                            ]}
                                            onPress={() => setNewStatus(status)}
                                        >
                                            <Text style={[
                                                styles.statusButtonText,
                                                newStatus === status && styles.activeStatusText
                                            ]}>
                                                {status.charAt(0).toUpperCase() + status.slice(1)}
                                            </Text>
                                        </TouchableOpacity>
                                    ))}
                                </View>
                            </View>

                            {/* Admin Notes */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Admin Notes</Text>
                                <TextInput
                                    style={styles.textArea}
                                    value={adminNotes}
                                    onChangeText={setAdminNotes}
                                    placeholder="Add notes about this transaction..."
                                    multiline
                                    numberOfLines={4}
                                    placeholderTextColor="#999"
                                />
                            </View>

                            {/* Transaction Details */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Transaction Details</Text>

                                <Text style={styles.inputLabel}>Payment Method</Text>
                                <TextInput
                                    style={styles.input}
                                    value={transactionMethod}
                                    onChangeText={setTransactionMethod}
                                    placeholder="e.g., Bank Transfer, UPI, etc."
                                    placeholderTextColor="#999"
                                />

                                <Text style={styles.inputLabel}>Reference Number</Text>
                                <TextInput
                                    style={styles.input}
                                    value={referenceNumber}
                                    onChangeText={setReferenceNumber}
                                    placeholder="Transaction reference number"
                                    placeholderTextColor="#999"
                                />                                <Text style={styles.inputLabel}>Bank Name</Text>
                                <TextInput
                                    style={styles.input}
                                    value={bankName}
                                    onChangeText={setBankName}
                                    placeholder="Bank name used for transfer"
                                    placeholderTextColor="#999"
                                />
                            </View>

                            {/* Transaction History */}
                            <TransactionAuditTrail
                                transactionId={selectedTransaction.id}
                                style={styles.auditTrailSection}
                            />
                        </ScrollView>
                    )}
                </SafeAreaWrapper>
            </Modal>
        </SafeAreaWrapper>
    );
};

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#666',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
        marginTop: 16,
        marginBottom: 8,
    },
    errorText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
    },
    backButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    addButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        textAlign: 'center',
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
        borderRadius: 10,
        marginHorizontal: 16,
        marginVertical: 10,
        paddingHorizontal: 12,
        height: 44,
    },
    searchIcon: {
        marginRight: 8,
    },
    searchInput: {
        flex: 1,
        height: 44,
        fontSize: 16,
        color: '#333',
    }, dropdownContainer: {
        paddingHorizontal: 16,
        marginBottom: 10,
        position: 'relative',
        zIndex: 1000,
    },
    dropdownButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    dropdownButtonText: {
        fontSize: 16,
        color: '#333',
        fontWeight: '500',
    },
    dropdownMenu: {
        position: 'absolute',
        top: 50,
        left: 16,
        right: 16,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 8,
        zIndex: 1001,
    }, dropdownItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    lastDropdownItem: {
        borderBottomWidth: 0,
    },
    activeDropdownItem: {
        backgroundColor: '#fff5f5',
    },
    dropdownItemText: {
        fontSize: 16,
        color: '#333',
    },
    activeDropdownText: {
        color: '#FF6B6B',
        fontWeight: '600',
    },
    statsContainer: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        marginBottom: 10,
    },
    statCard: {
        flex: 1,
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginHorizontal: 4,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    statNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#FF6B6B',
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
    listContainer: {
        padding: 16,
    },
    transactionCard: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    transactionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    transactionInfo: {
        flex: 1,
    },
    transactionId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },
    sellerName: {
        fontSize: 15,
        fontWeight: '600',
        color: '#555',
        marginBottom: 2,
    },
    sellerEmail: {
        fontSize: 13,
        color: '#888',
        marginBottom: 4,
    },
    orderInfo: {
        fontSize: 13,
        color: '#666',
    },
    transactionMeta: {
        alignItems: 'flex-end',
    },
    amount: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#4CAF50',
        marginBottom: 8,
    },
    statusBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginBottom: 8,
    },
    statusText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        marginLeft: 4,
        textTransform: 'capitalize',
    },
    dateText: {
        fontSize: 12,
        color: '#888',
    }, notesPreview: {
        marginTop: 12,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    notesText: {
        fontSize: 13,
        color: '#666',
        fontStyle: 'italic',
    },
    quickActions: {
        flexDirection: 'row',
        marginTop: 12,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    quickActionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 8,
        backgroundColor: '#f8f9fa',
        borderWidth: 1,
        borderColor: '#e9ecef',
    },
    completeButton: {
        backgroundColor: '#e8f5e8',
        borderColor: '#4CAF50',
    },
    quickActionText: {
        fontSize: 12,
        color: '#666',
        marginLeft: 4,
        fontWeight: '500',
    },
    completeButtonText: {
        color: '#4CAF50',
    },
    completedIndicator: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    completedText: {
        fontSize: 12,
        color: '#4CAF50',
        marginLeft: 4,
        fontWeight: '500',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#666',
        marginTop: 16,
        marginBottom: 8,
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        textAlign: 'center',
        paddingHorizontal: 40,
    },
    saveButton: {
        paddingHorizontal: 16,
    },
    saveButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#FF6B6B',
    },
    modalContent: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    modalSection: {
        backgroundColor: '#fff',
        marginHorizontal: 16,
        marginVertical: 8,
        borderRadius: 12,
        padding: 16,
    },
    modalSectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 16,
    },
    modalInfo: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    }, statusButtons: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    warningBox: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff3cd',
        borderColor: '#ffeaa7',
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        marginBottom: 16,
    },
    warningText: {
        fontSize: 14,
        color: '#856404',
        marginLeft: 8,
        flex: 1,
    },
    statusButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 20,
        backgroundColor: '#f0f0f0',
        marginRight: 8,
        marginBottom: 8,
    },
    activeStatusButton: {
        backgroundColor: '#FF6B6B',
    },
    statusButtonText: {
        fontSize: 14,
        color: '#666',
        fontWeight: '500',
    },
    activeStatusText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    input: {
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#fff',
        marginBottom: 12,
    },
    textArea: {
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#fff',
        height: 100,
        textAlignVertical: 'top',
    },
    auditTrailSection: {
        marginTop: 8,
        backgroundColor: '#fff',
    },
});

export default AdminTransactionManagementScreen;
