# Firebase Functions Deployment Guide

This guide will help you deploy the Firebase Cloud Functions for the seller verification system.

## Prerequisites

1. Make sure you have the Firebase CLI installed:
   ```
   npm install -g firebase-tools
   ```

2. Make sure you're logged in to Firebase:
   ```
   firebase login
   ```

3. Make sure your project is initialized with Firebase:
   ```
   firebase init
   ```
   - Select "Functions" when prompted
   - Select your Firebase project
   - Choose JavaScript as the language
   - Say yes to ESLint
   - Say yes to installing dependencies

## Setting Up Environment Variables

For security reasons, we use environment variables to store sensitive information like API keys. Follow these steps to set up the environment variables:

1. Create a `.env` file in the `functions` directory:
   ```
   cd functions
   cp .env.example .env
   ```

2. Edit the `.env` file and add your Brevo API key:
   ```
   BREVO_API_KEY=your_brevo_api_key_here
   ```

   **Note:** To get a Brevo API key:
   - Create an account on Brevo (formerly Sendinblue) at https://www.brevo.com/
   - Go to your account settings
   - Navigate to SMTP & API
   - Create a new API key with appropriate permissions (at least email sending)
   - Copy the API key and use it in your `.env` file

3. Set the environment variable in Firebase:
   ```
   firebase functions:config:set brevo.apikey="your_brevo_api_key_here"
   ```

## Deploying the Functions

1. Navigate to the project root directory:
   ```
   cd /path/to/your/project
   ```

2. Deploy the functions:
   ```
   firebase deploy --only functions
   ```

3. Verify the deployment:
   ```
   firebase functions:log
   ```

## Testing the Functions

To test if the functions are working correctly:

1. Go to the Firebase Console: https://console.firebase.google.com/
2. Navigate to your project
3. Go to "Functions" in the left sidebar
4. Check if your functions are listed and active
5. Test the seller verification flow in your app:
   - Sign up as a seller
   - Complete the verification form
   - Check if the admin email (<EMAIL>) receives the notification

## Troubleshooting

If you encounter any issues:

1. Check the Firebase Functions logs:
   ```
   firebase functions:log
   ```

2. Make sure your Brevo API key is valid and has the correct permissions

3. Verify that the environment variables are set correctly:
   ```
   firebase functions:config:get
   ```

4. If you make changes to the functions, redeploy them:
   ```
   firebase deploy --only functions
   ```

## Important Notes

- The email notification is <NAME_EMAIL>
- The verification code is included in the email
- The admin should send the verification code to the seller's email
- When the seller enters the verification code, their account will be automatically approved
- The seller will need to enter this code when they log in next time
