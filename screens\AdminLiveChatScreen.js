import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  addDoc,
  serverTimestamp,
  where,
  getDocs
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';

const AdminLiveChatScreen = ({ navigation }) => {
  const [activeChats, setActiveChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedChat, setSelectedChat] = useState(null);
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [chatModalVisible, setChatModalVisible] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    checkAdminStatus();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchActiveChats();
    }
  }, [isAdmin]);

  const checkAdminStatus = async () => {
    try {
      const userDoc = await getDocs(
        query(collection(db, 'users'), where('uid', '==', auth.currentUser.uid))
      );

      if (!userDoc.empty) {
        const userData = userDoc.docs[0].data();
        setIsAdmin(userData.isAdmin === true);
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    }
  };

  const fetchActiveChats = () => {
    setLoading(true);

    const chatsRef = collection(db, 'supportChats');
    const q = query(
      chatsRef,
      where('status', '==', 'active'),
      orderBy('lastMessageAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const chats = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastMessageAt: doc.data().lastMessageAt?.toDate() || new Date()
      }));

      setActiveChats(chats);
      setLoading(false);
      setRefreshing(false);
    });

    return unsubscribe;
  };

  const openChatModal = async (chat) => {
    setSelectedChat(chat);
    setChatModalVisible(true);

    // Listen to messages for this chat
    const messagesRef = collection(db, 'chatMessages');
    const q = query(
      messagesRef,
      where('chatId', '==', chat.id),
      orderBy('timestamp', 'asc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messages = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      }));

      setChatMessages(messages);
    });

    return unsubscribe;
  };

  const sendAdminMessage = async () => {
    if (!newMessage.trim() || !selectedChat) return;

    const messageText = newMessage.trim();
    setNewMessage('');

    try {
      const messageData = {
        chatId: selectedChat.id,
        senderId: auth.currentUser.uid,
        senderName: auth.currentUser.displayName || 'Support Agent',
        message: messageText,
        timestamp: serverTimestamp(),
        type: 'admin'
      };

      await addDoc(collection(db, 'chatMessages'), messageData);

      // Update chat last message time
      await updateDoc(doc(db, 'supportChats', selectedChat.id), {
        lastMessageAt: serverTimestamp(),
        assignedAgent: auth.currentUser.uid
      });

    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  const closeChatSession = async (chatId) => {
    try {
      await updateDoc(doc(db, 'supportChats', chatId), {
        status: 'closed',
        closedAt: serverTimestamp(),
        closedBy: auth.currentUser.uid
      });

      // Send closing message
      const closingMessage = {
        chatId,
        senderId: 'system',
        senderName: 'SwipeSense Support',
        message: 'This chat session has been closed by our support team. Thank you for contacting us!',
        timestamp: serverTimestamp(),
        type: 'system'
      };

      await addDoc(collection(db, 'chatMessages'), closingMessage);

      setChatModalVisible(false);
      setSelectedChat(null);
      setChatMessages([]);

      Alert.alert('Success', 'Chat session closed successfully');
    } catch (error) {
      console.error('Error closing chat:', error);
      Alert.alert('Error', 'Failed to close chat session');
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchActiveChats();
  };

  const renderChatItem = ({ item }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => openChatModal(item)}
    >
      <View style={styles.chatHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.userName}</Text>
          <Text style={styles.userEmail}>{item.userEmail}</Text>
        </View>
        <View style={styles.chatMeta}>
          <Text style={styles.chatTime}>
            {item.lastMessageAt.toLocaleTimeString()}
          </Text>
          <View style={[styles.userTypeBadge, {
            backgroundColor: item.isSeller ? '#FF6B6B' : '#4ECDC4'
          }]}>
            <Text style={styles.userTypeText}>
              {item.isSeller ? 'Seller' : 'Buyer'}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.chatActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => openChatModal(item)}
        >
          <Ionicons name="chatbubbles-outline" size={16} color="#007AFF" />
          <Text style={styles.actionText}>Reply</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.closeButton]}
          onPress={() => closeChatSession(item.id)}
        >
          <Ionicons name="close-circle-outline" size={16} color="#FF6B6B" />
          <Text style={[styles.actionText, styles.closeText]}>Close</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderMessage = ({ item }) => (
    <View style={[
      styles.messageContainer,
      item.type === 'admin' ? styles.adminMessage :
        item.type === 'system' ? styles.systemMessage : styles.userMessage
    ]}>
      <Text style={styles.senderName}>{item.senderName}</Text>
      <Text style={styles.messageText}>{item.message}</Text>
      <Text style={styles.messageTime}>
        {item.timestamp.toLocaleTimeString()}
      </Text>
    </View>
  );

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have permission to access live chat management.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Live Chat Management</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <View style={styles.container}>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>Loading active chats...</Text>
          </View>
        ) : (
          <FlatList
            data={activeChats}
            renderItem={renderChatItem}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="chatbubbles-outline" size={80} color="#ccc" />
                <Text style={styles.emptyText}>No active chats</Text>
                <Text style={styles.emptySubtext}>
                  Active chat sessions will appear here
                </Text>
              </View>
            }
            contentContainerStyle={styles.listContainer}
          />
        )}

        {/* Chat Modal */}
        <Modal
          visible={chatModalVisible}
          animationType="slide"
          onRequestClose={() => setChatModalVisible(false)}
        >
          <SafeAreaWrapper>
            <KeyboardAvoidingView
              style={styles.modalContainer}
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
              <View style={styles.modalHeader}>
                <TouchableOpacity
                  onPress={() => setChatModalVisible(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.modalTitle}>
                  Chat with {selectedChat?.userName}
                </Text>
              </View>

              <FlatList
                data={chatMessages}
                renderItem={renderMessage}
                keyExtractor={(item) => item.id}
                style={styles.messagesContainer}
                contentContainerStyle={styles.messagesContent}
              />

              <View style={styles.messageInputContainer}>
                <TextInput
                  style={styles.messageInput}
                  value={newMessage}
                  onChangeText={setNewMessage}
                  placeholder="Type your response..."
                  multiline
                />
                <TouchableOpacity
                  style={styles.sendButton}
                  onPress={sendAdminMessage}
                >
                  <Ionicons name="send" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </KeyboardAvoidingView>
          </SafeAreaWrapper>
        </Modal>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  listContainer: {
    padding: 16,
  },
  chatItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  chatMeta: {
    alignItems: 'flex-end',
  },
  chatTime: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  userTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  userTypeText: {
    fontSize: 12,
    color: 'white',
    fontWeight: 'bold',
  },
  chatActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  closeButton: {
    backgroundColor: '#fff5f5',
  },
  actionText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  closeText: {
    color: '#FF6B6B',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalCloseButton: {
    padding: 8,
    marginRight: 12,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  messageContainer: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    maxWidth: '80%',
  },
  userMessage: {
    backgroundColor: '#e3f2fd',
    alignSelf: 'flex-end',
  },
  adminMessage: {
    backgroundColor: '#f3e5f5',
    alignSelf: 'flex-start',
  },
  systemMessage: {
    backgroundColor: '#fff3e0',
    alignSelf: 'center',
  },
  senderName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  messageTime: {
    fontSize: 10,
    color: '#999',
    textAlign: 'right',
  },
  messageInputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AdminLiveChatScreen;
