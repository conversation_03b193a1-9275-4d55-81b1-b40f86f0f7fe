const functions = require('firebase-functions');
const admin = require('firebase-admin');
const sellerVerification = require('./sellerVerification');
const orderEmails = require('./orderEmails');
const supportEmails = require('./supportEmails');
const liveChatNotifications = require('./liveChatNotifications'); // Add support emails
const transactionEmails = require('./transactionEmails'); // Add transaction emails
const Razorpay = require('razorpay');

// Initialize Firebase Admin SDK only if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Export seller verification functions
exports.notifyAdminOfSellerVerification = sellerVerification.notifyAdminOfSellerVerification;
exports.updateUserOnVerificationStatusChange = sellerVerification.updateUserOnVerificationStatusChange;
exports.sendVerificationCodeToAdmin = sellerVerification.sendVerificationCodeToAdmin;

// Export order email functions
exports.sendEmail = orderEmails.sendEmail;
exports.sendOrderConfirmationEmails = orderEmails.sendOrderConfirmationEmails;
exports.handleOrderCreation = orderEmails.handleOrderCreation;
exports.sendOrderStatusUpdateEmails = orderEmails.sendOrderStatusUpdateEmails;
exports.sendOrderNotificationToAdminCallable = orderEmails.sendOrderNotificationToAdminCallable;

// Export support email functions
exports.sendSupportTicketNotification = supportEmails.sendSupportTicketNotification;
exports.sendSupportTicketUpdate = supportEmails.sendSupportTicketUpdate;

// Export transaction email functions (they are already callable functions)
exports.sendTransactionStatusUpdateToSeller = transactionEmails.sendTransactionStatusUpdateToSeller;
exports.sendTransactionNotificationToAdmin = transactionEmails.sendTransactionNotificationToAdmin;

// Export live chat notification functions
exports.notifyAdminsOfNewChat = liveChatNotifications.notifyAdminsOfNewChat;
exports.notifyAdminsOfNewMessage = liveChatNotifications.notifyAdminsOfNewMessage;

// Simple test function
exports.testFunction = functions.https.onCall(async (data, context) => {
  return { message: 'Test function working', timestamp: Date.now() };
});

// Export Razorpay order creation function directly
exports.createRazorpayOrder = functions.https.onCall(async (data, context) => {
  // Authenticate the user
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated to create a Razorpay order'
    );
  }

  const { amount, currency = 'INR', receipt, notes } = data;

  // Validate inputs
  if (typeof amount !== 'number' || amount <= 0) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'The function must be called with a valid amount (number greater than 0)'
    );
  }

  try {
    console.log('Creating Razorpay order with data:', { amount, currency, userId: context.auth.uid });

    // Initialize Razorpay with credentials from Firebase config
    const razorpay = new Razorpay({
      key_id: functions.config().razorpay.key_id,
      key_secret: functions.config().razorpay.key_secret,
    });

    // Create order options
    const orderOptions = {
      amount: Math.round(amount * 100), // Razorpay expects amount in smallest currency unit (paise for INR)
      currency: currency,
      receipt: receipt || `rcpt_${Date.now().toString().slice(-8)}_${context.auth.uid.slice(-6)}`, // Keep under 40 chars
      notes: notes || {}
    };

    console.log('Order options:', orderOptions);

    // Create the order using Razorpay API
    const order = await razorpay.orders.create(orderOptions);

    console.log('Razorpay order created successfully:', order.id);

    // Return the order details
    return {
      orderId: order.id,
      amount: order.amount,
      currency: order.currency,
      status: order.status,
      receipt: order.receipt,
      created_at: order.created_at
    };

  } catch (error) {
    console.error('Error creating Razorpay order:', error);

    // Extract more detailed error information
    let errorMessage = 'Failed to create Razorpay order';
    if (error.error && error.error.description) {
      errorMessage = error.error.description;
    } else if (error.message) {
      errorMessage = error.message;
    }

    throw new functions.https.HttpsError(
      'internal',
      `Failed to create Razorpay order: ${errorMessage}`
    );
  }
});
