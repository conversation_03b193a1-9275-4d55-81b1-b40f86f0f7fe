import React from 'react';
import { TouchableOpacity, StyleSheet, ActivityIndicator, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ResponsiveText from './ResponsiveText';
import theme from '../utils/theme';
import { scaleWidth, scaleHeight } from '../utils/responsiveUtils';
import { createShadow, createBorderRadius } from '../utils/platformUtils';

/**
 * ResponsiveButton component that scales based on screen dimensions
 * and applies platform-specific optimizations
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Button text
 * @param {Function} props.onPress - Button press handler
 * @param {string} props.variant - Button variant (primary, secondary, outline, text)
 * @param {string} props.size - Button size (small, medium, large)
 * @param {boolean} props.fullWidth - Whether button should take full width
 * @param {boolean} props.loading - Whether button is in loading state
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {string} props.iconName - Ionicons icon name
 * @param {string} props.iconPosition - Icon position (left or right)
 * @param {Object} props.style - Additional styles for the button
 * @param {Object} props.textStyle - Additional styles for the button text
 * @param {Object} props.rest - Additional props for the TouchableOpacity
 * @returns {React.ReactElement} - ResponsiveButton component
 */
const ResponsiveButton = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  loading = false,
  disabled = false,
  iconName,
  iconPosition = 'left',
  style,
  textStyle,
  ...rest
}) => {
  // Get the base styles for the variant and size
  const variantStyle = styles[variant] || styles.primary;
  const sizeStyle = styles[size] || styles.medium;

  // Determine the text color based on the variant
  const getTextColor = () => {
    if (disabled) return theme.colors.textLighter;

    switch (variant) {
      case 'primary':
      case 'secondary':
        return theme.colors.white;
      case 'outline':
      case 'text':
        return theme.colors.primary;
      default:
        return theme.colors.white;
    }
  };

  // Determine the icon color based on the variant
  const getIconColor = () => {
    return getTextColor();
  };

  // Render the button content
  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={getTextColor()}
        />
      );
    }

    const iconSize = size === 'small' ? 16 : size === 'large' ? 24 : 20;

    return (
      <>
        {iconName && iconPosition === 'left' && (
          <Ionicons
            name={iconName}
            size={iconSize}
            color={getIconColor()}
            style={styles.iconLeft}
          />
        )}
        <ResponsiveText
          variant="button"
          color={getTextColor()}
          style={[
            styles.buttonText,
            size === 'small' && styles.smallText,
            size === 'large' && styles.largeText,
            textStyle,
          ]}
        >
          {title}
        </ResponsiveText>
        {iconName && iconPosition === 'right' && (
          <Ionicons
            name={iconName}
            size={iconSize}
            color={getIconColor()}
            style={styles.iconRight}
          />
        )}
      </>
    );
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      style={[
        styles.button,
        variantStyle,
        sizeStyle,
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style,
      ]}
      {...rest}
    >
      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: theme.borders.radius.md,
    ...createBorderRadius(theme.borders.radius.md),
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Variant styles
  primary: {
    backgroundColor: theme.colors.primary,
    ...createShadow({
      elevation: 2,
      color: theme.colors.primary,
      opacity: 0.4,
    }),
  },
  secondary: {
    backgroundColor: theme.colors.secondary,
    ...createShadow({
      elevation: 2,
      color: theme.colors.secondary,
      opacity: 0.4,
    }),
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  text: {
    backgroundColor: 'transparent',
    ...createShadow({
      elevation: 0,
      opacity: 0,
    }),
  },
  // Size styles
  small: {
    paddingVertical: scaleHeight(8),
    paddingHorizontal: scaleWidth(16),
    minWidth: scaleWidth(80),
  },
  medium: {
    paddingVertical: scaleHeight(12),
    paddingHorizontal: scaleWidth(24),
    minWidth: scaleWidth(120),
  },
  large: {
    paddingVertical: scaleHeight(16),
    paddingHorizontal: scaleWidth(32),
    minWidth: scaleWidth(160),
  },
  // Width styles
  fullWidth: {
    width: '100%',
  },
  // State styles
  disabled: {
    backgroundColor: theme.colors.backgroundDark,
    borderColor: theme.colors.borderDark,
    ...createShadow({
      elevation: 0,
      opacity: 0,
    }),
  },
  // Text styles
  buttonText: {
    textAlign: 'center',
  },
  smallText: {
    fontSize: theme.typography.fontSizes.sm,
  },
  largeText: {
    fontSize: theme.typography.fontSizes.lg,
  },
  // Icon styles
  iconLeft: {
    marginRight: theme.spacing.xs,
  },
  iconRight: {
    marginLeft: theme.spacing.xs,
  },
});

export default ResponsiveButton;
