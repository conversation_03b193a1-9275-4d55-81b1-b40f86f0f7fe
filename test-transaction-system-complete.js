// Transaction System End-to-End Test
// This script tests the complete transaction management flow

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, where, orderBy } = require('firebase/firestore');
const { getFunctions, httpsCallable } = require('firebase/functions');

// Initialize Firebase (you'll need to add your config)
const firebaseConfig = {
    // Your Firebase config here
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.REACT_APP_FIREBASE_APP_ID
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const functions = getFunctions(app);

class TransactionSystemTester {
    constructor() {
        this.testResults = [];
        this.errors = [];
    }

    log(message, isError = false) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${message}`;

        if (isError) {
            console.error(logMessage);
            this.errors.push(logMessage);
        } else {
            console.log(logMessage);
            this.testResults.push(logMessage);
        }
    }

    async testTransactionCreation() {
        this.log('=== Testing Transaction Creation ===');

        try {
            // Check if transactions are being created properly
            const transactionsQuery = query(
                collection(db, 'sellerTransactions'),
                orderBy('createdAt', 'desc')
            );

            const transactionsSnapshot = await getDocs(transactionsQuery);
            const transactions = [];

            transactionsSnapshot.forEach(doc => {
                transactions.push({ id: doc.id, ...doc.data() });
            });

            this.log(`Found ${transactions.length} total transactions`);

            // Check transaction structure
            if (transactions.length > 0) {
                const sampleTransaction = transactions[0];
                const requiredFields = [
                    'transactionNumber',
                    'orderId',
                    'sellerId',
                    'buyerId',
                    'amount',
                    'status',
                    'createdAt'
                ];

                const missingFields = requiredFields.filter(field => !sampleTransaction[field]);

                if (missingFields.length === 0) {
                    this.log('✓ Transaction structure is correct');
                } else {
                    this.log(`✗ Missing fields in transaction: ${missingFields.join(', ')}`, true);
                }

                // Check status values
                const validStatuses = ['pending_transfer', 'transfer_in_progress', 'transferred'];
                const statusCounts = {};

                transactions.forEach(transaction => {
                    const status = transaction.status;
                    statusCounts[status] = (statusCounts[status] || 0) + 1;
                });

                this.log('Transaction status distribution:');
                Object.entries(statusCounts).forEach(([status, count]) => {
                    const isValid = validStatuses.includes(status);
                    this.log(`  ${status}: ${count} ${isValid ? '✓' : '✗ (invalid status)'}`, !isValid);
                });
            } else {
                this.log('✗ No transactions found - may indicate transaction creation is not working', true);
            }

        } catch (error) {
            this.log(`✗ Error testing transaction creation: ${error.message}`, true);
        }
    }

    async testAuditTrail() {
        this.log('=== Testing Audit Trail ===');

        try {
            const auditQuery = query(
                collection(db, 'transactionAudit'),
                orderBy('timestamp', 'desc')
            );

            const auditSnapshot = await getDocs(auditQuery);
            const auditEntries = [];

            auditSnapshot.forEach(doc => {
                auditEntries.push({ id: doc.id, ...doc.data() });
            });

            this.log(`Found ${auditEntries.length} audit trail entries`);

            if (auditEntries.length > 0) {
                const sampleAudit = auditEntries[0];
                const requiredFields = [
                    'transactionId',
                    'newStatus',
                    'actionBy',
                    'timestamp',
                    'actionType'
                ];

                const missingFields = requiredFields.filter(field => !sampleAudit[field]);

                if (missingFields.length === 0) {
                    this.log('✓ Audit trail structure is correct');
                } else {
                    this.log(`✗ Missing fields in audit entry: ${missingFields.join(', ')}`, true);
                }

                // Check audit trail completeness
                const auditByTransaction = {};
                auditEntries.forEach(entry => {
                    const txId = entry.transactionId;
                    if (!auditByTransaction[txId]) {
                        auditByTransaction[txId] = [];
                    }
                    auditByTransaction[txId].push(entry);
                });

                this.log(`Audit trails exist for ${Object.keys(auditByTransaction).length} transactions`);
            } else {
                this.log('✗ No audit trail entries found - audit trail may not be working', true);
            }

        } catch (error) {
            this.log(`✗ Error testing audit trail: ${error.message}`, true);
        }
    }

    async testEmailNotifications() {
        this.log('=== Testing Email Notification Setup ===');

        try {
            // Test if email functions are deployed
            const testFunction = httpsCallable(functions, 'sendTransactionNotificationToAdmin');

            // This will fail if function doesn't exist
            try {
                await testFunction({});
            } catch (funcError) {
                if (funcError.code === 'functions/not-found') {
                    this.log('✗ Email notification functions not deployed', true);
                } else if (funcError.code === 'functions/invalid-argument') {
                    this.log('✓ Email notification functions are deployed (got expected validation error)');
                } else {
                    this.log(`Email function test result: ${funcError.message}`);
                }
            }

        } catch (error) {
            this.log(`✗ Error testing email notifications: ${error.message}`, true);
        }
    }

    async testTransactionFlow() {
        this.log('=== Testing Transaction Flow Logic ===');

        try {
            // Get transactions in different states
            const pendingQuery = query(
                collection(db, 'sellerTransactions'),
                where('status', '==', 'pending_transfer')
            );

            const progressQuery = query(
                collection(db, 'sellerTransactions'),
                where('status', '==', 'transfer_in_progress')
            );

            const completedQuery = query(
                collection(db, 'sellerTransactions'),
                where('status', '==', 'transferred')
            );

            const [pendingSnap, progressSnap, completedSnap] = await Promise.all([
                getDocs(pendingQuery),
                getDocs(progressQuery),
                getDocs(completedQuery)
            ]);

            this.log(`Pending transfers: ${pendingSnap.size}`);
            this.log(`In progress transfers: ${progressSnap.size}`);
            this.log(`Completed transfers: ${completedSnap.size}`);

            // Check for proper timestamps
            let timestampIssues = 0;

            completedSnap.forEach(doc => {
                const data = doc.data();
                if (!data.transferredAt) {
                    timestampIssues++;
                }
            });

            if (timestampIssues === 0) {
                this.log('✓ All completed transactions have transfer timestamps');
            } else {
                this.log(`✗ ${timestampIssues} completed transactions missing transferredAt timestamp`, true);
            }

        } catch (error) {
            this.log(`✗ Error testing transaction flow: ${error.message}`, true);
        }
    }

    async testTransactionAmounts() {
        this.log('=== Testing Transaction Amount Calculations ===');

        try {
            const transactionsQuery = query(
                collection(db, 'sellerTransactions'),
                orderBy('createdAt', 'desc')
            );

            const snapshot = await getDocs(transactionsQuery);
            const transactions = [];

            snapshot.forEach(doc => {
                transactions.push({ id: doc.id, ...doc.data() });
            });

            let amountIssues = 0;
            let totalPending = 0;
            let totalTransferred = 0;

            transactions.forEach(transaction => {
                // Check if amount is a valid number
                if (typeof transaction.amount !== 'number' || transaction.amount <= 0) {
                    amountIssues++;
                }

                // Calculate totals
                if (transaction.status === 'pending_transfer' || transaction.status === 'transfer_in_progress') {
                    totalPending += transaction.amount || 0;
                } else if (transaction.status === 'transferred') {
                    totalTransferred += transaction.amount || 0;
                }
            });

            if (amountIssues === 0) {
                this.log('✓ All transactions have valid amounts');
            } else {
                this.log(`✗ ${amountIssues} transactions have invalid amounts`, true);
            }

            this.log(`Total pending amount: ₹${totalPending.toFixed(2)}`);
            this.log(`Total transferred amount: ₹${totalTransferred.toFixed(2)}`);

        } catch (error) {
            this.log(`✗ Error testing transaction amounts: ${error.message}`, true);
        }
    }

    async runAllTests() {
        this.log('Starting Transaction System End-to-End Test');
        this.log('============================================');

        await this.testTransactionCreation();
        await this.testAuditTrail();
        await this.testEmailNotifications();
        await this.testTransactionFlow();
        await this.testTransactionAmounts();

        this.log('============================================');
        this.log('Test Summary');
        this.log(`Total log entries: ${this.testResults.length}`);
        this.log(`Errors found: ${this.errors.length}`);

        if (this.errors.length === 0) {
            this.log('✓ All tests passed! Transaction system appears to be working correctly.');
        } else {
            this.log('✗ Issues found in transaction system:');
            this.errors.forEach(error => {
                this.log(error);
            });
        }

        return {
            success: this.errors.length === 0,
            errors: this.errors,
            results: this.testResults
        };
    }
}

// Run tests
async function runTransactionSystemTest() {
    const tester = new TransactionSystemTester();
    return await tester.runAllTests();
}

// Export for use in other files
module.exports = {
    TransactionSystemTester,
    runTransactionSystemTest
};

// Run immediately if called directly
if (require.main === module) {
    runTransactionSystemTest()
        .then(results => {
            process.exit(results.success ? 0 : 1);
        })
        .catch(error => {
            console.error('Test execution failed:', error);
            process.exit(1);
        });
}
