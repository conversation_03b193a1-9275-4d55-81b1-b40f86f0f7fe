# Firebase Permission Issues Fixed

This document explains the fixes implemented to resolve the Firebase permission issues in your app.

## Issues Fixed

1. **Permission Error on Swipe Actions**
   - Error updating `swipeViewCount` when swiping cards right (like) or left (dislike)
   - Fixed by updating Firestore security rules to allow `swipeViewCount` updates

2. **Cart Update Error**
   - Error adding items to cart with undefined title field
   - Fixed by adding default values for all required fields in cart items

## Changes Made

### 1. Updated Firestore Security Rules

The security rules have been updated to allow authenticated users to update the `swipeViewCount` field:

```javascript
// Anyone can update like/save related fields
request.resource.data.diff(resource.data).affectedKeys()
  .hasOnly(['likeCount', 'likedBy', 'dislikedBy', 'saveCount', 'viewCount', 'swipeViewCount'])
```

### 2. Fixed Cart Functionality

The cart functionality has been updated to handle undefined values:

```javascript
// In ItemDetailsScreen.js
await addDoc(collection(db, 'users', currentUserId, 'cart'), {
  itemId: itemId,
  addedAt: new Date(),
  title: item.title || 'Untitled Item',
  category: item.category || 'Uncategorized',
  brand: item.brand || 'Unknown Brand',
  imageUrl: item.imageUrl || null,
  size: item.size || 'One Size'
});

// In ClothingFeedScreen.js
await addDoc(collection(db, 'users', currentUserId, 'cart'), {
  itemId: item.id,
  addedAt: new Date(),
  title: item.title || 'Untitled Item',
  category: item.category || 'Uncategorized',
  brand: item.brand || 'Unknown Brand',
  imageUrl: item.imageUrl || null,
  size: item.size || 'One Size'
});
```

### 3. Improved swipeViewCount Update Logic

The `processSwipeCompletion` function in ClothingFeedScreen.js has been updated to handle the swipeViewCount update more robustly:

```javascript
// Increment the swipeViewCount in the clothingItems collection
try {
  const itemRef = doc(db, 'clothingItems', swipedItem.id);
  
  // First check if the item exists
  const itemDoc = await getDoc(itemRef);
  if (itemDoc.exists()) {
    // Initialize swipeViewCount if it doesn't exist
    const currentCount = itemDoc.data().swipeViewCount || 0;
    
    await updateDoc(itemRef, {
      swipeViewCount: currentCount + 1
    });
    console.log(`[ClothingFeed] Updated swipeViewCount for item ${swipedItem.id}`);
  } else {
    console.log(`[ClothingFeed] Item ${swipedItem.id} does not exist, cannot update swipeViewCount`);
  }
} catch (error) {
  console.error('[ClothingFeed] Error updating swipeViewCount:', error);
}
```

## How to Deploy the Changes

1. **Deploy the Updated Security Rules**

   Run the `deploy-rules.bat` script to deploy the updated security rules to Firebase:

   ```
   ./deploy-rules.bat
   ```

2. **Restart the App**

   Restart your app to ensure all changes take effect.

## Best Practices for Future Development

1. **Always Validate Data**
   - Always provide default values for required fields
   - Use the `||` operator to provide fallbacks for potentially undefined values

2. **Check Item Existence**
   - Always check if an item exists before updating it
   - Use `getDoc()` to verify item existence before `updateDoc()`

3. **Handle Errors Gracefully**
   - Wrap Firebase operations in try/catch blocks
   - Log errors for debugging
   - Provide user-friendly error messages

4. **Test Permissions**
   - Use the Firebase Test Screen to verify permissions
   - Test all operations (read, write, update, delete) for each collection

By following these practices, you can avoid similar issues in the future.
