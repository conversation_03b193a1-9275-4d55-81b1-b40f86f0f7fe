const functions = require('firebase-functions');

/**
 * HTTPS Callable Function to create a Razorpay order.
 * Expects 'amount', 'currency', and optionally 'receipt' and 'notes' in the data.
 */
exports.createRazorpayOrder = functions.https.onCall(async (data, context) => {
  // Authenticate the user
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated to create a Razorpay order'
    );
  }

  const { amount, currency = 'INR', receipt, notes } = data;

  // Validate inputs
  if (typeof amount !== 'number' || amount <= 0) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'The function must be called with a valid amount (number greater than 0)'
    );
  }

  // Load Razorpay credentials from functions config
  const config = functions.config().razorpay;
  if (!config || !config.key_id || !config.key_secret) {
    throw new functions.https.HttpsError(
      'failed-precondition',
      'Razorpay credentials are not configured on the server'
    );
  }

  try {
    // Import Razorpay here to avoid loading issues
    const Razorpay = require('razorpay');
    
    // Initialize Razorpay instance
    const razorpay = new Razorpay({
      key_id: config.key_id,
      key_secret: config.key_secret
    });

    // Create order options
    const options = {
      amount: Math.round(amount),          // amount in paise
      currency: currency,                  // e.g., 'INR'
      receipt: receipt || `receipt_${Date.now()}`,
      payment_capture: 1,                  // auto-capture payment
      ...(notes && { notes })              // optional notes
    };

    // Create order on Razorpay server
    const order = await razorpay.orders.create(options);

    // Return the Razorpay order ID to the client
    return { orderId: order.id };
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Unable to create Razorpay order: ' + error.message
    );
  }
});
