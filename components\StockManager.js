import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    Alert,
    ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { updateStock } from '../utils/stockUtils';

/**
 * Stock Manager Component
 * Allows sellers to update stock quantities for their items
 */
const StockManager = ({
    itemId,
    currentStock = 0,
    onStockUpdate,
    disabled = false,
    style
}) => {
    const [stock, setStock] = useState(currentStock.toString());
    const [isUpdating, setIsUpdating] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);

    const handleStockChange = (value) => {
        // Only allow numeric input
        const numericValue = value.replace(/[^0-9]/g, '');
        setStock(numericValue);
        setHasChanges(numericValue !== currentStock.toString());
    };

    const handleUpdateStock = async () => {
        const newStock = parseInt(stock, 10);

        if (isNaN(newStock) || newStock < 0) {
            Alert.alert('Invalid Stock', 'Please enter a valid stock quantity (0 or greater)');
            return;
        }

        if (newStock === currentStock) {
            setHasChanges(false);
            return;
        }

        setIsUpdating(true);

        try {
            const result = await updateStock(itemId, newStock);

            if (result.success) {
                Alert.alert('Success', 'Stock updated successfully');
                setHasChanges(false);

                // Notify parent component of stock update
                if (onStockUpdate) {
                    onStockUpdate(newStock);
                }
            } else {
                Alert.alert('Error', result.message);
                // Reset to current stock on error
                setStock(currentStock.toString());
                setHasChanges(false);
            }
        } catch (error) {
            console.error('Error updating stock:', error);
            Alert.alert('Error', 'Failed to update stock. Please try again.');
            setStock(currentStock.toString());
            setHasChanges(false);
        } finally {
            setIsUpdating(false);
        }
    };

    const handleQuickAdjust = (adjustment) => {
        const currentValue = parseInt(stock, 10) || 0;
        const newValue = Math.max(0, currentValue + adjustment);
        setStock(newValue.toString());
        setHasChanges(newValue !== currentStock);
    };

    const getStockStatusColor = () => {
        const stockNum = parseInt(stock, 10) || 0;
        if (stockNum === 0) return '#FF3B30';
        if (stockNum <= 5) return '#FF9500';
        return '#34C759';
    };

    const getStockStatusText = () => {
        const stockNum = parseInt(stock, 10) || 0;
        if (stockNum === 0) return 'Out of Stock';
        if (stockNum <= 5) return 'Low Stock';
        return 'In Stock';
    };

    return (
        <View style={[styles.container, style]}>
            <View style={styles.header}>
                <Text style={styles.title}>Stock Management</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStockStatusColor() }]}>
                    <Text style={styles.statusText}>{getStockStatusText()}</Text>
                </View>
            </View>

            <View style={styles.stockControls}>
                <View style={styles.inputContainer}>
                    <Text style={styles.label}>Current Stock:</Text>
                    <View style={styles.stockInputRow}>
                        <TouchableOpacity
                            style={[styles.adjustButton, disabled && styles.disabledButton]}
                            onPress={() => handleQuickAdjust(-1)}
                            disabled={disabled || isUpdating}
                        >
                            <Ionicons name="remove" size={20} color={disabled ? '#ccc' : '#666'} />
                        </TouchableOpacity>

                        <TextInput
                            style={[styles.stockInput, disabled && styles.disabledInput]}
                            value={stock}
                            onChangeText={handleStockChange}
                            keyboardType="numeric"
                            placeholder="0"
                            editable={!disabled && !isUpdating}
                            maxLength={4}
                        />

                        <TouchableOpacity
                            style={[styles.adjustButton, disabled && styles.disabledButton]}
                            onPress={() => handleQuickAdjust(1)}
                            disabled={disabled || isUpdating}
                        >
                            <Ionicons name="add" size={20} color={disabled ? '#ccc' : '#666'} />
                        </TouchableOpacity>
                    </View>
                </View>

                {hasChanges && (
                    <TouchableOpacity
                        style={[styles.updateButton, isUpdating && styles.updatingButton]}
                        onPress={handleUpdateStock}
                        disabled={isUpdating || disabled}
                    >
                        {isUpdating ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <>
                                <Ionicons name="checkmark" size={16} color="#fff" />
                                <Text style={styles.updateButtonText}>Update Stock</Text>
                            </>
                        )}
                    </TouchableOpacity>
                )}
            </View>

            <View style={styles.quickActions}>
                <Text style={styles.quickActionsLabel}>Quick Actions:</Text>
                <View style={styles.quickButtonsRow}>
                    <TouchableOpacity
                        style={[styles.quickButton, disabled && styles.disabledButton]}
                        onPress={() => handleQuickAdjust(5)}
                        disabled={disabled || isUpdating}
                    >
                        <Text style={[styles.quickButtonText, disabled && styles.disabledText]}>+5</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.quickButton, disabled && styles.disabledButton]}
                        onPress={() => handleQuickAdjust(10)}
                        disabled={disabled || isUpdating}
                    >
                        <Text style={[styles.quickButtonText, disabled && styles.disabledText]}>+10</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.quickButton, disabled && styles.disabledButton]}
                        onPress={() => {
                            setStock('0');
                            setHasChanges(currentStock !== 0);
                        }}
                        disabled={disabled || isUpdating}
                    >
                        <Text style={[styles.quickButtonText, disabled && styles.disabledText]}>Set to 0</Text>
                    </TouchableOpacity>
                </View>
            </View>

            {parseInt(stock, 10) <= 5 && parseInt(stock, 10) > 0 && (
                <View style={styles.warningContainer}>
                    <Ionicons name="warning" size={16} color="#FF9500" />
                    <Text style={styles.warningText}>
                        Low stock! Consider restocking soon.
                    </Text>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginVertical: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        borderWidth: 1,
        borderColor: 'rgba(0,0,0,0.08)',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    statusBadge: {
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    stockControls: {
        marginBottom: 16,
    },
    inputContainer: {
        marginBottom: 12,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666',
        marginBottom: 8,
    },
    stockInputRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    adjustButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#f0f0f0',
        justifyContent: 'center',
        alignItems: 'center',
        marginHorizontal: 8,
    },
    disabledButton: {
        backgroundColor: '#f8f8f8',
    },
    stockInput: {
        width: 80,
        height: 40,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        textAlign: 'center',
        fontSize: 16,
        fontWeight: '600',
        backgroundColor: '#fff',
    },
    disabledInput: {
        backgroundColor: '#f8f8f8',
        color: '#999',
    },
    updateButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#4CAF50',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 8,
        marginTop: 8,
    },
    updatingButton: {
        backgroundColor: '#999',
    },
    updateButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 4,
    },
    quickActions: {
        marginBottom: 12,
    },
    quickActionsLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666',
        marginBottom: 8,
    },
    quickButtonsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    quickButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        backgroundColor: '#f0f0f0',
        borderRadius: 6,
        minWidth: 60,
        alignItems: 'center',
    },
    quickButtonText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
    },
    disabledText: {
        color: '#999',
    },
    warningContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF3CD',
        padding: 12,
        borderRadius: 8,
        borderLeftWidth: 4,
        borderLeftColor: '#FF9500',
    },
    warningText: {
        fontSize: 14,
        color: '#856404',
        marginLeft: 8,
        flex: 1,
    },
});

export default StockManager;