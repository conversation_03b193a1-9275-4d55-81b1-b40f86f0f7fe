import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  getCountFromServer,
  Timestamp
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';

const { width } = Dimensions.get('window');

const AdminAnalytics = ({ navigation }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [analytics, setAnalytics] = useState({
    overview: {
      totalUsers: 0,
      totalSellers: 0,
      totalBuyers: 0,
      totalListings: 0,
      totalOrders: 0,
      totalRevenue: 0
    },
    growth: {
      newUsersToday: 0,
      newUsersThisWeek: 0,
      newUsersThisMonth: 0,
      newListingsToday: 0,
      newListingsThisWeek: 0,
      newListingsThisMonth: 0
    },
    engagement: {
      activeUsers: 0,
      averageSessionTime: 0,
      topCategories: [],
      popularItems: []
    },
    revenue: {
      todayRevenue: 0,
      weekRevenue: 0,
      monthRevenue: 0,
      averageOrderValue: 0
    }
  });

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchAnalytics();
    }
  }, [isAdmin]);

  const checkAdminAccess = async () => {
    try {
      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        Alert.alert(
          'Access Denied',
          'You do not have administrator privileges.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    try {
      setLoading(true);

      // Get date ranges
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Fetch overview data
      const [
        totalUsersSnapshot,
        totalSellersSnapshot,
        totalBuyersSnapshot,
        totalListingsSnapshot,
        totalOrdersSnapshot
      ] = await Promise.all([
        getCountFromServer(collection(db, 'users')),
        getCountFromServer(query(collection(db, 'users'), where('isSeller', '==', true))),
        getCountFromServer(query(collection(db, 'users'), where('isSeller', '==', false))),
        getCountFromServer(collection(db, 'items')),
        getCountFromServer(collection(db, 'orders'))
      ]);

      // Fetch growth data
      const [
        newUsersTodaySnapshot,
        newUsersWeekSnapshot,
        newUsersMonthSnapshot,
        newListingsTodaySnapshot,
        newListingsWeekSnapshot,
        newListingsMonthSnapshot
      ] = await Promise.all([
        getCountFromServer(query(collection(db, 'users'), where('createdAt', '>=', Timestamp.fromDate(today)))),
        getCountFromServer(query(collection(db, 'users'), where('createdAt', '>=', Timestamp.fromDate(weekAgo)))),
        getCountFromServer(query(collection(db, 'users'), where('createdAt', '>=', Timestamp.fromDate(monthAgo)))),
        getCountFromServer(query(collection(db, 'items'), where('createdAt', '>=', Timestamp.fromDate(today)))),
        getCountFromServer(query(collection(db, 'items'), where('createdAt', '>=', Timestamp.fromDate(weekAgo)))),
        getCountFromServer(query(collection(db, 'items'), where('createdAt', '>=', Timestamp.fromDate(monthAgo))))
      ]);

      // Fetch revenue data
      const ordersSnapshot = await getDocs(collection(db, 'orders'));
      let totalRevenue = 0;
      let todayRevenue = 0;
      let weekRevenue = 0;
      let monthRevenue = 0;

      ordersSnapshot.docs.forEach(doc => {
        const order = doc.data();
        const orderDate = order.createdAt?.toDate() || new Date();
        const amount = order.totalAmount || 0;

        totalRevenue += amount;

        if (orderDate >= today) {
          todayRevenue += amount;
        }
        if (orderDate >= weekAgo) {
          weekRevenue += amount;
        }
        if (orderDate >= monthAgo) {
          monthRevenue += amount;
        }
      });

      // Fetch top categories
      const itemsSnapshot = await getDocs(collection(db, 'items'));
      const categoryCount = {};

      itemsSnapshot.docs.forEach(doc => {
        const item = doc.data();
        const category = item.category || 'Uncategorized';
        categoryCount[category] = (categoryCount[category] || 0) + 1;
      });

      const topCategories = Object.entries(categoryCount)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([category, count]) => ({ category, count }));

      // Calculate averages
      const averageOrderValue = totalOrdersSnapshot.data().count > 0
        ? totalRevenue / totalOrdersSnapshot.data().count
        : 0;

      setAnalytics({
        overview: {
          totalUsers: totalUsersSnapshot.data().count,
          totalSellers: totalSellersSnapshot.data().count,
          totalBuyers: totalBuyersSnapshot.data().count,
          totalListings: totalListingsSnapshot.data().count,
          totalOrders: totalOrdersSnapshot.data().count,
          totalRevenue
        },
        growth: {
          newUsersToday: newUsersTodaySnapshot.data().count,
          newUsersThisWeek: newUsersWeekSnapshot.data().count,
          newUsersThisMonth: newUsersMonthSnapshot.data().count,
          newListingsToday: newListingsTodaySnapshot.data().count,
          newListingsThisWeek: newListingsWeekSnapshot.data().count,
          newListingsThisMonth: newListingsMonthSnapshot.data().count
        },
        engagement: {
          topCategories
        },
        revenue: {
          todayRevenue,
          weekRevenue,
          monthRevenue,
          averageOrderValue
        }
      });

    } catch (error) {
      console.error('Error fetching analytics:', error);
      Alert.alert('Error', 'Failed to load analytics data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchAnalytics();
  };

  const renderMetricCard = (title, value, icon, color, subtitle = '') => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <Ionicons name={icon} size={24} color={color} />
      <View style={styles.metricContent}>
        <Text style={styles.metricValue}>{value}</Text>
        <Text style={styles.metricTitle}>{title}</Text>
        {subtitle ? <Text style={styles.metricSubtitle}>{subtitle}</Text> : null}
      </View>
    </View>
  );

  const renderCategoryItem = (item, index) => (
    <View key={index} style={styles.categoryItem}>
      <Text style={styles.categoryName}>{item.category}</Text>
      <Text style={styles.categoryCount}>{item.count} items</Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have administrator privileges.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Analytics Dashboard</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >

        {/* Overview Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Platform Overview</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              'Total Users',
              analytics.overview.totalUsers.toLocaleString(),
              'people-outline',
              '#4ECDC4'
            )}
            {renderMetricCard(
              'Sellers',
              analytics.overview.totalSellers.toLocaleString(),
              'storefront-outline',
              '#FF6B6B'
            )}
            {renderMetricCard(
              'Buyers',
              analytics.overview.totalBuyers.toLocaleString(),
              'person-outline',
              '#45B7D1'
            )}
            {renderMetricCard(
              'Total Listings',
              analytics.overview.totalListings.toLocaleString(),
              'shirt-outline',
              '#96CEB4'
            )}
            {renderMetricCard(
              'Total Orders',
              analytics.overview.totalOrders.toLocaleString(),
              'receipt-outline',
              '#FECA57'
            )}
            {renderMetricCard(
              'Total Revenue',
              `₹${analytics.overview.totalRevenue.toLocaleString()}`,
              'cash-outline',
              '#FF9FF3'
            )}
          </View>
        </View>

        {/* Growth Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Growth Metrics</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              'New Users Today',
              analytics.growth.newUsersToday.toLocaleString(),
              'person-add-outline',
              '#4ECDC4',
              `${analytics.growth.newUsersThisWeek} this week`
            )}
            {renderMetricCard(
              'New Users This Month',
              analytics.growth.newUsersThisMonth.toLocaleString(),
              'trending-up-outline',
              '#45B7D1'
            )}
            {renderMetricCard(
              'New Listings Today',
              analytics.growth.newListingsToday.toLocaleString(),
              'add-circle-outline',
              '#96CEB4',
              `${analytics.growth.newListingsThisWeek} this week`
            )}
            {renderMetricCard(
              'New Listings This Month',
              analytics.growth.newListingsThisMonth.toLocaleString(),
              'bar-chart-outline',
              '#FECA57'
            )}
          </View>
        </View>

        {/* Revenue Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Revenue Analytics</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              'Today\'s Revenue',
              `₹${analytics.revenue.todayRevenue.toLocaleString()}`,
              'today-outline',
              '#FF6B6B'
            )}
            {renderMetricCard(
              'This Week',
              `₹${analytics.revenue.weekRevenue.toLocaleString()}`,
              'calendar-outline',
              '#4ECDC4'
            )}
            {renderMetricCard(
              'This Month',
              `₹${analytics.revenue.monthRevenue.toLocaleString()}`,
              'stats-chart-outline',
              '#45B7D1'
            )}
            {renderMetricCard(
              'Avg Order Value',
              `₹${Math.round(analytics.revenue.averageOrderValue).toLocaleString()}`,
              'calculator-outline',
              '#96CEB4'
            )}
          </View>
        </View>

        {/* Top Categories Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Top Categories</Text>
          <View style={styles.categoriesContainer}>
            {analytics.engagement.topCategories.map(renderCategoryItem)}
          </View>
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  metricsGrid: {
    paddingHorizontal: 20,
  },
  metricCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricContent: {
    marginLeft: 16,
    flex: 1,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  metricTitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  categoriesContainer: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  categoryCount: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default AdminAnalytics;
