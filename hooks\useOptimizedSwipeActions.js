import { useCallback } from 'react';
import {
  collection,
  addDoc,
  doc,
  updateDoc,
  increment,
  arrayUnion,
  arrayRemove,
  setDoc,
  deleteDoc,
  getDoc,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import { db } from '../firebase.config';
import { trackSwipeGesture, trackCartAddition, startTiming, endTiming } from '../utils/performanceMonitor';

/**
 * Optimized swipe actions hook that provides immediate UI feedback
 * and performs database operations in the background for better performance
 */
export const useOptimizedSwipeActions = ({
  currentUserId,
  setInteractedItemIds,
  addToCart,
  triggerLikeAnimation,
  triggerDislikeAnimation
}) => {

  // Optimized process swipe completion with immediate feedback and performance tracking
  const processSwipeCompletion = useCallback(async (direction, swipedItem) => {
    const swipeTracker = trackSwipeGesture(direction, swipedItem?.id);

    try {
      if (!swipedItem) {
        console.log("[useOptimizedSwipeActions] No item to process for swipe completion");
        return;
      }

      // Check authentication first
      if (!currentUserId) {
        console.log("[useOptimizedSwipeActions] No authenticated user, skipping Firestore operations");
        // Still update local state
        setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
        swipeTracker?.endTracking();
        return;
      }

      console.log(`[useOptimizedSwipeActions] Processing ${direction} swipe for item ${swipedItem.id}`);

      // Track local state update performance
      startTiming(`local_state_update_${swipedItem.id}`);
      setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
      endTiming(`local_state_update_${swipedItem.id}`);

      // Handle different swipe directions with immediate feedback
      if (direction === 'up') {
        // Cart swipe - immediate feedback and optimistic updates
        handleCartSwipe(swipedItem);
      } else if (direction === 'right') {
        // Like swipe - immediate feedback
        startTiming(`like_animation_trigger_${swipedItem.id}`);
        if (triggerLikeAnimation) {
          triggerLikeAnimation();
        }
        endTiming(`like_animation_trigger_${swipedItem.id}`);
        handleLikeSwipe(swipedItem);
      } else if (direction === 'left') {
        // Dislike swipe - immediate feedback
        startTiming(`dislike_animation_trigger_${swipedItem.id}`);
        if (triggerDislikeAnimation) {
          triggerDislikeAnimation();
        }
        endTiming(`dislike_animation_trigger_${swipedItem.id}`);
        handleDislikeSwipe(swipedItem);
      }

      swipeTracker?.endTracking();

    } catch (error) {
      console.error('[useOptimizedSwipeActions] Error in processSwipeCompletion:', error);
      swipeTracker?.endTracking();
    }
  }, [currentUserId, setInteractedItemIds, addToCart, triggerLikeAnimation, triggerDislikeAnimation]);

  // Handle cart swipe with immediate feedback and performance tracking
  const handleCartSwipe = useCallback((swipedItem) => {
    console.log(`[useOptimizedSwipeActions] Processing cart swipe for item ${swipedItem.id}`);

    const cartTracker = trackCartAddition(swipedItem.id);

    // Track cart animation trigger performance
    startTiming(`cart_animation_trigger_${swipedItem.id}`);

    // Immediate cart addition with optimistic update
    if (addToCart) {
      addToCart(swipedItem);
    }

    endTiming(`cart_animation_trigger_${swipedItem.id}`);
    cartTracker?.markAnimationTriggered();

    // Background database operations (non-blocking)
    performCartBackgroundOperations(swipedItem).then(() => {
      cartTracker?.markDatabaseComplete();
    });
  }, [currentUserId, addToCart]);

  // Handle like swipe with immediate feedback
  const handleLikeSwipe = useCallback((swipedItem) => {
    console.log(`[useOptimizedSwipeActions] Processing like swipe for item ${swipedItem.id}`);

    // Background database operations (non-blocking)
    performLikeBackgroundOperations(swipedItem);
  }, [currentUserId]);

  // Handle dislike swipe with immediate feedback
  const handleDislikeSwipe = useCallback((swipedItem) => {
    console.log(`[useOptimizedSwipeActions] Processing dislike swipe for item ${swipedItem.id}`);

    // Background database operations (non-blocking)
    performDislikeBackgroundOperations(swipedItem);
  }, [currentUserId]);

  // Background operations for cart swipe
  const performCartBackgroundOperations = useCallback((swipedItem) => {
    return Promise.allSettled([
      // Store interaction
      storeInteraction(swipedItem.id, 'cart'),
      // Like the item (cart swipe also likes)
      performLikeOperation(swipedItem.id)
    ]).then((results) => {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[useOptimizedSwipeActions] Cart background operation ${index + 1} failed:`, result.reason);
        }
      });
      return results;
    });
  }, [currentUserId]);

  // Background operations for like swipe
  const performLikeBackgroundOperations = useCallback((swipedItem) => {
    Promise.allSettled([
      // Store interaction
      storeInteraction(swipedItem.id, 'like'),
      // Perform like operation
      performLikeOperation(swipedItem.id),
      // Update view count
      updateViewCount(swipedItem.id)
    ]).then((results) => {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[useOptimizedSwipeActions] Like background operation ${index + 1} failed:`, result.reason);
        }
      });
    });
  }, [currentUserId]);

  // Background operations for dislike swipe
  const performDislikeBackgroundOperations = useCallback((swipedItem) => {
    Promise.allSettled([
      // Store interaction
      storeInteraction(swipedItem.id, 'dislike'),
      // Perform dislike operation
      performDislikeOperation(swipedItem.id),
      // Update view count
      updateViewCount(swipedItem.id)
    ]).then((results) => {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[useOptimizedSwipeActions] Dislike background operation ${index + 1} failed:`, result.reason);
        }
      });
    });
  }, [currentUserId]);

  // Helper function to store interaction
  const storeInteraction = useCallback(async (itemId, interactionType) => {
    try {
      const interactedQuery = query(
        collection(db, 'users', currentUserId, 'interactedItems'),
        where('itemId', '==', itemId)
      );
      const interactedSnapshot = await getDocs(interactedQuery);

      if (interactedSnapshot.empty) {
        await addDoc(collection(db, 'users', currentUserId, 'interactedItems'), {
          itemId,
          interactedAt: new Date(),
          interactionType
        });
        console.log(`[useOptimizedSwipeActions] Stored interaction: ${interactionType} for item ${itemId}`);
      }
    } catch (error) {
      console.error('[useOptimizedSwipeActions] Error storing interaction:', error);
      throw error;
    }
  }, [currentUserId]);

  // Helper function to perform like operation
  const performLikeOperation = useCallback(async (itemId) => {
    try {
      const itemRef = doc(db, 'clothingItems', itemId);
      const itemDoc = await getDoc(itemRef);

      if (!itemDoc.exists()) {
        console.log(`[useOptimizedSwipeActions] Item ${itemId} does not exist, skipping like operation`);
        return;
      }

      const userLikesRef = doc(db, 'users', currentUserId, 'likes', itemId);
      const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', itemId);

      // Update item and user likes in parallel
      await Promise.all([
        updateDoc(itemRef, {
          likedBy: arrayUnion(currentUserId),
          dislikedBy: arrayRemove(currentUserId),
          likeCount: increment(1)
        }),
        setDoc(userLikesRef, { itemId, likedAt: new Date() })
      ]);

      // Clean up dislikes (non-critical)
      try {
        await deleteDoc(userDislikesRef);
      } catch (e) {
        // Ignore if doesn't exist
      }

      console.log(`[useOptimizedSwipeActions] Like operation completed for item ${itemId}`);
    } catch (error) {
      console.error('[useOptimizedSwipeActions] Error in like operation:', error);
      throw error;
    }
  }, [currentUserId]);

  // Helper function to perform dislike operation
  const performDislikeOperation = useCallback(async (itemId) => {
    try {
      const itemRef = doc(db, 'clothingItems', itemId);
      const itemDoc = await getDoc(itemRef);

      if (!itemDoc.exists()) {
        console.log(`[useOptimizedSwipeActions] Item ${itemId} does not exist, skipping dislike operation`);
        return;
      }

      const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', itemId);
      const userLikesRef = doc(db, 'users', currentUserId, 'likes', itemId);

      // Update item and user dislikes in parallel
      await Promise.all([
        updateDoc(itemRef, {
          dislikedBy: arrayUnion(currentUserId),
          likedBy: arrayRemove(currentUserId),
          dislikeCount: increment(1)
        }),
        setDoc(userDislikesRef, { itemId, dislikedAt: new Date() })
      ]);

      // Clean up likes (non-critical)
      try {
        await deleteDoc(userLikesRef);
      } catch (e) {
        // Ignore if doesn't exist
      }

      console.log(`[useOptimizedSwipeActions] Dislike operation completed for item ${itemId}`);
    } catch (error) {
      console.error('[useOptimizedSwipeActions] Error in dislike operation:', error);
      throw error;
    }
  }, [currentUserId]);

  // Helper function to update view count
  const updateViewCount = useCallback(async (itemId) => {
    try {
      const itemRef = doc(db, 'clothingItems', itemId);
      await updateDoc(itemRef, {
        viewCount: increment(1)
      });
      console.log(`[useOptimizedSwipeActions] View count updated for item ${itemId}`);
    } catch (error) {
      console.error('[useOptimizedSwipeActions] Error updating view count:', error);
      throw error;
    }
  }, []);

  return {
    processSwipeCompletion
  };
};
