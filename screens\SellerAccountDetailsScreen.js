import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Alert,
    ActivityIndicator,
    ScrollView,
    Platform,
    SafeAreaView,
    KeyboardAvoidingView,
    StatusBar // Added StatusBar
} from 'react-native';
import { auth, db } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import { doc, updateDoc, getDoc, serverTimestamp } from 'firebase/firestore';

const SellerAccountDetailsScreen = ({ navigation, route }) => {
    const [accountDetails, setAccountDetails] = useState({
        accountHolderName: '',
        accountNumber: '',
        ifscCode: '',
        bankName: '',
        branchName: ''
    });
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);

    const user = auth.currentUser;

    useEffect(() => {
        fetchAccountDetails();
    }, []);

    const fetchAccountDetails = async () => {
        try {
            if (!user) {
                Alert.alert('Error', 'User not found. Please log in again.');
                navigation.goBack();
                return;
            }

            const userDocRef = doc(db, 'users', user.uid);
            const userDoc = await getDoc(userDocRef);

            if (userDoc.exists()) {
                const userData = userDoc.data();
                if (userData.bankAccount) {
                    setAccountDetails({
                        accountHolderName: userData.bankAccount.accountHolderName || '',
                        accountNumber: userData.bankAccount.accountNumber || '',
                        ifscCode: userData.bankAccount.ifscCode || '',
                        bankName: userData.bankAccount.bankName || '',
                        branchName: userData.bankAccount.branchName || ''
                    });
                }
            }
        } catch (error) {
            console.error('Error fetching account details:', error);
            Alert.alert('Error', 'Failed to load account details.');
        } finally {
            setInitialLoading(false);
        }
    };

    const validateInputs = () => {
        if (!accountDetails.accountHolderName.trim()) {
            Alert.alert('Error', 'Please enter account holder name');
            return false;
        }
        if (!accountDetails.accountNumber.trim()) {
            Alert.alert('Error', 'Please enter account number');
            return false;
        }
        if (!accountDetails.ifscCode.trim()) {
            Alert.alert('Error', 'Please enter IFSC code');
            return false;
        }
        if (!accountDetails.bankName.trim()) {
            Alert.alert('Error', 'Please enter bank name');
            return false;
        }

        // Validate account number (basic validation)
        const accountNumberRegex = /^[0-9]{9,18}$/;
        if (!accountNumberRegex.test(accountDetails.accountNumber.trim())) {
            Alert.alert('Error', 'Please enter a valid account number (9-18 digits)');
            return false;
        }

        // Validate IFSC code
        const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
        if (!ifscRegex.test(accountDetails.ifscCode.trim().toUpperCase())) {
            Alert.alert('Error', 'Please enter a valid IFSC code (e.g., ICIC0000001)');
            return false;
        }

        return true;
    };

    const handleSave = async () => {
        if (!validateInputs()) return;

        setLoading(true);
        try {
            if (!user) {
                Alert.alert('Error', 'User not found. Please log in again.');
                navigation.goBack();
                return;
            }

            // Update user document with bank account details
            const userDocRef = doc(db, 'users', user.uid);
            await updateDoc(userDocRef, {
                bankAccount: {
                    accountHolderName: accountDetails.accountHolderName.trim(),
                    accountNumber: accountDetails.accountNumber.trim(),
                    ifscCode: accountDetails.ifscCode.trim().toUpperCase(),
                    bankName: accountDetails.bankName.trim(),
                    branchName: accountDetails.branchName.trim(),
                    updatedAt: serverTimestamp()
                },
                updatedAt: serverTimestamp()
            });

            Alert.alert('Success', 'Bank account details saved successfully!', [
                { text: 'OK', onPress: () => navigation.goBack() }
            ]);
        } catch (error) {
            console.error('Error saving account details:', error);
            Alert.alert('Error', 'Failed to save account details. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async () => {
        Alert.alert(
            'Delete Account Details',
            'Are you sure you want to delete your bank account details? This action cannot be undone.',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: async () => {
                        setLoading(true);
                        try {
                            const userDocRef = doc(db, 'users', user.uid);
                            await updateDoc(userDocRef, {
                                bankAccount: null,
                                updatedAt: serverTimestamp()
                            });

                            // Clear the form
                            setAccountDetails({
                                accountHolderName: '',
                                accountNumber: '',
                                ifscCode: '',
                                bankName: '',
                                branchName: ''
                            });

                            Alert.alert('Success', 'Bank account details deleted successfully!');
                        } catch (error) {
                            console.error('Error deleting account details:', error);
                            Alert.alert('Error', 'Failed to delete account details. Please try again.');
                        } finally {
                            setLoading(false);
                        }
                    }
                }
            ]
        );
    };

    const hasAccountDetails = () => {
        return accountDetails.accountHolderName || accountDetails.accountNumber ||
            accountDetails.ifscCode || accountDetails.bankName || accountDetails.branchName;
    };

    if (initialLoading) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#FF6B6B" />
                    <Text style={styles.loadingText}>Loading account details...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
            >
                <ScrollView
                    contentContainerStyle={styles.scrollContainer}
                    showsVerticalScrollIndicator={true}
                >
                    <View style={styles.content}>
                        <View style={styles.header}>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                            >
                                <Ionicons name="arrow-back" size={24} color="#333" />
                            </TouchableOpacity>
                            <Text style={styles.title}>Bank Account Details</Text>
                            <View style={{ width: 24 }} />
                        </View>

                        <Text style={styles.subtitle}>
                            Add your bank account details for receiving payments from your sales.
                            This information is securely stored and only visible to you and admin.
                        </Text>

                        <View style={styles.section}>
                            <Text style={styles.sectionTitle}>Account Information</Text>

                            <TextInput
                                style={styles.input}
                                placeholder="Account Holder Name"
                                value={accountDetails.accountHolderName}
                                onChangeText={(text) => setAccountDetails({ ...accountDetails, accountHolderName: text })}
                                editable={!loading}
                                placeholderTextColor="#000" // Changed to black
                                autoCapitalize="words"
                            />

                            <TextInput
                                style={styles.input}
                                placeholder="Account Number"
                                value={accountDetails.accountNumber}
                                onChangeText={(text) => setAccountDetails({ ...accountDetails, accountNumber: text })}
                                keyboardType="numeric"
                                editable={!loading}
                                placeholderTextColor="#000" // Changed to black
                                maxLength={18}
                            />

                            <TextInput
                                style={styles.input}
                                placeholder="IFSC Code (e.g., ICIC0000001)"
                                value={accountDetails.ifscCode}
                                onChangeText={(text) => setAccountDetails({ ...accountDetails, ifscCode: text.toUpperCase() })}
                                editable={!loading}
                                placeholderTextColor="#000" // Changed to black
                                autoCapitalize="characters"
                                maxLength={11}
                            />

                            <TextInput
                                style={styles.input}
                                placeholder="Bank Name"
                                value={accountDetails.bankName}
                                onChangeText={(text) => setAccountDetails({ ...accountDetails, bankName: text })}
                                editable={!loading}
                                placeholderTextColor="#000" // Changed to black
                                autoCapitalize="words"
                            />

                            <TextInput
                                style={styles.input}
                                placeholder="Branch Name (Optional)"
                                value={accountDetails.branchName}
                                onChangeText={(text) => setAccountDetails({ ...accountDetails, branchName: text })}
                                editable={!loading}
                                placeholderTextColor="#000" // Changed to black
                                autoCapitalize="words"
                            />
                        </View>

                        <View style={styles.buttonContainer}>
                            {loading ? (
                                <ActivityIndicator size="large" color="#FF6B6B" style={{ marginTop: 20 }} />
                            ) : (
                                <>
                                    <TouchableOpacity
                                        style={styles.saveButton}
                                        onPress={handleSave}
                                        disabled={loading}
                                    >
                                        <Ionicons name="save-outline" size={20} color="#fff" style={{ marginRight: 8 }} />
                                        <Text style={styles.saveButtonText}>Save Account Details</Text>
                                    </TouchableOpacity>

                                    {hasAccountDetails() && (
                                        <TouchableOpacity
                                            style={styles.deleteButton}
                                            onPress={handleDelete}
                                            disabled={loading}
                                        >
                                            <Ionicons name="trash-outline" size={20} color="#fff" style={{ marginRight: 8 }} />
                                            <Text style={styles.deleteButtonText}>Delete Account Details</Text>
                                        </TouchableOpacity>
                                    )}
                                </>
                            )}
                        </View>

                        <View style={styles.infoContainer}>
                            <View style={styles.infoItem}>
                                <Ionicons name="shield-checkmark-outline" size={24} color="#4CAF50" />
                                <Text style={styles.infoText}>Your bank details are encrypted and secure</Text>
                            </View>
                            <View style={styles.infoItem}>
                                <Ionicons name="eye-off-outline" size={24} color="#4CAF50" />
                                <Text style={styles.infoText}>Only you and SwipeSense admin can view these details</Text>
                            </View>
                            <View style={styles.infoItem}>
                                <Ionicons name="cash-outline" size={24} color="#4CAF50" />
                                <Text style={styles.infoText}>Required for receiving payments from your sales</Text>
                            </View>
                        </View>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f2f5',
        paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0, // Added status bar padding for Android
    },
    scrollContainer: {
        flexGrow: 1,
        paddingBottom: 40, // Increased padding
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 20, // Standardized top padding
        paddingBottom: 20,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#666',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 24,
        justifyContent: 'space-between',
        // Removed platform-specific paddingTop: Platform.OS === 'android' ? 10 : 0,
    },
    backButton: {
        padding: 8, // Increased padding for better touch area
        borderRadius: 20, // Make it circular
        backgroundColor: '#fff', // Added background
        elevation: 2, // Added shadow for Android
        shadowColor: '#000', // Shadow for iOS
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    title: {
        fontSize: 22, // Slightly reduced size
        fontWeight: '600', // Adjusted weight
        color: '#1C1C1E', // Darker color
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 10, // Add some margin
    },
    subtitle: {
        fontSize: 15, // Slightly reduced size
        color: '#555', // Adjusted color
        marginBottom: 32, // Increased margin
        textAlign: 'center',
        lineHeight: 22,
        paddingHorizontal: 10, // Add horizontal padding for better wrapping
    },
    section: {
        marginBottom: 24, // Reduced margin
        backgroundColor: '#fff', // Add background to section
        borderRadius: 12,
        padding: 20,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600', // Adjusted weight
        color: '#1C1C1E',
        marginBottom: 20, // Increased margin
    },
    input: {
        width: '100%',
        height: 52, // Increased height
        borderWidth: 1,
        borderColor: '#E0E0E0', // Lighter border
        borderRadius: 10, // Smoother radius
        marginBottom: 18, // Increased margin
        paddingHorizontal: 16,
        fontSize: 16,
        backgroundColor: '#fff', // Ensure background is white
        color: '#333',
    },
    buttonContainer: {
        marginTop: 10, // Add some top margin
        marginBottom: 20, // Reduced margin
    },
    saveButton: {
        width: '100%',
        height: 52, // Increased height
        backgroundColor: '#FF6B6B',
        borderRadius: 10, // Smoother radius
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 15,
        flexDirection: 'row',
    },
    saveButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600', // Adjusted weight
    },
    deleteButton: {
        width: '100%',
        height: 52, // Increased height
        backgroundColor: '#DC3545', // Adjusted color for delete
        borderRadius: 10, // Smoother radius
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    deleteButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600', // Adjusted weight
    },
    infoContainer: {
        backgroundColor: '#E9F5E9', // Lighter green background
        borderRadius: 12,
        padding: 20,
        marginTop: 30, // Increased margin
    },
    infoItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
        '&:last-child': { // Remove margin for the last item
            marginBottom: 0,
        },
    },
    infoText: {
        marginLeft: 12,
        fontSize: 14,
        color: '#38761D', // Darker green text for better contrast
        flex: 1,
        lineHeight: 20,
    },
});

export default SellerAccountDetailsScreen;
