import { db } from '../firebase.config';
import {
  collection,
  doc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  addDoc,
  serverTimestamp,
  getDoc,
  limit
} from 'firebase/firestore';

/**
 * Get all support tickets with optional filters
 * @param {Object} filters - Filter options
 * @returns {Promise<Array>} Array of support tickets
 */
export const getSupportTickets = async (filters = {}) => {
  try {
    let ticketsRef = collection(db, 'supportTickets');
    let q = query(ticketsRef, orderBy('createdAt', 'desc'));

    // Apply filters
    if (filters.status) {
      q = query(ticketsRef, where('status', '==', filters.status), orderBy('createdAt', 'desc'));
    }

    if (filters.category) {
      q = query(ticketsRef, where('category', '==', filters.category), orderBy('createdAt', 'desc'));
    }

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    return [];
  }
};

/**
 * Update support ticket status
 * @param {string} ticketId - Ticket ID
 * @param {string} status - New status
 * @param {string} adminId - Admin user ID
 * @returns {Promise<boolean>} Success status
 */
export const updateTicketStatus = async (ticketId, status, adminId = null) => {
  try {
    const updateData = {
      status,
      updatedAt: serverTimestamp()
    };

    if (adminId) {
      updateData.assignedTo = adminId;
    }

    await updateDoc(doc(db, 'supportTickets', ticketId), updateData);
    return true;
  } catch (error) {
    console.error('Error updating ticket status:', error);
    return false;
  }
};

/**
 * Add response to support ticket
 * @param {string} ticketId - Ticket ID
 * @param {Object} response - Response data
 * @returns {Promise<boolean>} Success status
 */
export const addTicketResponse = async (ticketId, response) => {
  try {
    // Get current ticket data to add response to responses array
    const ticketRef = doc(db, 'supportTickets', ticketId);
    const ticketDoc = await getDocs(query(collection(db, 'supportTickets'), where('__name__', '==', ticketId)));

    if (ticketDoc.empty) {
      throw new Error('Ticket not found');
    }

    const currentTicket = ticketDoc.docs[0].data();
    const currentResponses = currentTicket.responses || [];

    const newResponse = {
      message: response.message,
      responderId: response.responderId,
      responderName: response.responderName,
      responderType: response.isAdminResponse ? 'admin' : 'user',
      timestamp: new Date(),
      attachments: response.attachments || []
    };

    // Add response to the responses array
    const updatedResponses = [...currentResponses, newResponse];

    // Update the ticket with new response and metadata
    await updateDoc(ticketRef, {
      responses: updatedResponses,
      updatedAt: serverTimestamp(),
      lastResponseBy: newResponse.responderType,
      status: response.isAdminResponse && currentTicket.status === 'open' ? 'in-progress' : currentTicket.status
    });

    // Email notifications will be automatically sent via Firebase functions
    console.log('Ticket response added, email notifications will be sent automatically');

    return true;
  } catch (error) {
    console.error('Error adding ticket response:', error);
    return false;
  }
};

/**
 * Get responses for a specific ticket
 * @param {string} ticketId - Ticket ID
 * @returns {Promise<Array>} Array of responses
 */
export const getTicketResponses = async (ticketId) => {
  try {
    const responsesRef = collection(db, 'ticketResponses');
    const q = query(
      responsesRef,
      where('ticketId', '==', ticketId),
      orderBy('timestamp', 'asc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error fetching ticket responses:', error);
    return [];
  }
};

/**
 * Get support tickets by status
 * @param {string} status - Ticket status to filter by
 * @returns {Promise<Array>} Array of support tickets
 */
export const getTicketsByStatus = async (status) => {
  try {
    const ticketsRef = collection(db, 'supportTickets');
    let q;

    if (status === 'all') {
      q = query(ticketsRef, orderBy('createdAt', 'desc'));
    } else {
      q = query(ticketsRef, where('status', '==', status), orderBy('createdAt', 'desc'));
    }

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error fetching tickets by status:', error);
    return [];
  }
};

/**
 * Get support statistics
 * @returns {Promise<Object>} Support stats
 */
export const getSupportStatistics = async () => {
  try {
    const ticketsRef = collection(db, 'supportTickets');
    const allTickets = await getDocs(ticketsRef);

    const stats = {
      total: 0,
      open: 0,
      inProgress: 0,
      resolved: 0,
      closed: 0,
      byCategory: {},
      avgResponseTime: 0,
      todayTickets: 0,
      weekTickets: 0,
      monthTickets: 0
    };

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    allTickets.docs.forEach(doc => {
      const data = doc.data();
      const createdAt = data.createdAt?.toDate() || new Date();

      stats.total++;

      // Count by status
      switch (data.status) {
        case 'open':
          stats.open++;
          break;
        case 'in-progress':
        case 'in_progress':
          stats.inProgress++;
          break;
        case 'resolved':
          stats.resolved++;
          break;
        case 'closed':
          stats.closed++;
          break;
      }

      // Count by time period
      if (createdAt >= today) {
        stats.todayTickets++;
      }
      if (createdAt >= weekAgo) {
        stats.weekTickets++;
      }
      if (createdAt >= monthAgo) {
        stats.monthTickets++;
      }

      // Count by category
      const category = data.category || 'other';
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
    });

    return stats;
  } catch (error) {
    console.error('Error fetching support statistics:', error);
    return {
      total: 0,
      open: 0,
      inProgress: 0,
      resolved: 0,
      closed: 0,
      byCategory: {},
      avgResponseTime: 0,
      todayTickets: 0,
      weekTickets: 0,
      monthTickets: 0
    };
  }
};

/**
 * Get support statistics (alias for backward compatibility)
 * @returns {Promise<Object>} Support stats
 */
export const getSupportStats = getSupportStatistics;

/**
 * Get recent support tickets
 * @param {number} limit - Number of tickets to fetch
 * @returns {Promise<Array>} Array of recent tickets
 */
export const getRecentTickets = async (limit = 10) => {
  try {
    const ticketsRef = collection(db, 'supportTickets');
    const q = query(ticketsRef, orderBy('createdAt', 'desc'), limit(limit));

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error fetching recent tickets:', error);
    return [];
  }
};

/**
 * Get tickets assigned to a specific admin
 * @param {string} adminId - Admin user ID
 * @returns {Promise<Array>} Array of assigned tickets
 */
export const getAssignedTickets = async (adminId) => {
  try {
    const ticketsRef = collection(db, 'supportTickets');
    const q = query(
      ticketsRef,
      where('assignedTo', '==', adminId),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error fetching assigned tickets:', error);
    return [];
  }
};

/**
 * Assign ticket to admin
 * @param {string} ticketId - Ticket ID
 * @param {string} adminId - Admin user ID
 * @returns {Promise<boolean>} Success status
 */
export const assignTicket = async (ticketId, adminId) => {
  try {
    await updateDoc(doc(db, 'supportTickets', ticketId), {
      assignedTo: adminId,
      status: 'in-progress',
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error assigning ticket:', error);
    return false;
  }
};

/**
 * Auto-categorize ticket based on content
 * @param {string} subject - Ticket subject
 * @param {string} message - Ticket message
 * @returns {string} Suggested category
 */
export const categorizeTicket = (subject, message) => {
  const content = (subject + ' ' + message).toLowerCase();

  const categoryKeywords = {
    'account': ['account', 'login', 'password', 'profile', 'signup', 'register'],
    'orders': ['order', 'purchase', 'delivery', 'shipping', 'track', 'cancel'],
    'payments': ['payment', 'charge', 'refund', 'billing', 'card', 'transaction'],
    'technical': ['bug', 'error', 'crash', 'loading', 'slow', 'not working'],
    'seller': ['seller', 'verification', 'listing', 'upload', 'store'],
    'refund': ['refund', 'return', 'exchange', 'money back', 'dispute']
  };

  for (const [category, keywords] of Object.entries(categoryKeywords)) {
    if (keywords.some(keyword => content.includes(keyword))) {
      return category;
    }
  }

  return 'other';
};

/**
 * Get suggested responses based on ticket content
 * @param {string} category - Ticket category
 * @param {string} message - Ticket message
 * @returns {Array} Array of suggested responses
 */
export const getSuggestedResponses = (category, message) => {
  const suggestions = {
    'account': [
      'Thank you for contacting us about your account issue. We\'ll help you resolve this.',
      'For account-related issues, please try resetting your password first.',
      'If you\'re having trouble logging in, please check your email for verification.'
    ],
    'orders': [
      'Thank you for reaching out about your order. Let me check the status for you.',
      'Order issues are our priority. We\'ll investigate and get back to you soon.',
      'For order tracking, please check your Order History in the app.'
    ],
    'payments': [
      'Payment issues require immediate attention. We\'ll resolve this quickly.',
      'For refund requests, please provide your order ID and we\'ll process it.',
      'All payment information is secure and encrypted through Razorpay.'
    ],
    'technical': [
      'Thank you for reporting this technical issue. Our team will investigate.',
      'Try restarting the app and checking your internet connection.',
      'We\'re aware of this issue and working on a fix.'
    ],
    'seller': [
      'Thank you for your seller inquiry. We\'ll help you with the verification process.',
      'For seller-related questions, please check our seller guidelines first.',
      'Seller verification typically takes 24-48 hours to complete.'
    ]
  };

  return suggestions[category] || [
    'Thank you for contacting SwipeSense support. We\'ve received your message and will respond soon.',
    'We appreciate you reaching out. Our team will investigate and get back to you.',
    'Your message is important to us. We\'ll provide a detailed response shortly.'
  ];
};

export default {
  getSupportTickets,
  getTicketsByStatus,
  getRecentTickets,
  getAssignedTickets,
  updateTicketStatus,
  assignTicket,
  addTicketResponse,
  getTicketResponses,
  getSupportStats,
  getSupportStatistics,
  categorizeTicket,
  getSuggestedResponses
};
