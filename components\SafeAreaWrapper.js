import React from 'react';
import { View, StyleSheet, StatusBar, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import theme from '../utils/theme';

/**
 * SafeAreaWrapper component that handles safe area insets consistently across platforms
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} props.style - Additional styles for the container
 * @param {boolean} props.edges - Edges to apply safe area insets to
 * @param {string} props.backgroundColor - Background color of the container
 * @param {boolean} props.withStatusBar - Whether to include status bar configuration
 * @param {string} props.statusBarStyle - Status bar style ('dark-content' or 'light-content')
 * @param {string} props.statusBarColor - Status bar background color
 * @param {boolean} props.statusBarTranslucent - Whether the status bar is translucent (Android only)
 * @returns {React.ReactElement} - SafeAreaWrapper component
 */
const SafeAreaWrapper = ({
  children,
  style,
  edges = ['right', 'left'],
  backgroundColor = theme.colors.background,
  withStatusBar = true,
  statusBarStyle = 'dark-content',
  statusBarColor = 'transparent',
  statusBarTranslucent = true,
}) => {
  return (
    <>
      {withStatusBar && (
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={statusBarColor}
          translucent={statusBarTranslucent}
        />
      )}
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor },
          style,
        ]}
        edges={edges}
      >
        {children}
      </SafeAreaView>
    </>
  );
};

/**
 * SafeAreaHeader component for consistent header styling with safe area insets
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} props.style - Additional styles for the container
 * @param {string} props.backgroundColor - Background color of the container
 * @returns {React.ReactElement} - SafeAreaHeader component
 */
export const SafeAreaHeader = ({
  children,
  style,
  backgroundColor = theme.colors.background,
}) => {
  return (
    <SafeAreaView
      style={[
        styles.headerContainer,
        { backgroundColor },
        style,
      ]}
      edges={['top', 'right', 'left']}
    >
      <View style={styles.headerContent}>
        {children}
      </View>
    </SafeAreaView>
  );
};

/**
 * SafeAreaFooter component for consistent footer styling with safe area insets
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} props.style - Additional styles for the container
 * @param {string} props.backgroundColor - Background color of the container
 * @returns {React.ReactElement} - SafeAreaFooter component
 */
export const SafeAreaFooter = ({
  children,
  style,
  backgroundColor = theme.colors.background,
}) => {
  return (
    <SafeAreaView
      style={[
        styles.footerContainer,
        { backgroundColor },
        style,
      ]}
      edges={['bottom', 'right', 'left']}
    >
      <View style={styles.footerContent}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    width: '100%',
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight || 0,
        elevation: 4,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
    }),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: Platform.OS === 'ios' ? 44 : 56,
    paddingHorizontal: theme.spacing.md,
  },
  footerContainer: {
    width: '100%',
    backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    ...Platform.select({
      android: {
        elevation: 8,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
    }),
  },
  footerContent: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
});

export default SafeAreaWrapper;
