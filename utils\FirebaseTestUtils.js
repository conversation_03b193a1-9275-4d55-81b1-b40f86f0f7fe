import { auth, db } from '../firebase.config';
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  serverTimestamp 
} from 'firebase/firestore';

/**
 * Test Firebase authentication
 * @returns {Promise<Object>} Test result
 */
export const testAuthentication = async () => {
  try {
    const user = auth.currentUser;
    if (user) {
      return { 
        success: true, 
        message: `Authenticated as ${user.email} (${user.uid})`,
        user: {
          uid: user.uid,
          email: user.email,
          emailVerified: user.emailVerified
        }
      };
    } else {
      return { success: false, message: 'Not authenticated' };
    }
  } catch (error) {
    return { success: false, message: `Error: ${error.message}` };
  }
};

/**
 * Test access to user document
 * @returns {Promise<Object>} Test result
 */
export const testUserDocument = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { success: false, message: 'Not authenticated' };
    }
    
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      return { 
        success: true, 
        message: 'User document accessible',
        data: {
          name: userDoc.data().name || 'No name',
          isSeller: userDoc.data().isSeller || false,
          emailVerified: userDoc.data().emailVerified || false
        }
      };
    } else {
      return { success: false, message: 'User document not found' };
    }
  } catch (error) {
    return { success: false, message: `Error: ${error.message}` };
  }
};

/**
 * Test access to clothing items collection
 * @returns {Promise<Object>} Test result
 */
export const testClothingItems = async () => {
  try {
    const itemsQuery = query(collection(db, 'clothingItems'), limit(1));
    const querySnapshot = await getDocs(itemsQuery);
    
    return { 
      success: true, 
      message: `Successfully accessed clothing items (${querySnapshot.docs.length} items)` 
    };
  } catch (error) {
    return { success: false, message: `Error: ${error.message}` };
  }
};

/**
 * Test access to user's cart
 * @returns {Promise<Object>} Test result
 */
export const testCart = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { success: false, message: 'Not authenticated' };
    }
    
    const cartQuery = query(collection(db, 'users', user.uid, 'cart'));
    const querySnapshot = await getDocs(cartQuery);
    
    return { 
      success: true, 
      message: `Successfully accessed cart (${querySnapshot.docs.length} items)` 
    };
  } catch (error) {
    return { success: false, message: `Error: ${error.message}` };
  }
};

/**
 * Test access to user's collections
 * @returns {Promise<Object>} Test result
 */
export const testCollections = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { success: false, message: 'Not authenticated' };
    }
    
    const collectionsQuery = query(collection(db, 'users', user.uid, 'collections'));
    const querySnapshot = await getDocs(collectionsQuery);
    
    return { 
      success: true, 
      message: `Successfully accessed collections (${querySnapshot.docs.length} collections)` 
    };
  } catch (error) {
    return { success: false, message: `Error: ${error.message}` };
  }
};

/**
 * Test access to user's likes
 * @returns {Promise<Object>} Test result
 */
export const testLikes = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { success: false, message: 'Not authenticated' };
    }
    
    const likesQuery = query(collection(db, 'users', user.uid, 'likes'));
    const querySnapshot = await getDocs(likesQuery);
    
    return { 
      success: true, 
      message: `Successfully accessed likes (${querySnapshot.docs.length} likes)` 
    };
  } catch (error) {
    return { success: false, message: `Error: ${error.message}` };
  }
};

/**
 * Run all Firebase tests
 * @returns {Promise<Array>} Array of test results
 */
export const runAllTests = async () => {
  const results = [];
  
  results.push({ test: 'Authentication', ...(await testAuthentication()) });
  results.push({ test: 'User Document', ...(await testUserDocument()) });
  results.push({ test: 'Clothing Items', ...(await testClothingItems()) });
  results.push({ test: 'Cart', ...(await testCart()) });
  results.push({ test: 'Collections', ...(await testCollections()) });
  results.push({ test: 'Likes', ...(await testLikes()) });
  
  return results;
};
