import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Alert, SafeAreaView, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import { sendEmailVerification, signOut } from 'firebase/auth';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

const VerifyEmailScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [userEmail, setUserEmail] = useState('');
  const [hasNavigated, setHasNavigated] = useState(false);
  const user = auth.currentUser;
  const navigationRef = useRef(null);

  useEffect(() => {
    if (user) {
      setUserEmail(user.email);

      // Check if user is already verified
      if (user.emailVerified && !hasNavigated) {
        console.log("User is already verified, updating Firestore");
        handleAlreadyVerified();
      }
    } else {
      // If no user is logged in, navigate to Welcome screen instead of going back
      if (!hasNavigated) {
        console.log("No user logged in, redirecting to Welcome screen");
        setHasNavigated(true);
        navigation.replace('Welcome');
      }
    }
  }, [user, navigation, route.params, hasNavigated]);

  const handleAlreadyVerified = async () => {
    if (hasNavigated) return;
    setHasNavigated(true);

    try {
      // Get user data to check if they're a seller
      const userRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userRef);
      const userData = userDoc.exists() ? userDoc.data() : null;
      const isSeller = userData?.isSeller || route.params?.isSeller || false;

      // Update user document in Firestore
      await updateDoc(userRef, { emailVerified: true });

      if (isSeller) {
        console.log("Seller email already verified, redirecting to seller verification");
        // Update seller verification status
        await updateDoc(userRef, {
          sellerVerificationStatus: 'needsSellerInfo'
        });

        // Show success message and redirect to seller verification
        Alert.alert(
          'Email Verified',
          'Your email is already verified! Now complete your seller profile.',
          [{
            text: 'Continue', onPress: () => {
              console.log("Redirecting to seller verification form");
              // Navigate to seller verification
              navigation.navigate('SellerVerification');
            }
          }]
        );
      } else {
        // Show success message and redirect for non-sellers
        Alert.alert(
          'Already Verified',
          'Your email is already verified!',
          [{
            text: 'Continue', onPress: () => {
              if (route.params?.fromSettings) {
                navigation.goBack();
              } else {
                console.log("Email already verified, redirecting to main app");
                // Navigate to main app
                navigation.replace('MainApp');
              }
            }
          }]
        );
      }
    } catch (error) {
      console.error("Error updating verification status:", error);
      setHasNavigated(false); // Reset on error
    }
  };

  // Countdown timer for resend button
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);
  // Check verification status periodically
  useEffect(() => {
    let checkInterval;

    if (verifying && !hasNavigated) {
      checkInterval = setInterval(async () => {
        try {
          // Reload user to get latest emailVerified status
          await user.reload();
          const updatedUser = auth.currentUser;

          if (updatedUser && updatedUser.emailVerified && !hasNavigated) {
            clearInterval(checkInterval);
            setVerifying(false);
            setHasNavigated(true);
            console.log("Email verified automatically detected");

            // Handle verification completion
            await handleVerificationComplete(updatedUser);
          }
        } catch (error) {
          console.error('Error checking verification status:', error);
        }
      }, 3000); // Check every 3 seconds
    }

    return () => clearInterval(checkInterval);
  }, [verifying, hasNavigated, user, route.params]);

  const handleVerificationComplete = async (updatedUser) => {
    try {
      // Update user document in Firestore
      const userRef = doc(db, 'users', updatedUser.uid);
      await updateDoc(userRef, { emailVerified: true });
      console.log("Updated emailVerified status in Firestore");

      // Handle different verification flows
      const fromSettings = route.params?.fromSettings;
      const userPassword = route.params?.password;
      const isSeller = route.params?.isSeller;

      if (fromSettings) {
        // If coming from settings, go back to settings
        Alert.alert(
          'Email Verified',
          'Your email has been successfully verified!',
          [{ text: 'Continue', onPress: () => navigation.goBack() }]
        );
      } else if (isSeller) {
        // If this is a seller account, proceed to seller verification form
        console.log("Seller email verified, proceeding to seller verification form");

        await updateDoc(userRef, {
          emailVerified: true,
          sellerVerificationStatus: 'needsSellerInfo'
        });
        console.log("Updated emailVerified status in Firestore for seller");

        Alert.alert(
          'Email Verified',
          'Your email has been successfully verified! Now complete your seller profile.',
          [{
            text: 'Continue', onPress: () => {
              console.log("Redirecting to seller verification form");
              navigation.replace('SellerVerification');
            }
          }]
        );
      } else {
        // Check if user is a seller by getting user data from Firestore
        const userDoc = await getDoc(userRef);
        const userData = userDoc.exists() ? userDoc.data() : null;

        if (userData && userData.isSeller) {
          console.log("Seller detected in verification, redirecting to seller verification");
          await updateDoc(userRef, {
            sellerVerificationStatus: 'needsSellerInfo'
          });

          Alert.alert(
            'Email Verified',
            'Your email is verified! Now complete your seller profile.',
            [{
              text: 'Continue', onPress: () => {
                console.log("Redirecting to seller verification form");
                navigation.replace('SellerVerification');
              }
            }]
          );
        } else {
          // Default case for non-sellers
          Alert.alert(
            'Email Verified',
            'Your email has been successfully verified!',
            [{
              text: 'Continue', onPress: () => {
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'MainApp' }],
                });
              }
            }]
          );
        }
      }
    } catch (error) {
      console.error("Error updating verification status:", error);
      setHasNavigated(false); // Reset on error
    }
  };

  const handleResendVerification = async () => {
    if (countdown > 0) return;

    setLoading(true);
    try {
      await sendEmailVerification(user);
      setCountdown(60); // Set 60 seconds cooldown
      setVerifying(true);
      Alert.alert(
        'Verification Email Sent',
        `A verification email has been sent to ${user.email}. Please check your inbox and spam folder.`
      );
    } catch (error) {
      console.error('Error sending verification email:', error);
      Alert.alert('Error', 'Failed to send verification email. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  const handleCheckVerification = async () => {
    if (hasNavigated || loading) return;

    setLoading(true);
    try {
      await user.reload();
      const updatedUser = auth.currentUser;

      if (updatedUser.emailVerified) {
        console.log("Email verified in Firebase Auth");
        setHasNavigated(true);
        await handleVerificationComplete(updatedUser);
      } else {
        console.log("Email not verified yet");
        Alert.alert('Not Verified', 'Your email is not verified yet. Please check your inbox and click the verification link.');
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
      Alert.alert('Error', 'Failed to check verification status. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  const handleLogout = async () => {
    if (hasNavigated) return;

    try {
      setHasNavigated(true);
      await signOut(auth);
      // Navigate to Welcome screen after logout
      console.log("User logged out from verification screen");
      navigation.replace('Welcome');
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
      setHasNavigated(false); // Reset on error
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Ionicons name="mail-outline" size={80} color="#FF6B6B" style={styles.icon} />

        <Text style={styles.title}>Verify Your Email</Text>

        <Text style={styles.description}>
          We've sent a verification email to:
        </Text>

        <Text style={styles.email}>{userEmail}</Text>

        <Text style={styles.instructions}>
          Please check your inbox and click the verification link to activate your account.
          If you don't see the email, check your spam folder.
        </Text>

        <TouchableOpacity
          style={styles.button}
          onPress={handleCheckVerification}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.buttonText}>I've Verified My Email</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.resendButton, countdown > 0 && styles.disabledButton]}
          onPress={handleResendVerification}
          disabled={loading || countdown > 0}
        >
          <Text style={styles.resendButtonText}>
            {countdown > 0 ? `Resend Email (${countdown}s)` : 'Resend Verification Email'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  email: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF6B6B',
    marginBottom: 20,
    textAlign: 'center',
  },
  instructions: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
    marginBottom: 15,
    width: '100%',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resendButton: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#FF6B6B',
    width: '100%',
    alignItems: 'center',
  },
  resendButtonText: {
    color: '#FF6B6B',
    fontSize: 16,
  },
  disabledButton: {
    borderColor: '#ccc',
    backgroundColor: '#f5f5f5',
  },
  logoutButton: {
    marginTop: 20,
    padding: 10,
  },
  logoutButtonText: {
    color: '#666',
    fontSize: 14,
  },
});

export default VerifyEmailScreen;
