import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
  Platform,
  SafeAreaView,
  KeyboardAvoidingView
} from 'react-native';
import { auth, db } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import { doc, updateDoc, collection, addDoc, serverTimestamp, getDoc } from 'firebase/firestore';
import { generateVerificationCode } from '../utils/verificationUtils';
import { signOut } from 'firebase/auth';

const SellerVerificationScreen = ({ navigation, route }) => {
  const [address, setAddress] = useState({
    street: '',
    city: '',
    state: '',
    country: '',
    zipCode: '' // Changed from pincode to zipCode for consistency
  });
  const [website, setWebsite] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);

  const user = auth.currentUser;

  const validateInputs = () => {
    if (!address.street.trim()) {
      Alert.alert('Error', 'Please enter your street address');
      return false;
    }
    if (!address.city.trim()) {
      Alert.alert('Error', 'Please enter your city');
      return false;
    }
    if (!address.state.trim()) {
      Alert.alert('Error', 'Please enter your state/area');
      return false;
    }
    if (!address.country.trim()) {
      Alert.alert('Error', 'Please enter your country');
      return false;
    }
    if (!address.zipCode.trim()) {
      Alert.alert('Error', 'Please enter your zip/postal code');
      return false;
    } if (!phoneNumber.trim()) {
      Alert.alert('Error', 'Please enter your phone number');
      return false;
    }

    // Basic website URL validation (only if website is provided)
    if (website.trim()) {
      const urlRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+([/?#].*)?$/;
      if (!urlRegex.test(website.trim())) {
        Alert.alert('Error', 'Please enter a valid website URL');
        return false;
      }
    }

    // Basic phone number validation
    const phoneRegex = /^\+?[0-9]{10,15}$/;
    if (!phoneRegex.test(phoneNumber.trim())) {
      Alert.alert('Error', 'Please enter a valid phone number (10-15 digits)');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateInputs()) return;

    setLoading(true);
    try {
      if (!user) {
        Alert.alert('Error', 'User not found. Please log in again.');
        navigation.navigate('Login');
        return;
      }

      // Generate verification code
      const verificationCode = generateVerificationCode();

      // Get user's name from Firestore document
      const userDocRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);
      let userName = user.displayName || '';

      if (userDoc.exists()) {
        const userData = userDoc.data();
        userName = userData.name || userData.displayName || user.displayName || '';
      }

      // Update user document with seller verification details
      await updateDoc(userDocRef, {
        address: {
          street: address.street.trim(),
          city: address.city.trim(),
          state: address.state.trim(),
          country: address.country.trim(),
          zipCode: address.zipCode.trim()
        },
        website: website.trim(),
        phoneNumber: phoneNumber.trim(),
        sellerVerificationStatus: 'needsCode' // Status indicating verification code is needed
      });

      // Create a verification request in the sellerVerifications collection
      const verificationData = {
        userId: user.uid,
        email: user.email,
        name: userName,
        address: {
          street: address.street.trim(),
          city: address.city.trim(),
          state: address.state.trim(),
          country: address.country.trim(),
          zipCode: address.zipCode.trim()
        },
        website: website.trim(),
        phoneNumber: phoneNumber.trim(),
        verificationCode: verificationCode,
        status: 'needsCode',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await addDoc(collection(db, 'sellerVerifications'), verificationData);

      // The Cloud Function will automatically trigger when a new document is added to sellerVerifications
      console.log(`Seller verification request submitted for ${user.email}`);
      console.log(`Verification code: ${verificationCode} - This will be sent to the admin via email`);

      // Navigate to verification pending screen
      navigation.navigate('VerificationPending');
    } catch (error) {
      console.error('Error submitting seller verification:', error);
      Alert.alert('Error', 'Failed to submit verification details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.container}>
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => {
                  // Show confirmation dialog before navigating away
                  Alert.alert(
                    'Cancel Verification',
                    'Are you sure you want to cancel the seller verification process? You will be signed out.',
                    [
                      { text: 'No', style: 'cancel' },
                      {
                        text: 'Yes, Sign Out',
                        style: 'destructive',
                        onPress: async () => {
                          try {
                            await signOut(auth);
                            // Navigate to Welcome screen
                            navigation.navigate('Welcome');
                          } catch (error) {
                            console.error('Error signing out:', error);
                            Alert.alert('Error', 'Failed to sign out. Please try again.');
                          }
                        }
                      }
                    ]
                  );
                }}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
              <Text style={styles.title}>Seller Verification</Text>
            </View>
            <Text style={styles.subtitle}>
              Please provide the following details to verify your seller account.
              This information will be reviewed by our team.
            </Text>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Address Information</Text>              <TextInput
                style={styles.input}
                placeholder="Street Address"
                value={address.street}
                onChangeText={(text) => setAddress({ ...address, street: text })}
                editable={!loading}
                placeholderTextColor="#000"
              />

              <TextInput
                style={styles.input}
                placeholder="City"
                value={address.city}
                onChangeText={(text) => setAddress({ ...address, city: text })}
                editable={!loading}
                placeholderTextColor="#000"
              />

              <TextInput
                style={styles.input}
                placeholder="State/Area"
                value={address.state}
                onChangeText={(text) => setAddress({ ...address, state: text })}
                editable={!loading}
                placeholderTextColor="#000"
              />

              <TextInput
                style={styles.input}
                placeholder="Country"
                value={address.country}
                onChangeText={(text) => setAddress({ ...address, country: text })}
                editable={!loading}
                placeholderTextColor="#000"
              />

              <TextInput
                style={styles.input}
                placeholder="Zip/Postal Code"
                value={address.zipCode}
                onChangeText={(text) => setAddress({ ...address, zipCode: text })}
                keyboardType="number-pad"
                editable={!loading}
                placeholderTextColor="#000"
              />
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Contact Information</Text>              <TextInput
                style={styles.input}
                placeholder="Website URL (optional)"
                value={website}
                onChangeText={setWebsite}
                keyboardType="url"
                autoCapitalize="none"
                editable={!loading}
                placeholderTextColor="#000"
              />

              <TextInput
                style={styles.input}
                placeholder="Phone Number"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
                editable={!loading}
                placeholderTextColor="#000"
              />
            </View>

            {loading ? (
              <ActivityIndicator size="large" color="#FF6B6B" style={{ marginTop: 20 }} />
            ) : (
              <TouchableOpacity
                style={styles.button}
                onPress={handleSubmit}
                disabled={loading}
              >
                <Text style={styles.buttonText}>Submit for Verification</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingTop: Platform.OS === 'ios' ? 40 : 20,
    paddingBottom: 20,
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    zIndex: 10,
    padding: 5,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 25,
    textAlign: 'center',
  },
  section: {
    width: '100%',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  button: {
    width: '100%',
    height: 50,
    backgroundColor: '#FF6B6B',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SellerVerificationScreen;
