import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  getBroadCategories,
  getDetailedCategories,
  validateCategorySelection,
  searchCategories
} from '../utils/categoryHierarchy';

const HybridCategorySelector = ({
  selectedBroadCategory,
  selectedDetailedCategories = [],
  onBroadCategorySelect,
  onDetailedCategoriesChange,
  disabled = false,
  style
}) => {
  const [broadCategories] = useState(getBroadCategories());
  const [availableDetailedCategories, setAvailableDetailedCategories] = useState([]);
  const [showBroadModal, setShowBroadModal] = useState(false);
  const [showDetailedModal, setShowDetailedModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredBroadCategories, setFilteredBroadCategories] = useState(broadCategories);
  const [filteredDetailedCategories, setFilteredDetailedCategories] = useState([]);

  // Update available detailed categories when broad category changes
  useEffect(() => {
    if (selectedBroadCategory) {
      const detailed = getDetailedCategories(selectedBroadCategory);
      setAvailableDetailedCategories(detailed);
      setFilteredDetailedCategories(detailed);

      // Remove detailed categories that are no longer available
      const validDetailedCategories = selectedDetailedCategories.filter(cat =>
        detailed.includes(cat)
      );
      if (validDetailedCategories.length !== selectedDetailedCategories.length) {
        onDetailedCategoriesChange(validDetailedCategories);
      }
    } else {
      setAvailableDetailedCategories([]);
      setFilteredDetailedCategories([]);
      onDetailedCategoriesChange([]);
    }
  }, [selectedBroadCategory]);

  // Filter broad categories based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = broadCategories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredBroadCategories(filtered);
    } else {
      setFilteredBroadCategories(broadCategories);
    }
  }, [searchQuery, broadCategories]);

  // Filter detailed categories based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = availableDetailedCategories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDetailedCategories(filtered);
    } else {
      setFilteredDetailedCategories(availableDetailedCategories);
    }
  }, [searchQuery, availableDetailedCategories]);

  const handleBroadCategorySelect = (category) => {
    onBroadCategorySelect(category);
    setShowBroadModal(false);
    setSearchQuery('');
  };

  const handleDetailedCategoryToggle = (category) => {
    const newSelected = selectedDetailedCategories.includes(category)
      ? selectedDetailedCategories.filter(cat => cat !== category)
      : [...selectedDetailedCategories, category];

    onDetailedCategoriesChange(newSelected);
  };

  const openBroadModal = () => {
    if (!disabled) {
      setSearchQuery('');
      setShowBroadModal(true);
    }
  };

  const openDetailedModal = () => {
    if (!disabled && selectedBroadCategory) {
      setSearchQuery('');
      setShowDetailedModal(true);
    }
  };

  const renderSelectedTags = (items, onRemove, emptyText) => (
    <View style={styles.selectedTagsContainer}>
      {items.length === 0 ? (
        <Text style={styles.emptyText}>{emptyText}</Text>
      ) : (
        <View style={styles.tagsWrapper}>
          {items.map((item, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{item}</Text>
              <TouchableOpacity
                onPress={() => onRemove(item)}
                style={styles.tagRemoveButton}
                disabled={disabled}
              >
                <Ionicons name="close" size={14} color="#FF6B6B" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderBroadCategoryModal = () => (
    <Modal
      visible={showBroadModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowBroadModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Category Type</Text>
            <TouchableOpacity
              onPress={() => setShowBroadModal(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />            <TextInput
              style={styles.searchInput}
              placeholder="Search category types..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              placeholderTextColor="#000"
            />
          </View>

          <ScrollView style={styles.categoriesList}>
            {filteredBroadCategories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryItem,
                  selectedBroadCategory === category && styles.selectedCategoryItem
                ]}
                onPress={() => handleBroadCategorySelect(category)}
              >
                <Text
                  style={[
                    styles.categoryItemText,
                    selectedBroadCategory === category && styles.selectedCategoryItemText
                  ]}
                >
                  {category}
                </Text>
                {selectedBroadCategory === category && (
                  <Ionicons name="checkmark" size={20} color="#FF6B6B" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  const renderDetailedCategoryModal = () => (
    <Modal
      visible={showDetailedModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowDetailedModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Specific Categories</Text>
            <TouchableOpacity
              onPress={() => setShowDetailedModal(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />            <TextInput
              style={styles.searchInput}
              placeholder="Search specific categories..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              placeholderTextColor="#000"
            />
          </View>

          <FlatList
            data={filteredDetailedCategories}
            keyExtractor={(item) => item}
            renderItem={({ item: category }) => (
              <TouchableOpacity
                style={[
                  styles.categoryItem,
                  selectedDetailedCategories.includes(category) && styles.selectedCategoryItem
                ]}
                onPress={() => handleDetailedCategoryToggle(category)}
              >
                <Text
                  style={[
                    styles.categoryItemText,
                    selectedDetailedCategories.includes(category) && styles.selectedCategoryItemText
                  ]}
                >
                  {category}
                </Text>
                <Ionicons
                  name={selectedDetailedCategories.includes(category) ? "checkbox" : "square-outline"}
                  size={20}
                  color={selectedDetailedCategories.includes(category) ? "#FF6B6B" : "#666"}
                />
              </TouchableOpacity>
            )}
            style={styles.categoriesList}
          />
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Broad Category Selector (Single Selection) */}
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Category Type</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            disabled && styles.selectorDisabled,
            !selectedBroadCategory && styles.selectorPlaceholder
          ]}
          onPress={openBroadModal}
          disabled={disabled}
        >
          <Text
            style={[
              styles.selectorText,
              !selectedBroadCategory && styles.selectorPlaceholderText
            ]}
          >
            {selectedBroadCategory || 'Select category type'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Detailed Categories Selector (Multiple Selection) */}
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Specific Categories</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            (disabled || !selectedBroadCategory) && styles.selectorDisabled
          ]}
          onPress={openDetailedModal}
          disabled={disabled || !selectedBroadCategory}
        >
          <Text style={styles.selectorText}>
            {selectedDetailedCategories.length > 0
              ? `${selectedDetailedCategories.length} selected`
              : selectedBroadCategory
                ? 'Select specific categories'
                : 'Select category type first'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>

        {renderSelectedTags(
          selectedDetailedCategories,
          (item) => handleDetailedCategoryToggle(item),
          'No specific categories selected'
        )}
      </View>

      {renderBroadCategoryModal()}
      {renderDetailedCategoryModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  selectorContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  selectorDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  selectorPlaceholder: {
    borderColor: '#ccc',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectorPlaceholderText: {
    color: '#999',
  },
  selectedTagsContainer: {
    minHeight: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  tagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 10,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 3,
  },
  tagText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  tagRemoveButton: {
    padding: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  categoriesList: {
    maxHeight: 400,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedCategoryItem: {
    backgroundColor: '#fff5f5',
  },
  categoryItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedCategoryItemText: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
});

export default HybridCategorySelector;
