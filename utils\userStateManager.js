// User State Manager - handles force refresh of user data from Firestore
import { auth, db } from '../firebase.config';
import { doc, getDoc } from 'firebase/firestore';

class UserStateManager {
    constructor() {
        this.listeners = [];
    }

    // Add a listener for user state changes
    addListener(callback) {
        this.listeners.push(callback);
    }

    // Remove a listener
    removeListener(callback) {
        this.listeners = this.listeners.filter(listener => listener !== callback);
    }

    // Force refresh user data from Firestore and notify all listeners
    async forceRefreshUserData() {
        console.log('UserStateManager: Forcing refresh of user data from Firestore');

        const currentUser = auth.currentUser;
        if (!currentUser) {
            console.log('UserStateManager: No current user, cannot refresh');
            return null;
        }

        try {
            // Fetch fresh user data from Firestore
            const userDocRef = doc(db, 'users', currentUser.uid);
            const userDoc = await getDoc(userDocRef);

            if (!userDoc.exists()) {
                console.log('UserStateManager: User document does not exist');
                return null;
            }

            const userData = userDoc.data();
            console.log('UserStateManager: Fresh user data fetched:', userData);

            // Combine Firebase Auth user with Firestore data
            const combinedUserData = {
                ...currentUser,
                ...userData,
                needsVerification: false,
                needsSellerOnboarding: false,
                needsSellerCodeVerification: false
            };

            // Notify all listeners
            this.listeners.forEach(callback => {
                try {
                    callback(combinedUserData);
                } catch (error) {
                    console.error('UserStateManager: Error calling listener callback:', error);
                }
            });

            return combinedUserData;
        } catch (error) {
            console.error('UserStateManager: Error refreshing user data:', error);
            return null;
        }
    }
}

// Export a singleton instance
export default new UserStateManager();
