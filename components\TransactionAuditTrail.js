import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { getTransactionAuditTrail } from '../utils/transactionUtils';

const TransactionAuditTrail = ({ transactionId, style }) => {
    const [auditTrail, setAuditTrail] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        loadAuditTrail();
    }, [transactionId]);

    const loadAuditTrail = async () => {
        try {
            setLoading(true);
            setError(null);
            const trail = await getTransactionAuditTrail(transactionId);
            setAuditTrail(trail);
        } catch (err) {
            console.error('Error loading audit trail:', err);
            setError('Failed to load transaction history');
        } finally {
            setLoading(false);
        }
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'N/A';

        try {
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleString();
        } catch (err) {
            return 'Invalid date';
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending_transfer':
                return '#FF9800';
            case 'transfer_in_progress':
                return '#2196F3';
            case 'transferred':
                return '#4CAF50';
            default:
                return '#666';
        }
    };

    const getStatusDisplayText = (status) => {
        switch (status) {
            case 'pending_transfer':
                return 'Pending Transfer';
            case 'transfer_in_progress':
                return 'Transfer In Progress';
            case 'transferred':
                return 'Transferred';
            default:
                return status || 'Unknown';
        }
    };

    if (loading) {
        return (
            <View style={[styles.container, style]}>
                <Text style={styles.title}>Transaction History</Text>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="#2196F3" />
                    <Text style={styles.loadingText}>Loading history...</Text>
                </View>
            </View>
        );
    }

    if (error) {
        return (
            <View style={[styles.container, style]}>
                <Text style={styles.title}>Transaction History</Text>
                <Text style={styles.errorText}>{error}</Text>
            </View>
        );
    }

    if (auditTrail.length === 0) {
        return (
            <View style={[styles.container, style]}>
                <Text style={styles.title}>Transaction History</Text>
                <Text style={styles.emptyText}>No history available</Text>
            </View>
        );
    }

    return (
        <View style={[styles.container, style]}>
            <Text style={styles.title}>Transaction History</Text>
            <ScrollView style={styles.trailContainer} showsVerticalScrollIndicator={false}>
                {auditTrail.map((entry, index) => (
                    <View key={entry.id || index} style={styles.auditEntry}>
                        <View style={styles.auditHeader}>
                            <View style={styles.statusContainer}>
                                {entry.previousStatus && (
                                    <Text style={[styles.statusText, { color: getStatusColor(entry.previousStatus) }]}>
                                        {getStatusDisplayText(entry.previousStatus)}
                                    </Text>
                                )}
                                {entry.previousStatus && entry.newStatus && (
                                    <Text style={styles.arrowText}> → </Text>
                                )}
                                {entry.newStatus && (
                                    <Text style={[styles.statusText, { color: getStatusColor(entry.newStatus) }]}>
                                        {getStatusDisplayText(entry.newStatus)}
                                    </Text>
                                )}
                            </View>
                            <Text style={styles.timestampText}>
                                {formatTimestamp(entry.timestamp)}
                            </Text>
                        </View>

                        {entry.notes && (
                            <Text style={styles.notesText}>{entry.notes}</Text>
                        )}

                        <Text style={styles.actionByText}>
                            Action by: {entry.actionBy === 'system' ? 'System' : entry.actionBy}
                        </Text>

                        {index < auditTrail.length - 1 && <View style={styles.divider} />}
                    </View>
                ))}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        padding: 16,
        marginVertical: 8,
    },
    title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#1a1a1a',
        marginBottom: 12,
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 20,
    },
    loadingText: {
        marginLeft: 8,
        color: '#666',
        fontSize: 14,
    },
    errorText: {
        color: '#f44336',
        fontSize: 14,
        textAlign: 'center',
        paddingVertical: 20,
    },
    emptyText: {
        color: '#666',
        fontSize: 14,
        textAlign: 'center',
        paddingVertical: 20,
    },
    trailContainer: {
        maxHeight: 300,
    },
    auditEntry: {
        paddingVertical: 8,
    },
    auditHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 4,
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    statusText: {
        fontSize: 14,
        fontWeight: '600',
    },
    arrowText: {
        fontSize: 14,
        color: '#666',
        marginHorizontal: 4,
    },
    timestampText: {
        fontSize: 12,
        color: '#666',
        textAlign: 'right',
    },
    notesText: {
        fontSize: 13,
        color: '#444',
        marginTop: 4,
        fontStyle: 'italic',
    },
    actionByText: {
        fontSize: 12,
        color: '#888',
        marginTop: 2,
    },
    divider: {
        height: 1,
        backgroundColor: '#e0e0e0',
        marginTop: 8,
    },
});

export default TransactionAuditTrail;
