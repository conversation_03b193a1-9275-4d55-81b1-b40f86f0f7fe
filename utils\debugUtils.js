import { Alert, Platform } from 'react-native';
import { getEnvironmentInfo } from './platformUtils';

/**
 * Debug utility to show the current environment information
 * Can be called from any screen to help diagnose issues
 */
export const showEnvironmentDebugInfo = () => {
  try {
    const env = getEnvironmentInfo();
    
    // Build a formatted string with all the environment details
    const infoString = `
📱 Environment Details:
------------------
Platform: ${Platform.OS} ${Platform.Version || ''}
Production: ${env.isProduction ? 'Yes ✅' : 'No ❌'}
Development Mode: ${env.isDev ? 'Yes ✅' : 'No ❌'}
Expo Go: ${env.isExpoGo ? 'Yes ✅' : 'No ❌'}
Standalone App: ${env.isStandaloneApp ? 'Yes ✅' : 'No ❌'}
Hermes Engine: ${env.hasHermes ? 'Yes ✅' : 'No ❌'}
React DevTools: ${env.hasReactDevTools ? 'Yes ✅' : 'No ❌'}
Expo Constants:
  - Execution Env: ${env.executionEnvironment || 'N/A'}
  - App Ownership: ${env.appOwnership || 'N/A'}
  - Release Channel: ${env.releaseChannel || 'N/A'}
Node Env: ${process.env.NODE_ENV || 'Not set'}
`;

    // Display the information in an alert
    Alert.alert(
      'Environment Info',
      infoString,
      [{ text: 'OK' }]
    );
    
    // Also log to console for easier debugging
    console.log('Environment Debug Info:', env);
    
    return env;
  } catch (e) {
    console.error('Error showing environment debug info:', e);
    Alert.alert('Error', 'Could not retrieve environment information: ' + e.message);
    return null;
  }
};

/**
 * Add this to any screen with a button to quickly check environment
 * @example
 * <TouchableOpacity onPress={showEnvironmentDebugInfo}>
 *   <Text>Debug Environment</Text>
 * </TouchableOpacity>
 */
