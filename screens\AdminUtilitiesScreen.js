// Admin Utilities Screen for testing and managing the transaction system
import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Alert,
    ScrollView,
    TextInput,
    ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { createSellerTransactionsFromOrder, markTransactionAsCompleted } from '../utils/transactionUtils';
import { completeOrder } from '../utils/orderUtils';

const AdminUtilitiesScreen = ({ navigation }) => {
    const [loading, setLoading] = useState(false);
    const [testOrderId, setTestOrderId] = useState('');
    const [testTransactionId, setTestTransactionId] = useState('');

    const testCreateTransactions = async () => {
        if (!testOrderId.trim()) {
            Alert.alert('Error', 'Please enter an Order ID');
            return;
        }

        setLoading(true);
        try {
            console.log('Testing transaction creation for order:', testOrderId);

            // Mock order data for testing
            const mockOrderData = {
                id: testOrderId,
                userId: 'test-buyer-123',
                status: 'delivered',
                totalAmount: 1000,
                items: [
                    {
                        id: 'item-1',
                        uploaderId: 'seller-123',
                        title: 'Test Product 1',
                        price: 500,
                        quantity: 1
                    },
                    {
                        id: 'item-2',
                        uploaderId: 'seller-456',
                        title: 'Test Product 2',
                        price: 500,
                        quantity: 1
                    }
                ]
            };

            await createSellerTransactionsFromOrder(testOrderId, mockOrderData);

            Alert.alert(
                'Success',
                'Test transactions created successfully!\n\n' +
                'Platform Fee: 0%\n' +
                'Seller 1 gets: ₹500.00\n' +
                'Seller 2 gets: ₹500.00'
            );
        } catch (error) {
            console.error('Error creating test transactions:', error);
            Alert.alert('Error', 'Failed to create transactions: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const testCompleteTransaction = async () => {
        if (!testTransactionId.trim()) {
            Alert.alert('Error', 'Please enter a Transaction ID');
            return;
        }

        setLoading(true);
        try {
            const transferDetails = {
                method: 'Bank Transfer',
                referenceNumber: `TEST-${Date.now()}`,
                bankName: 'Test Bank',
                transferredBy: 'admin-test',
                transferredAt: new Date()
            };

            await markTransactionAsCompleted(
                testTransactionId,
                transferDetails,
                'Test completion by admin utilities'
            );

            Alert.alert('Success', 'Transaction marked as completed successfully!');
        } catch (error) {
            console.error('Error completing transaction:', error);
            Alert.alert('Error', 'Failed to complete transaction: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const testCompleteOrder = async () => {
        if (!testOrderId.trim()) {
            Alert.alert('Error', 'Please enter an Order ID');
            return;
        }

        setLoading(true);
        try {
            await completeOrder(testOrderId);
            Alert.alert('Success', 'Order completed and transactions created!');
        } catch (error) {
            console.error('Error completing order:', error);
            Alert.alert('Error', 'Failed to complete order: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const navigateToTransactionManagement = () => {
        navigation.navigate('AdminTransactionManagementScreen');
    };

    const utilities = [
        {
            title: 'Transaction Management',
            description: 'View and manage all seller transactions',
            icon: 'card-outline',
            action: navigateToTransactionManagement,
            color: '#FF6B6B'
        },
        {
            title: 'Test Transaction Creation',
            description: 'Create test transactions for an order',
            icon: 'add-circle-outline',
            action: testCreateTransactions,
            color: '#4CAF50'
        },
        {
            title: 'Test Transaction Completion',
            description: 'Mark a transaction as completed',
            icon: 'checkmark-circle-outline',
            action: testCompleteTransaction,
            color: '#2196F3'
        },
        {
            title: 'Test Complete Order',
            description: 'Complete order and auto-create transactions',
            icon: 'receipt-outline',
            action: testCompleteOrder,
            color: '#FF9800'
        }
    ];

    return (
        <SafeAreaWrapper>
            <SafeAreaHeader>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <Ionicons name="arrow-back" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Admin Utilities</Text>
                <View style={styles.placeholder} />
            </SafeAreaHeader>

            <ScrollView style={styles.container}>
                {/* Info Card */}
                <View style={styles.infoCard}>
                    <View style={styles.infoHeader}>
                        <Ionicons name="information-circle" size={24} color="#2196F3" />
                        <Text style={styles.infoTitle}>Transaction System Info</Text>
                    </View>
                    <Text style={styles.infoText}>
                        Platform Fee: <Text style={styles.highlight}>0%</Text> (No fees currently)
                    </Text>
                    <Text style={styles.infoText}>
                        Sellers receive 100% of the sale amount
                    </Text>
                </View>

                {/* Test Inputs */}
                <View style={styles.inputSection}>
                    <Text style={styles.sectionTitle}>Test Parameters</Text>

                    <Text style={styles.inputLabel}>Order ID for Testing</Text>
                    <TextInput
                        style={styles.input}
                        value={testOrderId}
                        onChangeText={setTestOrderId}
                        placeholder="Enter order ID (e.g., test-order-123)"
                        placeholderTextColor="#999"
                    />

                    <Text style={styles.inputLabel}>Transaction ID for Testing</Text>
                    <TextInput
                        style={styles.input}
                        value={testTransactionId}
                        onChangeText={setTestTransactionId}
                        placeholder="Enter transaction ID"
                        placeholderTextColor="#999"
                    />
                </View>

                {/* Utility Buttons */}
                <View style={styles.utilitiesSection}>
                    <Text style={styles.sectionTitle}>Available Utilities</Text>

                    {utilities.map((utility, index) => (
                        <TouchableOpacity
                            key={index}
                            style={[styles.utilityCard, { borderLeftColor: utility.color }]}
                            onPress={utility.action}
                            disabled={loading}
                        >
                            <View style={styles.utilityIcon}>
                                <Ionicons name={utility.icon} size={24} color={utility.color} />
                            </View>
                            <View style={styles.utilityContent}>
                                <Text style={styles.utilityTitle}>{utility.title}</Text>
                                <Text style={styles.utilityDescription}>{utility.description}</Text>
                            </View>
                            <Ionicons name="chevron-forward" size={20} color="#666" />
                        </TouchableOpacity>
                    ))}
                </View>

                {/* Status */}
                {loading && (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color="#FF6B6B" />
                        <Text style={styles.loadingText}>Processing...</Text>
                    </View>
                )}
            </ScrollView>
        </SafeAreaWrapper>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    backButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        textAlign: 'center',
    },
    placeholder: {
        width: 40,
    },
    infoCard: {
        backgroundColor: '#fff',
        margin: 16,
        padding: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    infoHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    infoTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 8,
    },
    infoText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 4,
    },
    highlight: {
        fontWeight: 'bold',
        color: '#4CAF50',
    },
    inputSection: {
        backgroundColor: '#fff',
        margin: 16,
        marginTop: 0,
        padding: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 16,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    input: {
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#fff',
    },
    utilitiesSection: {
        margin: 16,
        marginTop: 0,
    },
    utilityCard: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        flexDirection: 'row',
        alignItems: 'center',
        borderLeftWidth: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    utilityIcon: {
        marginRight: 12,
    },
    utilityContent: {
        flex: 1,
    },
    utilityTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },
    utilityDescription: {
        fontSize: 14,
        color: '#666',
    },
    loadingContainer: {
        alignItems: 'center',
        padding: 20,
    },
    loadingText: {
        marginTop: 8,
        fontSize: 16,
        color: '#666',
    },
});

export default AdminUtilitiesScreen;
