import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  Image,
  FlatList,
  Platform, // Added Platform
  StatusBar // Added StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase.config';
import { getOrderDetails } from '../utils/paymentUtils';

const OrderConfirmationScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [mainOrder, setMainOrder] = useState(null);
  const [totalAmount, setTotalAmount] = useState(0);

  const { orderIds, orderId, paymentId, isSimulated } = route.params || {};
  // Handle both new format (orderIds array) and old format (single orderId)
  const orderIdArray = orderIds || (orderId ? [orderId] : []);
  const mainOrderId = orderIdArray.length > 0 ? orderIdArray[0] : null;

  useEffect(() => {
    if (orderIdArray.length > 0) {
      fetchOrdersDetails();
    } else {
      setLoading(false);
    }
  }, [orderIdArray]);

  const fetchOrdersDetails = async () => {
    try {
      console.log('Fetching order details for:', orderIdArray);

      const orderDetailsPromises = orderIdArray.map(id => getOrderDetails(id));
      const orderDetailsResults = await Promise.all(orderDetailsPromises);

      // Filter out any null results
      const validOrders = orderDetailsResults.filter(order => order !== null);

      if (validOrders.length > 0) {
        setOrders(validOrders);
        setMainOrder(validOrders[0]); // Set the first order as the main order

        // Calculate total amount across all orders
        const total = validOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
        setTotalAmount(total);
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderOrderItems = () => {
    // Combine all items from all orders
    const allItems = orders.flatMap(order => order.items || []);

    if (allItems.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No items in this order</Text>
        </View>
      );
    }

    return allItems.map((item, index) => (
      <View key={`${item.id || ''}-${index}`} style={styles.orderItem}>
        <View style={styles.itemImageContainer}>
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.itemImage}
            resizeMode="cover"
          />
        </View>
        <View style={styles.itemDetails}>
          <Text style={styles.itemTitle}>{item.title || 'Untitled Item'}</Text>
          {item.brand && <Text style={styles.itemBrand}>{item.brand}</Text>}
          {item.gender && <Text style={styles.itemGender}>{item.gender}</Text>}
          <View style={styles.priceQuantityRow}>
            <Text style={styles.itemPrice}>₹{(item.price || 0).toFixed(2)}</Text>
            <Text style={styles.itemQuantity}>Qty: {item.quantity || 1}</Text>
          </View>
        </View>
      </View>
    ));
  };

  const renderShippingAddress = () => {
    if (!mainOrder || !mainOrder.shippingAddress) return null;

    const address = mainOrder.shippingAddress;

    return (
      <View style={styles.addressContainer}>
        <Text style={styles.addressText}>{address.street}</Text>
        <Text style={styles.addressText}>{address.city}, {address.state} {address.zipCode}</Text>
        <Text style={styles.addressText}>{address.country}</Text>
        <Text style={styles.addressText}>Phone: {address.phone}</Text>
      </View>
    );
  };

  const renderOrderSummary = () => {
    if (!mainOrder) return null;

    return (
      <View style={styles.summaryContainer}>
        {orders.length > 1 ? (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Orders Created:</Text>
            <Text style={styles.summaryValue}>{orders.length}</Text>
          </View>
        ) : (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Order ID:</Text>
            <Text style={styles.summaryValue}>{mainOrderId}</Text>
          </View>
        )}
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Payment ID:</Text>
          <Text style={styles.summaryValue}>{paymentId}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Order Date:</Text>
          <Text style={styles.summaryValue}>{formatDate(mainOrder.createdAt)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Order Status:</Text>
          <Text style={[styles.summaryValue, styles.statusText]}>
            Processing
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Payment Status:</Text>
          <Text style={[styles.summaryValue, styles.paymentStatusText]}>
            Completed
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.navigate('ClothingFeed')}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Order Confirmation</Text>
          <View style={styles.rightPlaceholder} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Loading order details...</Text>
          </View>
        ) : (
          <>
            <ScrollView style={styles.content}>
              <View style={styles.successContainer}>
                <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
                <Text style={styles.successTitle}>Order Placed!</Text>
                <Text style={styles.successText}>
                  Your order has been successfully placed and is being processed.
                </Text>
                {isSimulated && (
                  <View style={styles.simulatedWarning}>
                    <Ionicons name="warning" size={20} color="#FF9800" />
                    <Text style={styles.simulatedWarningText}>
                      This was a simulated payment for testing purposes. In a production build, this would be a real payment.
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Summary</Text>
                {renderOrderSummary()}
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Items</Text>
                {renderOrderItems()}
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Shipping Address</Text>
                {renderShippingAddress()}
              </View>

              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Payment Details</Text>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Subtotal</Text>
                  <Text style={styles.paymentValue}>
                    ₹{(totalAmount / 1.05).toFixed(2)}
                  </Text>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Shipping</Text>
                  <Text style={styles.paymentValue}>₹0.00</Text>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Tax (5%)</Text>
                  <Text style={styles.paymentValue}>₹{(totalAmount - (totalAmount / 1.05)).toFixed(2)}</Text>
                </View>
                <View style={[styles.paymentRow, styles.totalRow]}>
                  <Text style={styles.totalLabel}>Total</Text>
                  <Text style={styles.totalValue}>
                    ₹{totalAmount.toFixed(2)}
                  </Text>
                </View>
              </View>
            </ScrollView>

            <View style={styles.footer}>
              <TouchableOpacity
                style={styles.continueButton}
                onPress={() => navigation.navigate('ClothingFeed')}
              >
                <Text style={styles.continueButtonText}>Continue Shopping</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.viewOrdersButton}
                onPress={() => navigation.navigate('OrderHistory')}
              >
                <Text style={styles.viewOrdersButtonText}>View My Orders</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 // Added for Android
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
    backgroundColor: '#fff',
    // Removed elevation for Android, handled by safeArea paddingTop
    // elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  rightPlaceholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  successContainer: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: 'rgba(76,175,80,0.08)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#4CAF50',
    marginTop: 12,
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  successText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  simulatedWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    marginTop: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  simulatedWarningText: {
    fontSize: 14,
    color: '#E65100',
    marginLeft: 8,
    flex: 1,
    fontWeight: '500',
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  summaryContainer: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 10,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  statusText: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  paymentStatusText: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  orderItem: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
    overflow: 'hidden',
  },
  itemImageContainer: {
    width: 90,
    height: 90,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  itemImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  itemDetails: {
    flex: 1,
    padding: 10,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  itemBrand: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  itemGender: {
    fontSize: 13,
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  priceQuantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#888',
  },
  addressContainer: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 10,
  },
  addressText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  paymentLabel: {
    fontSize: 16,
    color: '#333',
  },
  paymentValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  totalRow: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  footer: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  continueButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  viewOrdersButton: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewOrdersButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
});

export default OrderConfirmationScreen;
