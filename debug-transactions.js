// Debug Transaction Creation
// Quick test to understand the price issue

console.log('=== TRANSACTION DEBUG TEST ===');

// Mock the exact structure that would come from a cart/order
const mockCartItems = [
    {
        id: 'cart-item-1',
        itemId: 'clothing-item-123',
        title: 'Test Shirt',
        price: 280, // This should be preserved
        quantity: 1,
        uploaderId: 'seller-user-1', // Seller who uploaded this item
        addedAt: new Date()
    },
    {
        id: 'cart-item-2',
        itemId: 'clothing-item-456',
        title: 'Test Pants',
        price: 280,
        quantity: 1,
        uploaderId: 'seller-user-2', // Different seller
        addedAt: new Date()
    }
];

const mockOrderData = {
    userId: 'buyer-user-123',
    amount: 560, // Total order amount
    items: mockCartItems,
    status: 'confirmed'
};

console.log('Mock order data:', mockOrderData);

// This should create:
// Transaction 1: seller-user-1 -> ₹280 for Test Shirt
// Transaction 2: seller-user-2 -> ₹280 for Test Pants

// The issue might be:
// 1. price field is 0 in cart items
// 2. uploaderId is missing in cart items  
// 3. Price calculation logic is wrong
// 4. Field name mismatch (price vs amount)

console.log('=== Expected Results ===');
console.log('Seller seller-user-1 should see: ₹280 transaction');
console.log('Seller seller-user-2 should see: ₹280 transaction');
console.log('Admin should see: Both transactions totaling ₹560');
console.log('Current issue: All amounts showing as ₹0.00');

export { mockOrderData, mockCartItems };
