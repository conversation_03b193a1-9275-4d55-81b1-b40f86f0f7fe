import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  filterContainer: {
    paddingVertical: 6,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  filterScrollContent: {
    paddingHorizontal: 5,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#f8f8f8',
    marginHorizontal: 3,
    flexDirection: 'row',
    alignItems: 'center',
    height: 32,
  },
  activeFilterButton: {
    backgroundColor: '#FF6B6B',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#555',
    fontWeight: '500',
    marginRight: 3,
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  activeIcon: {
    marginLeft: 2,
  },
  categoryLoadingIndicator: {
    paddingHorizontal: 10,
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
});
