import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Alert, ScrollView, Platform, ActivityIndicator, Linking } from 'react-native';
import { doc, onSnapshot, collection, query, where, getDocs, addDoc, serverTimestamp } from 'firebase/firestore';
import { db, auth } from '../firebase.config'; // Import auth as well
import { Ionicons } from '@expo/vector-icons';

// Reusing styles from ProfileScreen, consider moving to a shared file if they diverge significantly
// For simplicity, copying relevant styles here. Ideally, import from a shared styles file.
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between', // Keep space-between for potential future additions
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? 40 : 50,
    paddingHorizontal: 15,
    paddingBottom: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  profileInfoContainer: {
    alignItems: 'center',
    paddingVertical: 30,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 15,
    borderWidth: 3,
    borderColor: '#FFC0CB', // Match MyProfileScreen border
  },
  nameSellerContainer: { // Style for the container
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    flexWrap: 'wrap', // Allow wrapping if name is long
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8, // Add space between name and badge
    textAlign: 'center', // Center if it wraps
  },
  sellerBadge: { // Style for the seller badge
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffebee', // Light pink background
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: '#FFC0CB', // Light pink border
    marginTop: Platform.OS === 'ios' ? 0 : 4, // Adjust spacing for Android wrap
  },
  sellerBadgeText: { // Style for the seller text
    marginLeft: 4,
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '600',
  },
  userBio: {
    fontSize: 15,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10, // Adjusted margin
    paddingHorizontal: 20, // Reduced horizontal padding
    lineHeight: 22, // Added line height
  },
  addressText: { // Style for address
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  linkText: { // Style for link
    color: '#007AFF',
    textDecorationLine: 'underline',
  },
  actionsContainer: { // Simplified actions
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    marginBottom: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#eee',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  actionIcon: {
    marginRight: 15,
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  loadingContainer: { // For initial loading or error state within the profile area
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    minHeight: 200, // Give it some height
  },
  loadingContainerCentered: { // For full screen loading
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
});


const UserProfileScreen = ({ route, navigation }) => {
  const { userId: profileUserId } = route.params; // Rename for clarity
  const [userInfo, setUserInfo] = useState(null);
  const [currentUserInfo, setCurrentUserInfo] = useState(null); // State for current user's profile
  const [loading, setLoading] = useState(true);
  // Keeping messaging state and function for future use, currently not displayed
  const [isMessaging, setIsMessaging] = useState(false); // State for message button loading
  const currentUser = auth.currentUser;

  useEffect(() => {
    let profileUnsubscribe = null;
    let currentUserUnsubscribe = null;
    let isMounted = true; // Flag to prevent state updates on unmounted component

    const fetchData = async () => {
      if (isMounted) {
        setUserInfo(null);
        setCurrentUserInfo(null);
        setLoading(true);
      }

      // Fetch Profile User's Data
      if (profileUserId) {
        const userDocRef = doc(db, 'users', profileUserId);
        profileUnsubscribe = onSnapshot(userDocRef, (docSnap) => {
          if (!isMounted) return;
          if (docSnap.exists()) {
            const data = docSnap.data();
            setUserInfo({
              id: docSnap.id,
              name: data.name || 'User Name',
              bio: data.bio || 'No bio available.',
              address: data.address || null,
              storeLink: data.website || null, // Use website field
              profilePictureUrl: data.profilePictureUrl || 'https://via.placeholder.com/150', // Store the URL
              isSeller: data.isSeller || false, // Fetch isSeller status
            });
          } else {
            console.log("No such user document!");
            Alert.alert("Error", "User profile not found.");
            setUserInfo(null);
          }
        }, (error) => {
          if (!isMounted) return;
          console.error("Error fetching profile user data:", error);
          Alert.alert("Error", "Could not fetch user profile.");
          setUserInfo(null);
        });
      } else {
        if (isMounted) {
          Alert.alert("Error", "User ID not provided.");
          setLoading(false);
          navigation.goBack();
        }
        return;
      }

      // Fetch Current User's Data if logged in
      if (currentUser) {
        const currentUserDocRef = doc(db, 'users', currentUser.uid);
        currentUserUnsubscribe = onSnapshot(currentUserDocRef, (docSnap) => {
          if (!isMounted) return;
          if (docSnap.exists()) {
            setCurrentUserInfo(docSnap.data());
          } else {
            console.log("Current user profile document not found!");
            setCurrentUserInfo({ name: 'You', profilePictureUrl: null }); // Basic fallback
          }
        }, (error) => {
          if (!isMounted) return;
          console.error("Error fetching current user data:", error);
          setCurrentUserInfo(null);
        });
      } else {
        if (isMounted) setCurrentUserInfo(null); // Not logged in
      }

      if (isMounted) setLoading(false);
    };

    fetchData();

    // Cleanup listeners on component unmount
    return () => {
      isMounted = false; // Set flag on unmount
      if (profileUnsubscribe) profileUnsubscribe();
      if (currentUserUnsubscribe) currentUserUnsubscribe();
    };
  }, [profileUserId, currentUser, navigation]); // Add currentUser dependency

  const handleViewUploads = () => {
    // Navigate to UserUploads, passing the viewed user's ID
    navigation.navigate('UserUploads', { userId: profileUserId });
  };

  // Function kept for future use but currently not called as message button is hidden
  const handleMessageUser = async () => {
    // Ensure both user infos are loaded before proceeding
    if (!currentUser || !currentUserInfo?.name || !userInfo?.name || !profileUserId || currentUser.uid === profileUserId) {
      console.log("Cannot message this user or yourself, or user data not loaded.");
      Alert.alert("Error", "Could not initiate chat. User data might be loading or missing.");
      return;
    }

    setIsMessaging(true);
    try {
      const threadsRef = collection(db, 'threads');
      // Query for existing threads involving both users
      const q1 = query(threadsRef, where('participants', '==', [currentUser.uid, profileUserId]));
      const q2 = query(threadsRef, where('participants', '==', [profileUserId, currentUser.uid]));

      const [querySnapshot1, querySnapshot2] = await Promise.all([getDocs(q1), getDocs(q2)]);

      const existingThread = querySnapshot1.docs[0] || querySnapshot2.docs[0];

      let threadId;
      if (existingThread) {
        threadId = existingThread.id;
        console.log("Found existing thread:", threadId);
      } else {
        console.log("Creating new thread...");
        // Create a new thread if none exists
        const newThreadRef = await addDoc(threadsRef, {
          participants: [currentUser.uid, profileUserId],
          participantNames: {
            [currentUser.uid]: currentUserInfo.name,
            [profileUserId]: userInfo.name
          },
          participantImages: {
            [currentUser.uid]: currentUserInfo.profilePictureUrl || null,
            [profileUserId]: userInfo.profilePictureUrl || null
          },
          lastMessage: "",
          lastMessageTimestamp: serverTimestamp(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
        threadId = newThreadRef.id;
        console.log("Created new thread:", threadId);
      }

      // Navigate to the chat thread screen
      navigation.navigate('ChatThread', {
        threadId: threadId,
        otherUserId: profileUserId // Pass the other user's ID
      });

    } catch (error) {
      console.error("Error finding or creating chat thread:", error);
      Alert.alert("Error", "Could not start chat. Please try again.");
    } finally {
      setIsMessaging(false);
    }
  };

  // Simplified action button component for this screen
  const ProfileActionButton = ({ iconName, title, onPress, disabled }) => (
    <TouchableOpacity style={styles.actionButton} onPress={onPress} disabled={disabled}>
      <Ionicons name={iconName} size={24} color="#555" style={styles.actionIcon} />
      <Text style={styles.actionText}>{title}</Text>
      <Ionicons name="chevron-forward-outline" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainerCentered}>
        <ActivityIndicator size="large" color="#FF6B6B" />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back-outline" size={28} color="#333" />
        </TouchableOpacity>
        {/* Header is simpler, no settings icon */}
      </View>

      {userInfo ? (
        <View style={styles.profileInfoContainer}>
          <Image source={{ uri: userInfo.profilePictureUrl }} style={styles.profileImage} />
          {/* Container for name and seller badge */}
          <View style={styles.nameSellerContainer}>
            <Text style={styles.userName}>{userInfo.name}</Text>
            {userInfo.isSeller && (
              <View style={styles.sellerBadge}>
                <Ionicons name="storefront-outline" size={16} color="#FF6B6B" />
                <Text style={styles.sellerBadgeText}>Seller</Text>
              </View>
            )}
          </View>
          <Text style={styles.userBio}>{userInfo.bio}</Text>
          {/* Display address if available */}
          {userInfo.address && (
            <Text style={styles.addressText}>
              {`${userInfo.address.street}, ${userInfo.address.city}, ${userInfo.address.state} ${userInfo.address.zipCode}, ${userInfo.address.country}`}
            </Text>
          )}
          {userInfo.storeLink ? (
            <Text
              style={[styles.userBio, styles.linkText]}
              onPress={() => Linking.openURL(userInfo.storeLink)}
            >
              {userInfo.storeLink}
            </Text>
          ) : null}
          {/* No Edit Profile button here */}
        </View>
      ) : (
        <View style={styles.loadingContainer}>
          <Text>Could not load profile information.</Text>
        </View>
      )}

      {/* Only show relevant actions for viewing another user */}
      {userInfo && (
         <View style={styles.actionsContainer}>
           <ProfileActionButton iconName="cloud-upload-outline" title={`${userInfo.name}'s Uploads`} onPress={handleViewUploads} />
           {/* Add Message button only if viewing someone else's profile AND data is loaded */}
           {/* Message button code kept but not rendered
           currentUser && currentUser.uid !== profileUserId && currentUserInfo?.name && userInfo?.name && (
            <ProfileActionButton
              iconName="chatbubble-ellipses-outline"
              title={`Message ${userInfo.name}`}
              onPress={handleMessageUser}
              disabled={isMessaging} // Disable while processing
            />
           ) */}
         </View>
      )}

      {/* No Logout button here */}
    </ScrollView>
  );
};

export default UserProfileScreen;
