import { StyleSheet, Platform } from 'react-native';

export const styles = StyleSheet.create({
  animationContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
    // Add platform-specific styles for better Android compatibility
    ...Platform.select({
      android: {
        elevation: 1000, // Higher elevation for Android to ensure visibility
      },
      ios: {
        zIndex: 1000, // zIndex for iOS
      },
    }),
  },
  animation: {
    width: 180,
    height: 180,
    // Add platform-specific styles for better Android compatibility
    ...Platform.select({
      android: {
        opacity: 1, // Force opacity on Android
      },
    }),
  },
  cartAnimationContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // More opaque for Android
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    // Platform-specific shadow handling
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 8, // Higher elevation for Android
        borderWidth: 1, // Add border for Android
        borderColor: 'rgba(0,0,0,0.1)', // Subtle border color
      },
    }),
  },
  cartAnimationText: {
    marginTop: 10,
    fontSize: Platform.OS === 'android' ? 20 : 18, // Larger text on Android
    fontWeight: 'bold',
    color: '#4CAF50',
    // Add text shadow for better visibility on Android
    ...Platform.select({
      android: {
        textShadowColor: 'rgba(0,0,0,0.1)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 1,
      },
    }),
  },
});
