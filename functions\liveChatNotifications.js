const functions = require('firebase-functions');
const admin = require('firebase-admin');
const SibApiV3Sdk = require('sib-api-v3-sdk');

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Initialize Brevo API client
const defaultClient = SibApiV3Sdk.ApiClient.instance;
const apiKey = defaultClient.authentications['api-key'];
const brevoApiKey = functions.config().brevo?.apikey || process.env.BREVO_API_KEY;
apiKey.apiKey = brevoApiKey;

/**
 * Cloud Function that sends notifications when a new live chat is created
 */
exports.notifyAdminsOfNewChat = functions.firestore
  .document('supportChats/{chatId}')
  .onCreate(async (snap, context) => {
    try {
      const chatId = context.params.chatId;
      const chatData = snap.data();

      console.log(`New live chat created: ${chatId}`);

      // Send email notification to admins
      await sendAdminChatNotification(chatId, chatData);

      // Send push notifications to admin users
      await sendPushNotificationToAdmins(chatId, chatData);

      console.log(`Live chat notifications sent for chat: ${chatId}`);
      return null;
    } catch (error) {
      console.error('Error sending live chat notifications:', error);
      return null;
    }
  });

/**
 * Cloud Function that sends notifications when new messages are added to live chat
 */
exports.notifyAdminsOfNewMessage = functions.firestore
  .document('chatMessages/{messageId}')
  .onCreate(async (snap, context) => {
    try {
      const messageId = context.params.messageId;
      const messageData = snap.data();

      // Only notify for user messages, not system messages
      if (messageData.type === 'user') {
        console.log(`New user message in chat: ${messageData.chatId}`);

        // Get chat details
        const chatDoc = await admin.firestore().doc(`supportChats/${messageData.chatId}`).get();
        if (chatDoc.exists) {
          const chatData = chatDoc.data();
          
          // Send push notifications to admins about new message
          await sendMessageNotificationToAdmins(messageData, chatData);
        }
      }

      return null;
    } catch (error) {
      console.error('Error sending message notifications:', error);
      return null;
    }
  });

/**
 * Send email notification to admins about new live chat
 */
async function sendAdminChatNotification(chatId, chatData) {
  try {
    const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: '<EMAIL>', name: 'SwipeSense Support Team' }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense Live Chat System',
      email: '<EMAIL>'
    };

    sendSmtpEmail.subject = `🔴 New Live Chat Started - ${chatData.userName}`;

    // Generate HTML content
    sendSmtpEmail.htmlContent = generateChatNotificationEmail(chatId, chatData);
    sendSmtpEmail.replyTo = { email: chatData.userEmail, name: chatData.userName };

    // Add headers for better organization
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'live-chat-notification',
      'X-Mailin-Tag': 'LiveChatStarted',
      'X-Chat-ID': chatId,
      'X-User-Type': chatData.isSeller ? 'seller' : 'buyer'
    };

    // Add tags for tracking
    sendSmtpEmail.tags = ['live-chat', 'admin-notification', chatData.isSeller ? 'seller-chat' : 'buyer-chat'];

    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Admin chat notification sent for chat: ${chatId}`);
    return result;
  } catch (error) {
    console.error('Error sending admin chat notification:', error);
    return null;
  }
}

/**
 * Send push notifications to all admin users
 */
async function sendPushNotificationToAdmins(chatId, chatData) {
  try {
    // Get all admin users
    const adminsSnapshot = await admin.firestore()
      .collection('users')
      .where('isAdmin', '==', true)
      .get();

    const adminTokens = [];
    adminsSnapshot.docs.forEach(doc => {
      const adminData = doc.data();
      if (adminData.pushToken) {
        adminTokens.push(adminData.pushToken);
      }
    });

    if (adminTokens.length === 0) {
      console.log('No admin push tokens found');
      return;
    }

    // Prepare notification payload
    const payload = {
      notification: {
        title: '🔴 New Live Chat',
        body: `${chatData.userName} started a live chat session`,
        icon: 'https://your-app-icon-url.com/icon.png',
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      },
      data: {
        type: 'live_chat',
        chatId: chatId,
        userName: chatData.userName,
        userEmail: chatData.userEmail,
        screen: 'AdminSupportDashboard'
      }
    };

    // Send to all admin tokens
    const promises = adminTokens.map(token => {
      return admin.messaging().send({
        token: token,
        ...payload
      });
    });

    await Promise.all(promises);
    console.log(`Push notifications sent to ${adminTokens.length} admins`);
  } catch (error) {
    console.error('Error sending push notifications to admins:', error);
  }
}

/**
 * Send push notifications to admins about new messages
 */
async function sendMessageNotificationToAdmins(messageData, chatData) {
  try {
    // Get all admin users
    const adminsSnapshot = await admin.firestore()
      .collection('users')
      .where('isAdmin', '==', true)
      .get();

    const adminTokens = [];
    adminsSnapshot.docs.forEach(doc => {
      const adminData = doc.data();
      if (adminData.pushToken) {
        adminTokens.push(adminData.pushToken);
      }
    });

    if (adminTokens.length === 0) {
      console.log('No admin push tokens found');
      return;
    }

    // Prepare notification payload
    const payload = {
      notification: {
        title: `💬 ${chatData.userName}`,
        body: messageData.message.length > 50 ? 
          `${messageData.message.substring(0, 50)}...` : 
          messageData.message,
        icon: 'https://your-app-icon-url.com/icon.png'
      },
      data: {
        type: 'chat_message',
        chatId: messageData.chatId,
        messageId: messageData.messageId,
        userName: chatData.userName,
        screen: 'LiveChatScreen'
      }
    };

    // Send to all admin tokens
    const promises = adminTokens.map(token => {
      return admin.messaging().send({
        token: token,
        ...payload
      });
    });

    await Promise.all(promises);
    console.log(`Message notifications sent to ${adminTokens.length} admins`);
  } catch (error) {
    console.error('Error sending message notifications to admins:', error);
  }
}

/**
 * Generate HTML email content for chat notification
 */
function generateChatNotificationEmail(chatId, chatData) {
  const userType = chatData.isSeller ? 'Seller' : 'Buyer';
  const urgencyColor = chatData.isSeller ? '#FF6B6B' : '#4ECDC4';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>New Live Chat - SwipeSense</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, ${urgencyColor} 0%, #45B7D1 100%); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🔴 New Live Chat Started</h1>
            <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">Immediate attention required</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #333; margin-top: 0;">Chat Details</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #666;">User:</td>
                    <td style="padding: 8px 0;">${chatData.userName}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #666;">Email:</td>
                    <td style="padding: 8px 0;">${chatData.userEmail}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #666;">User Type:</td>
                    <td style="padding: 8px 0;"><span style="background: ${urgencyColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${userType}</span></td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #666;">Chat ID:</td>
                    <td style="padding: 8px 0; font-family: monospace; background: #e9ecef; padding: 4px 8px; border-radius: 4px;">${chatId}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; color: #666;">Started:</td>
                    <td style="padding: 8px 0;">${new Date().toLocaleString()}</td>
                </tr>
            </table>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://console.firebase.google.com/project/styleswipe-67cb3/firestore/data/~2FsupportChats~2F${chatId}" 
               style="background: ${urgencyColor}; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                🚀 View Chat in Firebase Console
            </a>
        </div>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>⚡ Action Required:</strong> A user is waiting for live support. Please respond as soon as possible to provide excellent customer service.</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 14px; margin: 0;">
                SwipeSense Live Chat System<br>
                <a href="mailto:<EMAIL>" style="color: ${urgencyColor};"><EMAIL></a>
            </p>
        </div>
    </body>
    </html>
  `;
}
