const functions = require('firebase-functions');
const admin = require('firebase-admin');
const SibApiV3Sdk = require('sib-api-v3-sdk'); // Brevo (formerly Sendinblue) SDK

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Initialize Brevo API client
const defaultClient = SibApiV3Sdk.ApiClient.instance;
const apiKey = defaultClient.authentications['api-key'];
const brevoApiKey = functions.config().brevo?.apikey || process.env.BREVO_API_KEY; // Get from Firebase config or fallback to env var
console.log('Using Brevo API Key:', brevoApiKey ? 'API key is set' : 'API key is missing');
apiKey.apiKey = brevoApiKey;

/**
 * Cloud Function to send email
 * This is a generic email sending function that can be used for various email templates
 */
exports.sendEmail = functions.https.onCall(async (data, context) => {
  try {
    // Check if the request is from an authenticated user
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'You must be logged in to perform this action.'
      );
    }

    const { to, subject, templateId, params, apiKey, headers } = data;

    if (!to || !subject) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email recipient and subject are required.'
      );
    }

    // Create a Brevo email API instance
    const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: to }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };
    sendSmtpEmail.subject = subject;

    // If templateId is provided, use it
    if (templateId) {
      sendSmtpEmail.templateId = templateId;
      if (params) {
        sendSmtpEmail.params = params;
      }
    } else {
      // Otherwise, use the provided HTML content
      sendSmtpEmail.htmlContent = generateOrderConfirmationEmail(params);
    }

    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    const defaultHeaders = {
      'X-Mailin-custom': 'order-notification',
      'X-Mailin-Tag': 'OrderNotification'
    };

    // Merge custom headers if provided
    sendSmtpEmail.headers = headers ? { ...defaultHeaders, ...headers } : defaultHeaders;

    // Add tags for better tracking
    sendSmtpEmail.tags = ['order-notification'];

    // Add order-specific tags if this is an order email
    if (params && params.orderNumber) {
      sendSmtpEmail.tags.push(`order-${params.orderNumber}`);

      // Add seller-specific tag if this is a seller notification
      if (params.sellerId) {
        sendSmtpEmail.tags.push(`seller-${params.sellerId}`);
      }
    }

    // Send email using Brevo API
    console.log('Attempting to send email to:', to, 'with subject:', subject);

    try {
      // Add a small delay to prevent rate limiting and ensure emails are processed separately
      await new Promise(resolve => setTimeout(resolve, 1000));

      const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
      console.log(`Email sent to ${to} with subject: ${subject}`);
      console.log('Email send result:', result);
      return { success: true, message: 'Email sent successfully' };
    } catch (emailError) {
      console.error('Error sending email with Brevo:', emailError);
      console.error('Error details:', JSON.stringify(emailError, null, 2));
      throw new functions.https.HttpsError(
        'internal',
        `Failed to send email: ${emailError.message}`
      );
    }
  } catch (error) {
    console.error('Error in sendEmail function:', error);
    throw new functions.https.HttpsError(
      'internal',
      `Failed to process request: ${error.message}`
    );
  }
});

/**
 * Cloud Function that sends order confirmation emails when payment is completed
 * This function triggers when an order is updated and checks if payment status changed to 'completed'
 */
exports.sendOrderConfirmationEmails = functions.firestore
  .document('orders/{orderId}')
  .onUpdate(async (change, context) => {
    try {
      const beforeData = change.before.data();
      const afterData = change.after.data();
      const orderId = context.params.orderId;

      // Only send emails when payment status changes to 'completed'
      if (beforeData.paymentStatus === 'completed' || afterData.paymentStatus !== 'completed') {
        console.log(`Payment status for order ${orderId} is not newly completed (before: ${beforeData.paymentStatus}, after: ${afterData.paymentStatus}), skipping email`);
        return null;
      }

      console.log(`Payment completed for order ${orderId}, sending confirmation emails`);

      // Check if email has already been sent for this order
      if (afterData.emailSent === true) {
        console.log(`Email already sent for order ${orderId}, skipping`);
        return null;
      }

      if (!afterData.userId) {
        console.log('No user ID found in order data, skipping email');
        return null;
      }

      // Get buyer information
      const userDoc = await admin.firestore().collection('users').doc(afterData.userId).get();
      if (!userDoc.exists) {
        console.log(`User document not found for user ID: ${afterData.userId}`);
        return null;
      }

      const userData = userDoc.data();
      const buyerEmail = userData.email;
      const buyerName = userData.name || userData.displayName || buyerEmail.split('@')[0];

      if (!buyerEmail) {
        console.log('No buyer email found, skipping email');
        return null;
      }

      // Prepare order data for email
      const emailData = {
        orderId,
        buyerName,
        items: afterData.items || [],
        totalAmount: afterData.totalAmount || 0,
        shippingAddress: afterData.shippingAddress || {},
        orderDate: new Date().toLocaleDateString(),
        estimatedDelivery: getEstimatedDeliveryDate(),
        orderStatus: afterData.orderStatus || 'processing',
        paymentStatus: afterData.paymentStatus || 'completed'
      };

      // Create a Brevo email API instance
      const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

      // Send email to buyer (only if this is not part of a multi-seller order)
      if (!afterData.relatedOrderId) {
        await sendOrderConfirmationToBuyer(apiInstance, buyerEmail, emailData);
      }

      // Send email to seller (only if email hasn't been sent already)
      if (!afterData.emailSent) {
        await sendOrderNotificationsToSellers(apiInstance, afterData, emailData);

        // Mark the order as having had its email sent
        await admin.firestore().collection('orders').doc(orderId).update({
          emailSent: true
        });
      }

      // Send order notification to admin with complete order data
      const adminOrderData = {
        ...emailData,
        orderId,
        buyerEmail,
        buyerName,
        sellerId: afterData.sellerId,
        sellerName: afterData.sellerName,
        sellerEmail: afterData.sellerEmail,
        paymentId: afterData.paymentId,
        razorpayOrderId: afterData.razorpayOrderId
      };
      await sendOrderNotificationToAdmin(apiInstance, adminOrderData);

      return null;
    } catch (error) {
      console.error('Error sending order confirmation emails:', error);
      return null;
    }
  });

/**
 * Cloud Function that handles order creation for backward compatibility
 * This ensures that orders created with payment already completed still get emails
 */
exports.handleOrderCreation = functions.firestore
  .document('orders/{orderId}')
  .onCreate(async (snapshot, context) => {
    try {
      const orderData = snapshot.data();
      const orderId = context.params.orderId;

      // If the order is created with payment already completed, trigger email sending
      if (orderData.paymentStatus === 'completed' && !orderData.emailSent) {
        console.log(`Order ${orderId} created with completed payment, triggering email`);

        // Update the order to trigger the onUpdate function for email sending
        await admin.firestore().collection('orders').doc(orderId).update({
          emailTriggerTimestamp: admin.firestore.FieldValue.serverTimestamp()
        });
      }

      return null;
    } catch (error) {
      console.error('Error handling order creation:', error);
      return null;
    }
  });

/**
 * Send order confirmation email to buyer
 */
async function sendOrderConfirmationToBuyer(apiInstance, buyerEmail, orderData) {
  try {
    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: buyerEmail }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };

    // Check if we have multiple orders
    const hasMultipleOrders = orderData.orderIds && Array.isArray(orderData.orderIds) && orderData.orderIds.length > 1;

    // Create a simple subject line without order numbers
    const subjectLine = `Your SwipeSense Order Confirmation`;

    sendSmtpEmail.subject = subjectLine;

    // Make sure we calculate the total amount if it's not provided
    if (!orderData.totalAmount || orderData.totalAmount === 0) {
      // Calculate from items
      const calculatedTotal = (orderData.items || []).reduce((total, item) => {
        return total + ((item.price || 0) * (item.quantity || 1));
      }, 0);

      console.log(`Calculated total amount for buyer email: ${calculatedTotal} (original: ${orderData.totalAmount})`);
      orderData.totalAmount = calculatedTotal;
    }

    // Collect seller names for the email
    // If we have multiple orders, try to get seller names from the database
    const sellerNames = [];
    if (orderData.orderIds && Array.isArray(orderData.orderIds) && orderData.orderIds.length > 0) {
      try {
        // For each order, try to get the seller name
        for (const orderId of orderData.orderIds) {
          const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
          if (orderDoc.exists) {
            const orderData = orderDoc.data();
            if (orderData.sellerId) {
              // Get seller info
              const sellerDoc = await admin.firestore().collection('users').doc(orderData.sellerId).get();
              if (sellerDoc.exists) {
                const sellerData = sellerDoc.data();
                const sellerName = sellerData.name || sellerData.displayName || sellerData.email?.split('@')[0] || 'Seller';
                sellerNames.push(sellerName);
              }
            } else if (orderData.sellerName) {
              // If seller name is directly stored in the order
              sellerNames.push(orderData.sellerName);
            }
          }
        }
      } catch (error) {
        console.error('Error collecting seller names:', error);
      }
    }

    // Add seller names to the order data
    orderData.sellerNames = sellerNames;
    console.log(`Collected seller names for buyer email: ${sellerNames.join(', ') || 'None found'}`);

    // Generate the email HTML
    sendSmtpEmail.htmlContent = generateOrderConfirmationEmail(orderData);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'order-confirmation',
      'X-Mailin-Tag': 'OrderConfirmation',
      'X-Order-Count': hasMultipleOrders ? orderData.orderIds.length.toString() : '1'
    };

    // Add tags for better tracking
    sendSmtpEmail.tags = ['order-confirmation', 'buyer-notification'];
    if (hasMultipleOrders) {
      sendSmtpEmail.tags.push('multi-seller-order');
    }

    // Send email using Brevo API
    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Order confirmation email sent to buyer: ${buyerEmail}`);
    return result;
  } catch (error) {
    console.error('Error sending order confirmation to buyer:', error);
    return null;
  }
}

/**
 * Send order notifications to sellers
 */
async function sendOrderNotificationsToSellers(apiInstance, orderData, emailData) {
  try {
    // If this order already has a specific seller ID, only send to that seller
    if (orderData.sellerId) {
      console.log(`Order ${emailData.orderId} is already assigned to seller ${orderData.sellerId}`);

      const sellerDoc = await admin.firestore().collection('users').doc(orderData.sellerId).get();
      if (sellerDoc.exists) {
        const sellerData = sellerDoc.data();
        const sellerEmail = sellerData.email;

        if (sellerEmail) {
          // We already have the items filtered for this seller
          const sellerItems = orderData.items || [];

          console.log(`Sending notification for ${sellerItems.length} items to seller ${orderData.sellerId}`);

          // Calculate total for this seller's items
          const sellerTotal = sellerItems.reduce((total, item) => {
            return total + (item.price || 0) * (item.quantity || 1);
          }, 0);

          // Get the seller-specific order ID
          const sellerOrderId = await getSellerSpecificOrderId(emailData.orderId, orderData.sellerId);
          console.log(`Using order ID ${sellerOrderId} for seller ${orderData.sellerId}`);

          // Prepare seller-specific data
          const sellerEmailData = {
            ...emailData,
            items: sellerItems,
            totalAmount: sellerTotal,
            sellerName: sellerData.name || sellerData.displayName || sellerEmail.split('@')[0],
            sellerId: orderData.sellerId, // Include seller ID for tracking
            // CRITICAL FIX: Use the document ID of this specific order, not the main order ID
            // For orders with a specific sellerId, the document ID is the correct order ID
            orderId: sellerOrderId,
            // Add the document ID explicitly to ensure it's used in the email
            documentId: sellerOrderId
          };

          // Send email to seller
          await sendOrderNotificationToSeller(apiInstance, sellerEmail, sellerEmailData);
        }
      }

      return true;
    }

    // For orders without a specific seller ID, determine sellers from items
    const items = orderData.items || [];
    const sellerIds = new Set();
    const itemSellerMap = new Map(); // Map to store item ID to seller ID mapping

    // Get unique seller IDs from items and build item-seller mapping
    for (const item of items) {
      if (item.itemId) {
        const itemDoc = await admin.firestore().collection('clothingItems').doc(item.itemId).get();
        if (itemDoc.exists) {
          const itemData = itemDoc.data();
          if (itemData.uploaderId) {
            sellerIds.add(itemData.uploaderId);
            // Store the seller ID for each item
            itemSellerMap.set(item.itemId, itemData.uploaderId);
          }
        }
      }
    }

    console.log(`Found ${sellerIds.size} sellers for this order with ${items.length} items`);

    // Send notification to each seller
    for (const sellerId of sellerIds) {
      const sellerDoc = await admin.firestore().collection('users').doc(sellerId).get();
      if (sellerDoc.exists) {
        const sellerData = sellerDoc.data();
        const sellerEmail = sellerData.email;

        if (sellerEmail) {
          // Filter items for this seller only
          const sellerItems = items.filter(item => {
            // Only include items uploaded by this seller
            return itemSellerMap.get(item.itemId) === sellerId;
          });

          console.log(`Filtered ${sellerItems.length} items for seller ${sellerId}`);

          // Calculate total for this seller's items only
          const sellerTotal = sellerItems.reduce((total, item) => {
            return total + (item.price || 0) * (item.quantity || 1);
          }, 0);

          // Get the seller-specific order ID
          const sellerOrderId = await getSellerSpecificOrderId(emailData.orderId, sellerId);
          console.log(`Using order ID ${sellerOrderId} for seller ${sellerId}`);

          // Prepare seller-specific data
          const sellerEmailData = {
            ...emailData,
            items: sellerItems,
            totalAmount: sellerTotal,
            sellerName: sellerData.name || sellerData.displayName || sellerEmail.split('@')[0],
            sellerId: sellerId, // Include seller ID for tracking
            // CRITICAL FIX: For multi-seller orders, we need to check if there's a separate order document
            // for this seller and use that document ID as the order ID
            orderId: sellerOrderId,
            // Add the document ID explicitly to ensure it's used in the email
            documentId: sellerOrderId
          };

          // Send email to seller
          await sendOrderNotificationToSeller(apiInstance, sellerEmail, sellerEmailData);
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error sending order notifications to sellers:', error);
    return null;
  }
}

/**
 * Send order notification email to a seller
 */
async function sendOrderNotificationToSeller(apiInstance, sellerEmail, orderData) {
  try {
    // Generate a unique identifier for this email to prevent duplicates
    const uniqueId = `${Date.now()}`;

    // CRITICAL FIX: Ensure we have the correct order ID for this specific seller
    // For multi-seller orders, we must use the document ID of the seller's specific order
    // NOT the main order ID or related order ID
    // Priority: documentId > orderId > id
    let orderIdForEmail = orderData.documentId || orderData.orderId;

    // Double-check that we have a valid order ID
    if (!orderIdForEmail) {
      console.error('Missing order ID for seller email:', sellerEmail);
      // Try to get a valid order ID from the data
      if (orderData.id) {
        console.log(`Using orderData.id (${orderData.id}) as fallback for seller email`);
        orderIdForEmail = orderData.id;
      } else {
        console.error('No valid order ID found for seller email, using timestamp as fallback');
        orderIdForEmail = `TEMP-${Date.now()}`;
      }
    }

    // Log the order ID being used for debugging
    console.log(`Preparing email for seller ${sellerEmail} with order ID: ${orderIdForEmail}`);

    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: sellerEmail }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };

    // Use buyer's name in the subject line instead of order ID
    const buyerName = orderData.buyerName || 'Customer';
    sendSmtpEmail.subject = `New Order from ${buyerName}`;

    // Create a copy of orderData with the correct orderId to ensure the email template uses it
    const orderDataForEmail = {
      ...orderData,
      // Ensure the orderId is set correctly for the email template
      orderId: orderIdForEmail
    };

    sendSmtpEmail.htmlContent = generateSellerOrderNotificationEmail(orderDataForEmail);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'order-notification',
      'X-Mailin-Tag': 'SellerOrderNotification',
      'X-Order-Id': orderIdForEmail, // Add order ID to headers for tracking
      'X-Unique-Id': uniqueId, // Add unique ID to prevent duplicates
      'X-Seller-Id': orderData.sellerId || 'unknown', // Add seller ID for tracking
      'X-Document-Id': orderData.documentId || orderIdForEmail // Add document ID for tracking
    };

    // Add tags for better tracking
    sendSmtpEmail.tags = ['order-notification', 'seller-notification'];

    // Send email using Brevo API
    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Order notification email sent to seller: ${sellerEmail} for order: ${orderIdForEmail}`);
    return result;
  } catch (error) {
    console.error('Error sending order notification to seller:', error);
    return null;
  }
}

/**
 * Generate HTML content for order confirmation email
 */
function generateOrderConfirmationEmail(orderData) {
  // Extract data with fallbacks for safety
  const {
    buyerName,
    items,
    totalAmount,
    shippingAddress,
    orderDate,
    estimatedDelivery,
    sellerNames // Array of seller names (we'll need to extract this)
  } = orderData;

  // Format items HTML
  const itemsHtml = items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <img src="${item.imageUrl || ''}" alt="${item.title || 'Product'}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <p style="margin: 0; font-weight: bold;">${item.title || 'Product'}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">${item.brand || ''} ${item.size ? `- Size: ${item.size}` : ''}</p>
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
        <p style="margin: 0;">₹${(item.price || 0).toFixed(0)}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">Qty: ${item.quantity || 1}</p>
      </td>
    </tr>
  `).join('');

  // Format address
  const formattedAddress = shippingAddress ? `
    ${shippingAddress.street || ''},<br>
    ${shippingAddress.city || ''}, ${shippingAddress.state || ''} ${shippingAddress.zipCode || ''},<br>
    ${shippingAddress.country || ''},<br>
    Phone: ${shippingAddress.phone || ''}
  ` : 'No address provided';

  // Calculate total amount if it's not provided or is zero
  let displayTotal = totalAmount;
  if (!displayTotal || displayTotal === 0) {
    // Calculate from items
    const calculatedTotal = items.reduce((total, item) => {
      return total + ((item.price || 0) * (item.quantity || 1));
    }, 0);
    displayTotal = calculatedTotal;
    console.log(`Calculated total amount: ${calculatedTotal} (original value was: ${totalAmount})`);
  }

  // Extract seller names from items if not provided
  let sellerNamesArray = [];
  if (sellerNames && Array.isArray(sellerNames)) {
    sellerNamesArray = sellerNames;
  } else {
    // Try to extract seller names from the items
    // This is a fallback and may not work in all cases
    const sellerNamesSet = new Set();
    items.forEach(item => {
      if (item.sellerName) {
        sellerNamesSet.add(item.sellerName);
      }
    });
    sellerNamesArray = Array.from(sellerNamesSet);
  }

  // Create sellers HTML section
  let sellersHtml = '';
  if (sellerNamesArray.length > 0) {
    sellersHtml = `
      <div class="order-summary">
        <p><strong>Sellers:</strong> ${sellerNamesArray.join(', ')}</p>
        <p><strong>Order Date:</strong> ${orderDate}</p>
        <p><strong>Estimated Delivery:</strong> ${estimatedDelivery}</p>
        ${sellerNamesArray.length > 1 ? '<p><em>Each seller will process their portion of your order separately.</em></p>' : ''}
      </div>
    `;
  } else {
    // Fallback if no seller names are available
    sellersHtml = `
      <div class="order-summary">
        <p><strong>Order Date:</strong> ${orderDate}</p>
        <p><strong>Estimated Delivery:</strong> ${estimatedDelivery}</p>
      </div>
    `;
  }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: #4CAF50;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .order-summary {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        .total-row {
          font-weight: bold;
          font-size: 16px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Order Confirmation</h1>
        </div>
        <div class="content">
          <p>Hello ${buyerName},</p>
          <p>Thank you for your order! We're excited to confirm that your order has been received and is being processed.</p>

          ${sellersHtml}

          <h2>Order Details</h2>
          <table>
            <thead>
              <tr>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Item</th>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Description</th>
                <th style="text-align: right; padding: 10px; border-bottom: 2px solid #eee;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr class="total-row">
                <td colspan="2" style="padding: 15px; text-align: right;">Total:</td>
                <td style="padding: 15px; text-align: right;">₹${displayTotal.toFixed(0)}</td>
              </tr>
            </tbody>
          </table>

          <h2>Shipping Address</h2>
          <p>${formattedAddress}</p>

          <p>If you have any questions about your order, please contact our customer service team.</p>

          <p>Best regards,<br>SwipeSense Team</p>
        </div>
        <div class="footer">
          <p>This is an automated message from the SwipeSense system.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate HTML content for seller order notification email
 */
function generateSellerOrderNotificationEmail(orderData) {
  // Extract buyer name and other data
  const { items, totalAmount, shippingAddress, orderDate, sellerName, sellerId, buyerName } = orderData;

  // Get buyer name with fallback
  const buyerNameDisplay = buyerName || 'Customer';

  // Log the seller and buyer information for debugging
  console.log(`Generating email template for seller: ${sellerId || 'unknown'} with order from buyer: ${buyerNameDisplay}`);

  // Format items HTML
  const itemsHtml = items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <img src="${item.imageUrl || ''}" alt="${item.title || 'Product'}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <p style="margin: 0; font-weight: bold;">${item.title || 'Product'}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">${item.brand || ''} ${item.size ? `- Size: ${item.size}` : ''}</p>
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
        <p style="margin: 0;">₹${(item.price || 0).toFixed(2)}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">Qty: ${item.quantity || 1}</p>
      </td>
    </tr>
  `).join('');

  // Format address
  const formattedAddress = shippingAddress ? `
    ${shippingAddress.street || ''},<br>
    ${shippingAddress.city || ''}, ${shippingAddress.state || ''} ${shippingAddress.zipCode || ''},<br>
    ${shippingAddress.country || ''},<br>
    Phone: ${shippingAddress.phone || ''}
  ` : 'No address provided';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: #4CAF50;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .order-summary {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .buyer-name {
          font-size: 18px;
          font-weight: bold;
          color: #4CAF50;
          margin-bottom: 10px;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        .total-row {
          font-weight: bold;
          font-size: 16px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Order from ${buyerNameDisplay}</h1>
        </div>
        <div class="content">
          <p>Hello ${sellerName},</p>
          <p>You have received a new order from <span class="buyer-name">${buyerNameDisplay}</span>! Please process it as soon as possible.</p>

          <div class="order-summary">
            <p><strong>Order Date:</strong> ${orderDate}</p>
          </div>

          <h2>Order Details</h2>
          <table>
            <thead>
              <tr>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Item</th>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Description</th>
                <th style="text-align: right; padding: 10px; border-bottom: 2px solid #eee;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr class="total-row">
                <td colspan="2" style="padding: 15px; text-align: right;">Total:</td>
                <td style="padding: 15px; text-align: right;">₹${totalAmount.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>

          <h2>Shipping Address</h2>
          <p>${formattedAddress}</p>

          <p>Please update the order status in your seller dashboard once you've processed this order.</p>

          <p>Best regards,<br>SwipeSense Team</p>
        </div>
        <div class="footer">
          <p>This is an automated message from the SwipeSense system.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Send order notifications to sellers
 */
async function sendOrderNotificationsToSellers(apiInstance, orderData, emailData) {
  try {
    // If this order already has a specific seller ID, only send to that seller
    if (orderData.sellerId) {
      console.log(`Order ${emailData.orderId} is already assigned to seller ${orderData.sellerId}`);

      const sellerDoc = await admin.firestore().collection('users').doc(orderData.sellerId).get();
      if (sellerDoc.exists) {
        const sellerData = sellerDoc.data();
        const sellerEmail = sellerData.email;

        if (sellerEmail) {
          // We already have the items filtered for this seller
          const sellerItems = orderData.items || [];

          console.log(`Sending notification for ${sellerItems.length} items to seller ${orderData.sellerId}`);

          // Calculate total for this seller's items
          const sellerTotal = sellerItems.reduce((total, item) => {
            return total + (item.price || 0) * (item.quantity || 1);
          }, 0);

          // Get the seller-specific order ID
          const sellerOrderId = await getSellerSpecificOrderId(emailData.orderId, orderData.sellerId);
          console.log(`Using order ID ${sellerOrderId} for seller ${orderData.sellerId}`);

          // Prepare seller-specific data
          const sellerEmailData = {
            ...emailData,
            items: sellerItems,
            totalAmount: sellerTotal,
            sellerName: sellerData.name || sellerData.displayName || sellerEmail.split('@')[0],
            sellerId: orderData.sellerId, // Include seller ID for tracking
            // CRITICAL FIX: Use the document ID of this specific order, not the main order ID
            // For orders with a specific sellerId, the document ID is the correct order ID
            orderId: sellerOrderId,
            // Add the document ID explicitly to ensure it's used in the email
            documentId: sellerOrderId
          };

          // Send email to seller
          await sendOrderNotificationToSeller(apiInstance, sellerEmail, sellerEmailData);
        }
      }

      return true;
    }

    // For orders without a specific seller ID, determine sellers from items
    const items = orderData.items || [];
    const sellerIds = new Set();
    const itemSellerMap = new Map(); // Map to store item ID to seller ID mapping

    // Get unique seller IDs from items and build item-seller mapping
    for (const item of items) {
      if (item.itemId) {
        const itemDoc = await admin.firestore().collection('clothingItems').doc(item.itemId).get();
        if (itemDoc.exists) {
          const itemData = itemDoc.data();
          if (itemData.uploaderId) {
            sellerIds.add(itemData.uploaderId);
            // Store the seller ID for each item
            itemSellerMap.set(item.itemId, itemData.uploaderId);
          }
        }
      }
    }

    console.log(`Found ${sellerIds.size} sellers for this order with ${items.length} items`);

    // Send notification to each seller
    for (const sellerId of sellerIds) {
      const sellerDoc = await admin.firestore().collection('users').doc(sellerId).get();
      if (sellerDoc.exists) {
        const sellerData = sellerDoc.data();
        const sellerEmail = sellerData.email;

        if (sellerEmail) {
          // Filter items for this seller only
          const sellerItems = items.filter(item => {
            // Only include items uploaded by this seller
            return itemSellerMap.get(item.itemId) === sellerId;
          });

          console.log(`Filtered ${sellerItems.length} items for seller ${sellerId}`);

          // Calculate total for this seller's items only
          const sellerTotal = sellerItems.reduce((total, item) => {
            return total + (item.price || 0) * (item.quantity || 1);
          }, 0);

          // Get the seller-specific order ID
          const sellerOrderId = await getSellerSpecificOrderId(emailData.orderId, sellerId);
          console.log(`Using order ID ${sellerOrderId} for seller ${sellerId}`);

          // Prepare seller-specific data
          const sellerEmailData = {
            ...emailData,
            items: sellerItems,
            totalAmount: sellerTotal,
            sellerName: sellerData.name || sellerData.displayName || sellerEmail.split('@')[0],
            sellerId: sellerId, // Include seller ID for tracking
            // CRITICAL FIX: For multi-seller orders, we need to check if there's a separate order document
            // for this seller and use that document ID as the order ID
            orderId: sellerOrderId,
            // Add the document ID explicitly to ensure it's used in the email
            documentId: sellerOrderId
          };

          // Send email to seller
          await sendOrderNotificationToSeller(apiInstance, sellerEmail, sellerEmailData);
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error sending order notifications to sellers:', error);
    return null;
  }
}

/**
 * Send order notification email to a seller
 */
async function sendOrderNotificationToSeller(apiInstance, sellerEmail, orderData) {
  try {
    // Generate a unique identifier for this email to prevent duplicates
    const uniqueId = `${Date.now()}`;

    // CRITICAL FIX: Ensure we have the correct order ID for this specific seller
    // For multi-seller orders, we must use the document ID of the seller's specific order
    // NOT the main order ID or related order ID
    // Priority: documentId > orderId > id
    let orderIdForEmail = orderData.documentId || orderData.orderId;

    // Double-check that we have a valid order ID
    if (!orderIdForEmail) {
      console.error('Missing order ID for seller email:', sellerEmail);
      // Try to get a valid order ID from the data
      if (orderData.id) {
        console.log(`Using orderData.id (${orderData.id}) as fallback for seller email`);
        orderIdForEmail = orderData.id;
      } else {
        console.error('No valid order ID found for seller email, using timestamp as fallback');
        orderIdForEmail = `TEMP-${Date.now()}`;
      }
    }

    // Log the order ID being used for debugging
    console.log(`Preparing email for seller ${sellerEmail} with order ID: ${orderIdForEmail}`);

    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: sellerEmail }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };

    // Use buyer's name in the subject line instead of order ID
    const buyerName = orderData.buyerName || 'Customer';
    sendSmtpEmail.subject = `New Order from ${buyerName}`;

    // Create a copy of orderData with the correct orderId to ensure the email template uses it
    const orderDataForEmail = {
      ...orderData,
      // Ensure the orderId is set correctly for the email template
      orderId: orderIdForEmail
    };

    sendSmtpEmail.htmlContent = generateSellerOrderNotificationEmail(orderDataForEmail);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'order-notification',
      'X-Mailin-Tag': 'SellerOrderNotification',
      'X-Order-Id': orderIdForEmail, // Add order ID to headers for tracking
      'X-Unique-Id': uniqueId, // Add unique ID to prevent duplicates
      'X-Seller-Id': orderData.sellerId || 'unknown', // Add seller ID for tracking
      'X-Document-Id': orderData.documentId || orderIdForEmail // Add document ID for tracking
    };

    // Add tags for better tracking
    sendSmtpEmail.tags = ['order-notification', 'seller-notification'];

    // Send email using Brevo API
    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Order notification email sent to seller: ${sellerEmail} for order: ${orderIdForEmail}`);
    return result;
  } catch (error) {
    console.error('Error sending order notification to seller:', error);
    return null;
  }
}

/**
 * Generate HTML content for order confirmation email
 */
function generateOrderConfirmationEmail(orderData) {
  // Extract data with fallbacks for safety
  const {
    buyerName,
    items,
    totalAmount,
    shippingAddress,
    orderDate,
    estimatedDelivery,
    sellerNames // Array of seller names (we'll need to extract this)
  } = orderData;

  // Format items HTML
  const itemsHtml = items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <img src="${item.imageUrl || ''}" alt="${item.title || 'Product'}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <p style="margin: 0; font-weight: bold;">${item.title || 'Product'}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">${item.brand || ''} ${item.size ? `- Size: ${item.size}` : ''}</p>
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
        <p style="margin: 0;">₹${(item.price || 0).toFixed(0)}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">Qty: ${item.quantity || 1}</p>
      </td>
    </tr>
  `).join('');

  // Format address
  const formattedAddress = shippingAddress ? `
    ${shippingAddress.street || ''},<br>
    ${shippingAddress.city || ''}, ${shippingAddress.state || ''} ${shippingAddress.zipCode || ''},<br>
    ${shippingAddress.country || ''},<br>
    Phone: ${shippingAddress.phone || ''}
  ` : 'No address provided';

  // Calculate total amount if it's not provided or is zero
  let displayTotal = totalAmount;
  if (!displayTotal || displayTotal === 0) {
    // Calculate from items
    const calculatedTotal = items.reduce((total, item) => {
      return total + ((item.price || 0) * (item.quantity || 1));
    }, 0);
    displayTotal = calculatedTotal;
    console.log(`Calculated total amount: ${calculatedTotal} (original value was: ${totalAmount})`);
  }

  // Extract seller names from items if not provided
  let sellerNamesArray = [];
  if (sellerNames && Array.isArray(sellerNames)) {
    sellerNamesArray = sellerNames;
  } else {
    // Try to extract seller names from the items
    // This is a fallback and may not work in all cases
    const sellerNamesSet = new Set();
    items.forEach(item => {
      if (item.sellerName) {
        sellerNamesSet.add(item.sellerName);
      }
    });
    sellerNamesArray = Array.from(sellerNamesSet);
  }

  // Create sellers HTML section
  let sellersHtml = '';
  if (sellerNamesArray.length > 0) {
    sellersHtml = `
      <div class="order-summary">
        <p><strong>Sellers:</strong> ${sellerNamesArray.join(', ')}</p>
        <p><strong>Order Date:</strong> ${orderDate}</p>
        <p><strong>Estimated Delivery:</strong> ${estimatedDelivery}</p>
        ${sellerNamesArray.length > 1 ? '<p><em>Each seller will process their portion of your order separately.</em></p>' : ''}
      </div>
    `;
  } else {
    // Fallback if no seller names are available
    sellersHtml = `
      <div class="order-summary">
        <p><strong>Order Date:</strong> ${orderDate}</p>
        <p><strong>Estimated Delivery:</strong> ${estimatedDelivery}</p>
      </div>
    `;
  }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: #4CAF50;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .order-summary {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        .total-row {
          font-weight: bold;
          font-size: 16px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Order Confirmation</h1>
        </div>
        <div class="content">
          <p>Hello ${buyerName},</p>
          <p>Thank you for your order! We're excited to confirm that your order has been received and is being processed.</p>

          ${sellersHtml}

          <h2>Order Details</h2>
          <table>
            <thead>
              <tr>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Item</th>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Description</th>
                <th style="text-align: right; padding: 10px; border-bottom: 2px solid #eee;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr class="total-row">
                <td colspan="2" style="padding: 15px; text-align: right;">Total:</td>
                <td style="padding: 15px; text-align: right;">₹${displayTotal.toFixed(0)}</td>
              </tr>
            </tbody>
          </table>

          <h2>Shipping Address</h2>
          <p>${formattedAddress}</p>

          <p>If you have any questions about your order, please contact our customer service team.</p>

          <p>Best regards,<br>SwipeSense Team</p>
        </div>
        <div class="footer">
          <p>This is an automated message from the SwipeSense system.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Send order notification to admin
 */
const sendOrderNotificationToAdmin = async (apiInstance, orderData) => {
  try {
    console.log('[OrderEmails] Sending enhanced order notification to admin for order:', orderData.orderId);
    console.log('[OrderEmails] Order data received:', JSON.stringify(orderData, null, 2));

    const adminEmail = '<EMAIL>';
    const orderNumber = (orderData.orderId || 'N/A').slice(-8).toUpperCase();
    const totalAmount = `₹${(orderData.totalAmount || 0).toFixed(2)}`;

    // Get complete order information from Firestore
    let completeOrderData = {};
    let sellersInfo = [];
    let itemsDetails = [];

    try {
      const orderDoc = await admin.firestore().collection('orders').doc(orderData.orderId).get();
      if (orderDoc.exists) {
        completeOrderData = orderDoc.data();
        console.log('[OrderEmails] Complete order data retrieved:', JSON.stringify(completeOrderData, null, 2));

        // Get seller information for each item
        if (completeOrderData.items && Array.isArray(completeOrderData.items)) {
          console.log('[OrderEmails] Found items in order:', completeOrderData.items.length);
          const sellerIds = [...new Set(completeOrderData.items.map(item => item.uploaderId || item.sellerId).filter(Boolean))];
          console.log('[OrderEmails] Unique seller IDs found:', sellerIds);

          for (const sellerId of sellerIds) {
            try {
              console.log('[OrderEmails] Fetching seller data for:', sellerId);
              const sellerDoc = await admin.firestore().collection('users').doc(sellerId).get();
              if (sellerDoc.exists) {
                const sellerData = sellerDoc.data();
                console.log('[OrderEmails] Seller data retrieved for', sellerId, ':', JSON.stringify(sellerData, null, 2));
                sellersInfo.push({
                  id: sellerId,
                  name: sellerData.name || 'Unknown Seller',
                  email: sellerData.email || 'N/A',
                  phone: sellerData.phoneNumber || 'N/A',
                  itemCount: completeOrderData.items.filter(item => (item.uploaderId || item.sellerId) === sellerId).length
                });
              } else {
                console.warn('[OrderEmails] Seller document not found for ID:', sellerId);
                sellersInfo.push({
                  id: sellerId,
                  name: 'Unknown Seller (Doc Not Found)',
                  email: 'N/A',
                  phone: 'N/A',
                  itemCount: completeOrderData.items.filter(item => (item.uploaderId || item.sellerId) === sellerId).length
                });
              }
            } catch (sellerError) {
              console.error(`[OrderEmails] Error fetching seller ${sellerId}:`, sellerError);
              sellersInfo.push({
                id: sellerId,
                name: 'Unknown Seller (Error)',
                email: 'N/A',
                phone: 'N/A',
                itemCount: completeOrderData.items.filter(item => (item.uploaderId || item.sellerId) === sellerId).length
              });
            }
          }

          // Prepare items details
          itemsDetails = completeOrderData.items.map(item => ({
            title: item.title || 'Unknown Item',
            price: `₹${(item.price || 0).toFixed(2)}`,
            quantity: item.quantity || 1,
            size: item.size || 'N/A',
            color: item.color || 'N/A',
            category: item.category || 'N/A'
          }));
          console.log('[OrderEmails] Items details prepared:', itemsDetails.length, 'items');
        } else {
          console.warn('[OrderEmails] No items found in order or items is not an array');
        }
      } else {
        console.warn('[OrderEmails] Order document not found:', orderData.orderId);
      }
    } catch (firestoreError) {
      console.error('[OrderEmails] Error fetching complete order data:', firestoreError);
    }

    console.log('[OrderEmails] Final sellers info:', sellersInfo);
    console.log('[OrderEmails] Final items details:', itemsDetails);

    // Format seller information for email
    const sellersHtml = sellersInfo.length > 0 ? sellersInfo.map(seller => `
      <div style="margin: 10px 0; padding: 10px; background-color: #e8f5e8; border-radius: 5px;">
        <p><strong>👤 ${seller.name}</strong></p>
        <p>📧 Email: ${seller.email}</p>
        <p>📱 Phone: ${seller.phone}</p>
        <p>📦 Items: ${seller.itemCount} item(s)</p>
      </div>
    `).join('') : '<p>⚠️ No seller information available</p>';

    // Format items details for email
    const itemsHtml = itemsDetails.length > 0 ? itemsDetails.map((item, index) => `
      <tr style="border-bottom: 1px solid #ddd;">
        <td style="padding: 8px;">${index + 1}</td>
        <td style="padding: 8px;">${item.title}</td>
        <td style="padding: 8px;">${item.price}</td>
        <td style="padding: 8px;">${item.quantity}</td>
        <td style="padding: 8px;">${item.size}</td>
        <td style="padding: 8px;">${item.color}</td>
        <td style="padding: 8px;">${item.category}</td>
      </tr>
    `).join('') : '<tr><td colspan="7" style="padding: 8px; text-align: center;">No items found</td></tr>';

    const subject = `🛍️ New Order Placed - #${orderNumber} (${totalAmount})`;
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2196F3; border-bottom: 2px solid #2196F3; padding-bottom: 10px;">🛍️ New Order Notification</h2>
        <p style="font-size: 16px; color: #333;">A new order has been placed on StyleApp!</p>
        
        <!-- Order Summary -->
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196F3;">
          <h3 style="color: #2196F3; margin-top: 0;">📋 Order Summary</h3>
          <p><strong>Order ID:</strong> #${orderNumber}</p>
          <p><strong>Full Order ID:</strong> ${orderData.orderId}</p>
          <p><strong>Total Amount:</strong> <span style="color: #28a745; font-size: 18px; font-weight: bold;">${totalAmount}</span></p>
          <p><strong>Number of Items:</strong> ${itemsDetails.length} item(s)</p>
          <p><strong>Number of Sellers:</strong> ${sellersInfo.length} seller(s)</p>
          <p><strong>Order Date:</strong> ${new Date().toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}</p>
          <p><strong>Payment ID:</strong> ${orderData.paymentId || 'N/A'}</p>
          <p><strong>Razorpay Order ID:</strong> ${orderData.razorpayOrderId || 'N/A'}</p>
        </div>

        <!-- Buyer Information -->
        <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #1976d2;">
          <h3 style="color: #1976d2; margin-top: 0;">👤 Buyer Information</h3>
          <p><strong>Name:</strong> ${orderData.buyerName || 'N/A'}</p>
          <p><strong>Email:</strong> ${orderData.buyerEmail || 'N/A'}</p>
          <p><strong>User ID:</strong> ${completeOrderData.userId || 'N/A'}</p>
        </div>

        <!-- Sellers Information -->
        <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;">
          <h3 style="color: #4caf50; margin-top: 0;">🏪 Seller(s) Information</h3>
          ${sellersHtml}
        </div>

        <!-- Items Details -->
        <div style="background-color: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff9800;">
          <h3 style="color: #ff9800; margin-top: 0;">📦 Items Details</h3>
          <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
            <thead>
              <tr style="background-color: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">#</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Item</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Price</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Qty</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Size</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Color</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Category</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
            </tbody>
          </table>
        </div>

        <!-- Action Required -->
        <div style="background-color: #fce4ec; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #e91e63;">
          <h3 style="color: #e91e63; margin-top: 0;">⚡ Action Required</h3>
          <p>🔍 <strong>Review:</strong> Please review this order in the admin dashboard</p>
          <p>📋 <strong>Process:</strong> Ensure all sellers are notified and processing their items</p>
          <p>🚚 <strong>Monitor:</strong> Track the order status and delivery progress</p>
          <p>💰 <strong>Payments:</strong> Seller transactions will be created once order is delivered</p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px;">
            Best regards,<br>
            <strong>StyleApp System</strong><br>
            📧 <EMAIL> | 🌐 StyleApp Admin Dashboard
          </p>
        </div>
      </div>
    `;

    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
    sendSmtpEmail.subject = subject;
    sendSmtpEmail.htmlContent = htmlContent;
    sendSmtpEmail.sender = { name: 'StyleApp System', email: '<EMAIL>' };
    sendSmtpEmail.to = [{ email: adminEmail, name: 'StyleApp Admin' }];
    sendSmtpEmail.tags = ['admin-notification', 'new-order', `order-${orderNumber}`];

    // Add custom headers for better email tracking
    sendSmtpEmail.headers = {
      'X-Order-ID': orderData.orderId,
      'X-Order-Total': totalAmount,
      'X-Seller-Count': sellersInfo.length.toString()
    };

    await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log('[OrderEmails] Enhanced admin order notification sent successfully');

  } catch (error) {
    console.error('[OrderEmails] Error sending enhanced admin order notification:', error);
    throw error;
  }
};

/**
 * Callable function to send order notification to admin
 */
exports.sendOrderNotificationToAdminCallable = functions.https.onCall(async (data, context) => {
  try {
    console.log('[OrderEmails] Callable: Received request for sendOrderNotificationToAdmin');

    const { orderData } = data;
    if (!orderData) {
      throw new functions.https.HttpsError('invalid-argument', 'Order data is required');
    }

    const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
    await sendOrderNotificationToAdmin(apiInstance, orderData);

    return { success: true, message: 'Admin order notification sent successfully' };
  } catch (error) {
    console.error('[OrderEmails] Error in callable admin notification function:', error);
    throw new functions.https.HttpsError('internal', error.message || 'Failed to send admin order notification');
  }
});

/**
 * Cloud Function that sends order status update emails when an order status changes
 */
exports.sendOrderStatusUpdateEmails = functions.firestore
  .document('orders/{orderId}')
  .onUpdate(async (change, context) => {
    try {
      const beforeData = change.before.data();
      const afterData = change.after.data();
      const orderId = context.params.orderId;

      // Check if the order status has changed
      if (beforeData.orderStatus === afterData.orderStatus) {
        console.log(`Order status for ${orderId} has not changed, skipping email`);
        return null;
      }

      // Only send emails for specific status changes
      const statusesToNotify = ['shipped', 'delivered', 'cancelled'];
      if (!statusesToNotify.includes(afterData.orderStatus)) {
        console.log(`Order status ${afterData.orderStatus} does not require notification, skipping email`);
        return null;
      }

      console.log(`Order ${orderId} status changed from ${beforeData.orderStatus} to ${afterData.orderStatus}`);

      // Get buyer information
      if (!afterData.userId) {
        console.log('No user ID found in order data, skipping email');
        return null;
      }

      const userDoc = await admin.firestore().collection('users').doc(afterData.userId).get();
      if (!userDoc.exists) {
        console.log(`User document not found for user ID: ${afterData.userId}`);
        return null;
      }

      const userData = userDoc.data();
      const buyerEmail = userData.email;
      const buyerName = userData.name || userData.displayName || buyerEmail.split('@')[0];

      if (!buyerEmail) {
        console.log('No buyer email found, skipping email');
        return null;
      }

      // Get seller information
      let sellerName = 'Seller';
      if (afterData.sellerId) {
        const sellerDoc = await admin.firestore().collection('users').doc(afterData.sellerId).get();
        if (sellerDoc.exists) {
          const sellerData = sellerDoc.data();
          sellerName = sellerData.name || sellerData.displayName || sellerData.email?.split('@')[0] || 'Seller';
        }
      } else if (afterData.sellerName) {
        sellerName = afterData.sellerName;
      }

      // Prepare order data for email
      const emailData = {
        orderId,
        buyerName,
        buyerEmail,
        items: afterData.items || [],
        totalAmount: afterData.totalAmount || 0,
        shippingAddress: afterData.shippingAddress || {},
        orderDate: afterData.createdAt ? new Date(afterData.createdAt.toDate()).toLocaleDateString() : new Date().toLocaleDateString(),
        orderStatus: afterData.orderStatus,
        sellerName: sellerName,
        // Include tracking information if available (for shipped status)
        // Check for both trackingId and trackingNumber fields (different naming conventions)
        trackingId: afterData.trackingId || afterData.trackingNumber || null,
        deliveryService: afterData.deliveryService || null,
        // Include cancellation information if available
        cancellationReason: afterData.cancellationReason || null,
        cancelledBy: afterData.cancelledBy || 'seller',
        cancelledAt: afterData.cancelledAt || null,
        // Include payment information
        paymentMethod: afterData.paymentMethod || 'Razorpay',
        paymentStatus: afterData.paymentStatus || 'Completed'
      };

      // Log tracking information for debugging
      console.log(`Tracking information for order ${orderId}:`, {
        trackingId: emailData.trackingId,
        trackingNumber: afterData.trackingNumber,
        deliveryService: emailData.deliveryService
      });

      // Create a Brevo email API instance
      const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

      // Send email to buyer
      await sendOrderStatusUpdateToBuyer(apiInstance, buyerEmail, emailData);

      // If the order is cancelled, also send an email to the seller
      if (afterData.orderStatus === 'cancelled') {
        // Get seller email
        if (afterData.sellerId) {
          const sellerDoc = await admin.firestore().collection('users').doc(afterData.sellerId).get();
          if (sellerDoc.exists) {
            const sellerData = sellerDoc.data();
            const sellerEmail = sellerData.email;

            if (sellerEmail) {
              console.log(`Sending cancellation notification to seller: ${sellerEmail}`);

              // Create a copy of the email data for the seller
              const sellerEmailData = {
                ...emailData,
                // Add who cancelled the order
                cancelledBy: afterData.cancelledBy || 'buyer'
              };

              // Send email to seller
              await sendOrderCancellationToSeller(apiInstance, sellerEmail, sellerEmailData);
            }
          }
        }
      }

      // Mark the order as having had its status update email sent
      await admin.firestore().collection('orders').doc(orderId).update({
        statusUpdateEmailSent: true,
        statusUpdateEmailSentAt: admin.firestore.FieldValue.serverTimestamp()
      });

      return null;
    } catch (error) {
      console.error('Error sending order status update email:', error);
      return null;
    }
  });

/**
 * Send order cancellation email to seller
 */
async function sendOrderCancellationToSeller(apiInstance, sellerEmail, orderData) {
  try {
    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: sellerEmail }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };

    // Create a subject line for the cancellation
    const subjectLine = `Order Cancelled by ${orderData.cancelledBy === 'seller' ? 'You' : 'Buyer'}`;
    sendSmtpEmail.subject = subjectLine;

    // Generate the email HTML
    sendSmtpEmail.htmlContent = generateSellerCancellationEmail(orderData);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'order-cancellation',
      'X-Mailin-Tag': 'SellerCancellationNotification',
      'X-Order-Id': orderData.orderId,
      'X-Cancelled-By': orderData.cancelledBy || 'buyer'
    };

    // Add tags for better tracking
    sendSmtpEmail.tags = ['order-cancellation', 'seller-notification'];

    // Send email using Brevo API
    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Order cancellation email sent to seller: ${sellerEmail}`);
    return result;
  } catch (error) {
    console.error('Error sending order cancellation to seller:', error);
    return null;
  }
}

/**
 * Generate HTML content for seller cancellation email
 */
function generateSellerCancellationEmail(orderData) {
  // Extract data with fallbacks for safety
  const {
    buyerName,
    items,
    totalAmount,
    orderDate,
    cancellationReason,
    cancelledBy
  } = orderData;

  // Format items HTML
  const itemsHtml = items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <img src="${item.imageUrl || ''}" alt="${item.title || 'Product'}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <p style="margin: 0; font-weight: bold;">${item.title || 'Product'}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">${item.brand || ''} ${item.size ? `- Size: ${item.size}` : ''}</p>
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
        <p style="margin: 0;">₹${(item.price || 0).toFixed(0)}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">Qty: ${item.quantity || 1}</p>
      </td>
    </tr>
  `).join('');

  // Calculate total amount if it's not provided or is zero
  let displayTotal = totalAmount;
  if (!displayTotal || displayTotal === 0) {
    // Calculate from items
    const calculatedTotal = items.reduce((total, item) => {
      return total + ((item.price || 0) * (item.quantity || 1));
    }, 0);
    displayTotal = calculatedTotal;
  }

  // Create cancellation message based on who cancelled
  const cancellationMessage = cancelledBy === 'seller'
    ? 'You have cancelled this order.'
    : `The buyer (${buyerName}) has cancelled this order.`;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: #F44336;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .order-summary {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .cancellation-details {
          margin: 20px 0;
          background-color: #FFEBEE;
          padding: 15px;
          border-radius: 5px;
          border-left: 4px solid #F44336;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        .total-row {
          font-weight: bold;
          font-size: 16px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Order Cancelled</h1>
        </div>
        <div class="content">
          <p>Hello,</p>
          <p>${cancellationMessage}</p>

          <div class="cancellation-details">
            <h3>Cancellation Details</h3>
            ${cancellationReason ? `<p><strong>Reason:</strong> ${cancellationReason}</p>` : ''}
            <p><strong>Cancelled by:</strong> ${cancelledBy === 'seller' ? 'You (Seller)' : 'Buyer'}</p>
            <p><strong>Cancellation Date:</strong> ${new Date().toLocaleDateString()}</p>
          </div>

          <div class="order-summary">
            <p><strong>Order Date:</strong> ${orderDate}</p>
            <p><strong>Buyer:</strong> ${buyerName}</p>
          </div>

          <h2>Order Details</h2>
          <table>
            <thead>
              <tr>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Item</th>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Description</th>
                <th style="text-align: right; padding: 10px; border-bottom: 2px solid #eee;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr class="total-row">
                <td colspan="2" style="padding: 15px; text-align: right;">Total:</td>
                <td style="padding: 15px; text-align: right;">₹${displayTotal.toFixed(0)}</td>
              </tr>
            </tbody>
          </table>

          <p>If you have any questions, please contact our customer service team.</p>

          <p>Best regards,<br>SwipeSense Team</p>
        </div>
        <div class="footer">
          <p>This is an automated message from the SwipeSense system.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Send order status update email to buyer
 */
async function sendOrderStatusUpdateToBuyer(apiInstance, buyerEmail, orderData) {
  try {
    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: buyerEmail }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };

    // Create a subject line based on the order status
    let subjectLine = '';
    switch (orderData.orderStatus) {
      case 'shipped':
        subjectLine = `Your SwipeSense Order Has Been Shipped`;
        break;
      case 'delivered':
        subjectLine = `Your SwipeSense Order Has Been Delivered`;
        break;
      case 'cancelled':
        // For cancelled orders, always use the same subject line regardless of who cancelled
        subjectLine = `Your SwipeSense Order Has Been Cancelled`;
        break;
      default:
        subjectLine = `Your SwipeSense Order Status Update`;
    }

    sendSmtpEmail.subject = subjectLine;

    // Generate the email HTML
    sendSmtpEmail.htmlContent = generateOrderStatusUpdateEmail(orderData);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'order-status-update',
      'X-Mailin-Tag': 'OrderStatusUpdate',
      'X-Order-Status': orderData.orderStatus
    };

    // Add tags for better tracking
    sendSmtpEmail.tags = ['order-status-update', 'buyer-notification', `status-${orderData.orderStatus}`];

    // Send email using Brevo API
    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Order status update email sent to buyer: ${buyerEmail} for status: ${orderData.orderStatus}`);
    return result;
  } catch (error) {
    console.error('Error sending order status update to buyer:', error);
    return null;
  }
}

/**
 * Generate HTML content for order status update email
 */
function generateOrderStatusUpdateEmail(orderData) {
  // Extract data with fallbacks for safety
  const {
    buyerName,
    items,
    totalAmount,
    shippingAddress,
    orderDate,
    orderStatus,
    sellerName,
    trackingId,
    deliveryService,
    cancellationReason
  } = orderData;

  // Format items HTML
  const itemsHtml = items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <img src="${item.imageUrl || ''}" alt="${item.title || 'Product'}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">
        <p style="margin: 0; font-weight: bold;">${item.title || 'Product'}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">${item.brand || ''} ${item.size ? `- Size: ${item.size}` : ''}</p>
      </td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
        <p style="margin: 0;">₹${(item.price || 0).toFixed(0)}</p>
        <p style="margin: 5px 0 0; color: #666; font-size: 12px;">Qty: ${item.quantity || 1}</p>
      </td>
    </tr>
  `).join('');

  // Format address
  const formattedAddress = shippingAddress ? `
    ${shippingAddress.street || ''},<br>
    ${shippingAddress.city || ''}, ${shippingAddress.state || ''} ${shippingAddress.zipCode || ''},<br>
    ${shippingAddress.country || ''},<br>
    Phone: ${shippingAddress.phone || ''}
  ` : 'No address provided';

  // Calculate total amount if it's not provided or is zero
  let displayTotal = totalAmount;
  if (!displayTotal || displayTotal === 0) {
    // Calculate from items
    const calculatedTotal = items.reduce((total, item) => {
      return total + ((item.price || 0) * (item.quantity || 1));
    }, 0);
    displayTotal = calculatedTotal;
  }

  // Create status-specific content
  let statusTitle = '';
  let statusMessage = '';
  let statusDetailsHtml = '';
  let headerColor = '';

  // Check if this is a buyer-initiated cancellation
  const isBuyerCancelled = orderStatus === 'cancelled' && orderData.cancelledBy === 'buyer';

  switch (orderStatus) {
    case 'shipped':
      statusTitle = 'Your Order Has Been Shipped';
      statusMessage = `Great news! Your order has been shipped by ${sellerName}.`;
      headerColor = '#4CAF50'; // Green

      // Add tracking information if available
      if (trackingId && deliveryService) {
        statusDetailsHtml = `
          <div class="status-details">
            <h3>Shipping Information</h3>
            <p><strong>Delivery Service:</strong> ${deliveryService}</p>
            <p><strong>Tracking ID:</strong> ${trackingId}</p>
            <p>You can track your package using the tracking ID provided above.</p>
          </div>
        `;
      } else {
        statusDetailsHtml = `
          <div class="status-details">
            <p>Your order is on its way! The seller has not provided tracking information.</p>
          </div>
        `;
      }
      break;

    case 'delivered':
      statusTitle = 'Your Order Has Been Delivered';
      statusMessage = `Your order has been marked as delivered by ${sellerName}.`;
      headerColor = '#4CAF50'; // Green
      statusDetailsHtml = `
        <div class="status-details">
          <p>We hope you enjoy your purchase! If you have any issues with your order, please contact the seller.</p>
        </div>
      `;
      break;

    case 'cancelled':
      headerColor = '#F44336'; // Red

      if (isBuyerCancelled) {
        // Buyer-initiated cancellation
        statusTitle = 'Your Order Cancellation Confirmation';
        statusMessage = 'You have successfully cancelled your order.';

        // Format cancellation date and time
        const cancellationDate = orderData.cancelledAt
          ? new Date(orderData.cancelledAt instanceof Date ? orderData.cancelledAt : orderData.cancelledAt.toDate())
          : new Date();

        const formattedDate = cancellationDate.toLocaleDateString();
        const formattedTime = cancellationDate.toLocaleTimeString();

        // Add cancellation details
        statusDetailsHtml = `
          <div class="status-details">
            <h3>Cancellation Details</h3>
            <p><strong>Cancelled by:</strong> You</p>
            <p><strong>Cancellation Date:</strong> ${formattedDate}</p>
            <p><strong>Cancellation Time:</strong> ${formattedTime}</p>
            ${cancellationReason ? `<p><strong>Reason for Cancellation:</strong> ${cancellationReason}</p>` : ''}
            <p>Your payment will be refunded according to the payment method's refund policy.</p>
          </div>
        `;
      } else {
        // Seller-initiated cancellation
        statusTitle = 'Your Order Has Been Cancelled';
        statusMessage = `We're sorry, but your order has been cancelled by ${sellerName}.`;

        // Add cancellation reason if available
        if (cancellationReason) {
          statusDetailsHtml = `
            <div class="status-details">
              <h3>Cancellation Reason</h3>
              <p>${cancellationReason}</p>
            </div>
          `;
        } else {
          statusDetailsHtml = `
            <div class="status-details">
              <p>No specific reason was provided for the cancellation. If you have any questions, please contact the seller.</p>
            </div>
          `;
        }
      }
      break;

    default:
      statusTitle = 'Your Order Status Has Been Updated';
      statusMessage = `Your order status has been updated to: ${orderStatus}`;
      headerColor = '#2196F3'; // Blue
      statusDetailsHtml = `
        <div class="status-details">
          <p>If you have any questions about this status update, please contact the seller.</p>
        </div>
      `;
  }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: ${headerColor};
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .order-summary {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .status-details {
          margin: 20px 0;
          background-color: #f9f9f9;
          padding: 15px;
          border-radius: 5px;
          border-left: 4px solid ${headerColor};
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        .total-row {
          font-weight: bold;
          font-size: 16px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${statusTitle}</h1>
        </div>
        <div class="content">
          <p>Hello ${buyerName},</p>
          <p>${statusMessage}</p>

          ${statusDetailsHtml}

          <div class="order-summary">
            <p><strong>Seller:</strong> ${sellerName}</p>
            <p><strong>Order Date:</strong> ${orderDate}</p>
          </div>

          <h2>Order Details</h2>
          <table>
            <thead>
              <tr>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Item</th>
                <th style="text-align: left; padding: 10px; border-bottom: 2px solid #eee;">Description</th>
                <th style="text-align: right; padding: 10px; border-bottom: 2px solid #eee;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr class="total-row">
                <td colspan="2" style="padding: 15px; text-align: right;">Total:</td>
                <td style="padding: 15px; text-align: right;">₹${displayTotal.toFixed(0)}</td>
              </tr>
            </tbody>
          </table>

          <h2>Shipping Address</h2>
          <p>${formattedAddress}</p>

          <p>If you have any questions about your order, please contact our customer service team.</p>

          <p>Best regards,<br>SwipeSense Team</p>
        </div>
        <div class="footer">
          <p>This is an automated message from the SwipeSense system.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Get an estimated delivery date (7 days from now)
 */
function getEstimatedDeliveryDate() {
  const date = new Date();
  date.setDate(date.getDate() + 7);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}