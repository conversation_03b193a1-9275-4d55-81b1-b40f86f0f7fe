import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase.config';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Enhanced Recommendation Engine using Cosine Similarity
 * with Hierarchical Categories and User Preferences
 */
export class RecommendationEngine {
  constructor() {
    // Feature weights for similarity calculation
    this.featureWeights = {
      broadCategory: 0.30,      // High weight for primary category
      detailedCategory: 0.22,   // Medium-high weight for sub-category
      colors: 0.15,            // Medium weight for color similarity
      tags: 0.18,              // Medium weight for tags similarity
      brand: 0.08,             // Reduced weight
      priceRange: 0.05,        // Reduced weight
      size: 0.02               // Lowest weight
    };

    // Price ranges for categorization
    this.priceRanges = {
      budget: { min: 0, max: 1000 },
      affordable: { min: 1000, max: 3000 },
      mid: { min: 3000, max: 7000 },
      premium: { min: 7000, max: 15000 },
      luxury: { min: 15000, max: Infinity }
    };

    // Cache for user profiles
    this.userProfileCache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
  }

  /**
   * Extract features from a clothing item
   */
  extractFeatures(item) {
    if (!item || typeof item !== 'object') {
      return {
        broadCategory: 'Unknown',
        detailedCategory: 'Unknown',
        broadCategories: ['Unknown'],
        detailedCategories: ['Unknown'],
        colors: [],
        tags: [],
        brand: 'unknown',
        priceRange: 'unknown',
        size: 'unknown'
      };
    }

    // Handle multiple categories (new feature)
    let broadCategories = [];
    let detailedCategories = [];
    let colors = [];

    // Multi-category support
    if (Array.isArray(item.broadCategories) && item.broadCategories.length > 0) {
      broadCategories = item.broadCategories;
    } else if (item.broadCategory) {
      broadCategories = [item.broadCategory];
    }

    if (Array.isArray(item.detailedCategories) && item.detailedCategories.length > 0) {
      detailedCategories = item.detailedCategories;
    } else if (item.detailedCategory) {
      detailedCategories = [item.detailedCategory];
    }

    // Color support
    if (Array.isArray(item.colors) && item.colors.length > 0) {
      colors = item.colors.map(color => color.toLowerCase());
    }

    // If hierarchical categories are not available, try to infer from legacy category
    if (broadCategories.length === 0 && item.category) {
      const inferredBroad = this.inferBroadCategory(item.category);
      if (inferredBroad) broadCategories = [inferredBroad];
    }
    if (detailedCategories.length === 0 && item.category) {
      detailedCategories = [item.category];
    }

    // Ensure we have at least one category
    if (broadCategories.length === 0) broadCategories = ['Unknown'];
    if (detailedCategories.length === 0) detailedCategories = ['Unknown'];

    return {
      // Multi-category support
      broadCategories: broadCategories,
      detailedCategories: detailedCategories,
      colors: colors,

      // Backward compatibility
      broadCategory: broadCategories[0],
      detailedCategory: detailedCategories[0],

      tags: Array.isArray(item.tags) ? item.tags : [],
      brand: String(item.brand || 'Unknown').toLowerCase(),
      priceRange: this.getPriceRange(item.price),
      size: String(item.size || 'Unknown').toLowerCase()
    };
  }

  /**
   * Infer broad category from legacy category field
   */
  inferBroadCategory(category) {
    if (!category) return null;

    const categoryMappings = {
      'tops': 'T-Shirts',
      'bottoms': 'Pants',
      'dresses': 'Casual Dresses',
      'outerwear': 'Jackets',
      'shoes': 'Sneakers',
      'accessories': 'Bags'
    };

    const lowerCategory = category.toLowerCase();
    return categoryMappings[lowerCategory] || category;
  }

  /**
   * Get price range category
   */
  getPriceRange(price) {
    if (!price || isNaN(price)) return 'unknown';

    for (const [range, bounds] of Object.entries(this.priceRanges)) {
      if (price >= bounds.min && price < bounds.max) {
        return range;
      }
    }
    return 'luxury';
  }

  /**
   * Calculate Jaccard similarity for tags
   */
  calculateTagsSimilarity(tags1, tags2) {
    if (!tags1.length && !tags2.length) return 0;
    if (!tags1.length || !tags2.length) return 0;

    const set1 = new Set(tags1.map(tag => tag.toLowerCase()));
    const set2 = new Set(tags2.map(tag => tag.toLowerCase()));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * Calculate Jaccard similarity for colors
   */
  calculateColorsSimilarity(colors1, colors2) {
    if (!colors1.length && !colors2.length) return 0;
    if (!colors1.length || !colors2.length) return 0;

    const set1 = new Set(colors1.map(color => color.toLowerCase()));
    const set2 = new Set(colors2.map(color => color.toLowerCase()));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * Calculate similarity between multiple categories
   */
  calculateMultiCategorySimilarity(categories1, categories2) {
    if (!categories1.length && !categories2.length) return 0;
    if (!categories1.length || !categories2.length) return 0;

    const set1 = new Set(categories1.map(cat => cat.toLowerCase()));
    const set2 = new Set(categories2.map(cat => cat.toLowerCase()));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * Calculate cosine similarity between two items
   */
  calculateItemSimilarity(item1, item2) {
    const features1 = this.extractFeatures(item1);
    const features2 = this.extractFeatures(item2);

    let similarity = 0;

    // Multi-category similarity (Jaccard similarity for better matching)
    const broadCategorySimilarity = this.calculateMultiCategorySimilarity(
      features1.broadCategories,
      features2.broadCategories
    );
    similarity += broadCategorySimilarity * this.featureWeights.broadCategory;

    const detailedCategorySimilarity = this.calculateMultiCategorySimilarity(
      features1.detailedCategories,
      features2.detailedCategories
    );
    similarity += detailedCategorySimilarity * this.featureWeights.detailedCategory;

    // Color similarity (Jaccard similarity)
    const colorsSimilarity = this.calculateColorsSimilarity(features1.colors, features2.colors);
    similarity += colorsSimilarity * this.featureWeights.colors;

    // Tags similarity (Jaccard similarity)
    const tagsSimilarity = this.calculateTagsSimilarity(features1.tags, features2.tags);
    similarity += tagsSimilarity * this.featureWeights.tags;

    // Brand similarity (exact match)
    if (features1.brand === features2.brand) {
      similarity += this.featureWeights.brand;
    }

    // Price range similarity (exact match)
    if (features1.priceRange === features2.priceRange) {
      similarity += this.featureWeights.priceRange;
    }

    // Size similarity (exact match)
    if (features1.size === features2.size) {
      similarity += this.featureWeights.size;
    }

    return similarity;
  }

  /**
   * Fetch user's liked items from Firestore
   */
  async fetchUserLikedItems(userId) {
    try {
      const likesRef = collection(db, 'users', userId, 'likes');
      const likesSnapshot = await getDocs(likesRef);

      const likedItemIds = likesSnapshot.docs.map(doc => doc.data().itemId);

      // Fetch item details
      const itemPromises = likedItemIds.map(async (itemId) => {
        const itemDoc = await getDoc(doc(db, 'clothingItems', itemId));
        return itemDoc.exists() ? { id: itemDoc.id, ...itemDoc.data() } : null;
      });

      const items = await Promise.all(itemPromises);
      return items.filter(item => item !== null);
    } catch (error) {
      console.error('Error fetching user liked items:', error);
      return [];
    }
  }

  /**
   * Build user preference profile from interactions
   */
  async buildUserPreferenceProfile(userId) {
    try {
      // Check cache first
      const cacheKey = `userProfile_${userId}`;
      const cached = this.userProfileCache.get(cacheKey);

      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        return cached.profile;
      }

      const likedItems = await this.fetchUserLikedItems(userId);

      if (likedItems.length === 0) {
        return null; // No preferences available
      }

      const preferences = {
        broadCategories: {},
        detailedCategories: {},
        colors: {},
        tags: {},
        brands: {},
        priceRanges: {},
        sizes: {}
      };

      // Count preferences
      likedItems.forEach(item => {
        const features = this.extractFeatures(item);

        // Count all broad categories
        features.broadCategories.forEach(broadCat => {
          preferences.broadCategories[broadCat] =
            (preferences.broadCategories[broadCat] || 0) + 1;
        });

        // Count all detailed categories
        features.detailedCategories.forEach(detailedCat => {
          preferences.detailedCategories[detailedCat] =
            (preferences.detailedCategories[detailedCat] || 0) + 1;
        });

        // Count all colors
        features.colors.forEach(color => {
          preferences.colors[color.toLowerCase()] =
            (preferences.colors[color.toLowerCase()] || 0) + 1;
        });

        // Count tags
        features.tags.forEach(tag => {
          preferences.tags[tag.toLowerCase()] =
            (preferences.tags[tag.toLowerCase()] || 0) + 1;
        });

        preferences.brands[features.brand] =
          (preferences.brands[features.brand] || 0) + 1;

        preferences.priceRanges[features.priceRange] =
          (preferences.priceRanges[features.priceRange] || 0) + 1;

        preferences.sizes[features.size] =
          (preferences.sizes[features.size] || 0) + 1;
      });

      // Normalize preferences (convert counts to percentages)
      const totalItems = likedItems.length;
      Object.keys(preferences).forEach(category => {
        Object.keys(preferences[category]).forEach(key => {
          preferences[category][key] = preferences[category][key] / totalItems;
        });
      });

      // Cache the profile
      this.userProfileCache.set(cacheKey, {
        profile: preferences,
        timestamp: Date.now()
      });

      return preferences;
    } catch (error) {
      console.error('Error building user preference profile:', error);
      return null;
    }
  }

  /**
   * Calculate similarity between user preferences and an item
   */
  calculateUserItemSimilarity(userProfile, item) {
    if (!userProfile) return 0;

    const features = this.extractFeatures(item);
    let score = 0;

    // Multi-category preferences (average of all category scores)
    if (features.broadCategories.length > 0) {
      const broadCategoryScores = features.broadCategories.map(cat =>
        userProfile.broadCategories[cat] || 0
      );
      const avgBroadScore = broadCategoryScores.reduce((sum, score) => sum + score, 0) / broadCategoryScores.length;
      score += avgBroadScore * this.featureWeights.broadCategory;
    }

    if (features.detailedCategories.length > 0) {
      const detailedCategoryScores = features.detailedCategories.map(cat =>
        userProfile.detailedCategories[cat] || 0
      );
      const avgDetailedScore = detailedCategoryScores.reduce((sum, score) => sum + score, 0) / detailedCategoryScores.length;
      score += avgDetailedScore * this.featureWeights.detailedCategory;
    }

    // Color preferences (average of all color scores)
    if (features.colors.length > 0) {
      const colorScores = features.colors.map(color =>
        userProfile.colors[color.toLowerCase()] || 0
      );
      const avgColorScore = colorScores.reduce((sum, score) => sum + score, 0) / colorScores.length;
      score += avgColorScore * this.featureWeights.colors;
    }

    // Tags preference (average of all tag scores)
    if (features.tags.length > 0) {
      const tagScores = features.tags.map(tag =>
        userProfile.tags[tag.toLowerCase()] || 0
      );
      const avgTagScore = tagScores.reduce((sum, score) => sum + score, 0) / tagScores.length;
      score += avgTagScore * this.featureWeights.tags;
    }

    // Brand preference
    const brandScore = userProfile.brands[features.brand] || 0;
    score += brandScore * this.featureWeights.brand;

    // Price range preference
    const priceRangeScore = userProfile.priceRanges[features.priceRange] || 0;
    score += priceRangeScore * this.featureWeights.priceRange;

    // Size preference
    const sizeScore = userProfile.sizes[features.size] || 0;
    score += sizeScore * this.featureWeights.size;

    return score;
  }

  /**
   * Get recommended items for a user
   */
  async getRecommendedItems(userId, candidateItems, threshold = 0.3) {
    try {
      // Validate inputs
      if (!userId || !Array.isArray(candidateItems) || candidateItems.length === 0) {
        return Array.isArray(candidateItems) ? candidateItems : [];
      }

      // Ensure threshold is a valid number
      const validThreshold = typeof threshold === 'number' && !isNaN(threshold) ? threshold : 0.3;

      const userProfile = await this.buildUserPreferenceProfile(userId);

      if (!userProfile) {
        // No user preferences available, return shuffled items
        return this.shuffleArray([...candidateItems]);
      }

      const scoredItems = candidateItems
        .filter(item => item && typeof item === 'object') // Filter out invalid items
        .map(item => {
          try {
            const score = this.calculateUserItemSimilarity(userProfile, item);
            return {
              item,
              score: typeof score === 'number' && !isNaN(score) ? score : 0
            };
          } catch (error) {
            console.error('Error calculating similarity for item:', item?.id, error);
            return { item, score: 0 };
          }
        });

      // Filter items above threshold and sort by score
      const recommendedItems = scoredItems
        .filter(scored => scored.score >= validThreshold)
        .sort((a, b) => b.score - a.score)
        .map(scored => scored.item);

      // If we don't have enough recommended items, add some random ones
      if (recommendedItems.length < 10) {
        const remainingItems = candidateItems.filter(item =>
          item && !recommendedItems.find(recItem => recItem?.id === item?.id)
        );
        const shuffledRemaining = this.shuffleArray(remainingItems);
        recommendedItems.push(...shuffledRemaining.slice(0, 10 - recommendedItems.length));
      }

      return recommendedItems.filter(item => item); // Remove any null/undefined items
    } catch (error) {
      console.error('Error getting recommended items:', error);
      return Array.isArray(candidateItems) ? this.shuffleArray([...candidateItems]) : [];
    }
  }

  /**
   * Shuffle array using Fisher-Yates algorithm
   */
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Clear user profile cache
   */
  clearCache(userId = null) {
    if (userId) {
      this.userProfileCache.delete(`userProfile_${userId}`);
    } else {
      this.userProfileCache.clear();
    }
  }
}
