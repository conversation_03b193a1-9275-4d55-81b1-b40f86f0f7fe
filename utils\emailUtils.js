import { emailConfig } from '../firebase.config';
import { functions } from '../firebase.config';
import { httpsCallable } from 'firebase/functions';

/**
 * Send an order confirmation email to the buyer
 * @param {Object} orderData - Order data including buyer email, items, etc.
 * @returns {Promise<boolean>} - Whether the email was sent successfully
 */
export const sendBuyerOrderConfirmationEmail = async (orderData) => {
  try {
    if (!orderData || !orderData.buyerEmail) {
      throw new Error('Invalid order data or missing buyer email');
    }

    console.log(`Processing buyer email for order(s) with ${orderData.items.length} total items`);

    // Format the order items for the email
    const formattedItems = orderData.items.map(item => ({
      name: item.title || 'Untitled Item',
      price: item.price ? `₹${item.price.toFixed(2)}` : 'N/A',
      quantity: item.quantity || 1,
      category: item.category || 'Uncategorized',
      brand: item.brand || 'Unknown Brand',
    }));

    // Ensure we have a valid amount and convert it to a string with proper formatting
    // Make sure we're using a number for the calculation
    const numericAmount = typeof orderData.amount === 'number' ? orderData.amount : 0;
    const totalAmount = numericAmount > 0 ? `₹${numericAmount.toFixed(2)}` : 'N/A';

    console.log(`Total amount for buyer email: ${totalAmount} (original value: ${orderData.amount})`);

    // Check if we have multiple orders
    const hasMultipleOrders = orderData.orderIds && orderData.orderIds.length > 1;

    // CRITICAL FIX: Ensure we're using the correct order IDs
    // For multi-seller orders, we need to make sure we're using the actual document IDs
    // from the database, not just the IDs passed in the orderData
    const orderIds = orderData.orderIds || [orderData.id];

    // Create a formatted string of all order IDs
    const orderIdText = hasMultipleOrders
      ? `Orders: ${orderIds.join(', ')}`
      : `Order: ${orderData.id}`;

    console.log(`Order IDs for buyer email: ${orderIdText}`);

    // Collect seller names if available
    const sellerNames = [];
    if (orderData.sellerInfo) {
      // Try to extract seller names from sellerInfo
      Object.keys(orderData.sellerInfo).forEach(sellerId => {
        if (orderData.sellerInfo[sellerId]?.name || orderData.sellerInfo[sellerId]?.displayName) {
          sellerNames.push(orderData.sellerInfo[sellerId].name || orderData.sellerInfo[sellerId].displayName);
        }
      });
    }

    // Prepare the email data
    const emailData = {
      to: orderData.buyerEmail,
      subject: 'Your SwipeSense Order Confirmation',
      templateId: 1, // Replace with your Brevo template ID
      params: {
        orderDate: new Date().toLocaleDateString(),
        customerName: orderData.buyerName || 'Valued Customer',
        items: formattedItems,
        totalAmount: totalAmount,
        shippingAddress: formatAddress(orderData.shippingAddress),
        estimatedDelivery: getEstimatedDeliveryDate(),
        paymentMethod: orderData.paymentMethod || 'Online Payment',
        paymentStatus: orderData.paymentStatus || 'Pending',
        hasMultipleOrders: hasMultipleOrders,
        orderCount: hasMultipleOrders ? orderIds.length : 1,
        // Add explicit amount as a number for calculations in the template
        amountValue: numericAmount,
        // Add seller names
        sellerNames: sellerNames.length > 0 ? sellerNames : undefined
      },
      apiKey: emailConfig.brevoApiKey,
      // Add headers to prevent duplicate emails
      headers: {
        'X-Order-Id': orderData.id,
        'X-Order-Count': hasMultipleOrders ? orderIds.length.toString() : '1',
        'X-Unique-Id': Date.now().toString() // Add unique ID to prevent duplicates
      }
    };

    // Add additional debugging information
    console.log(`Sending buyer confirmation email with the following data:
      - Order ID: ${orderData.id}
      - Order IDs: ${JSON.stringify(orderIds)}
      - Total Amount: ${orderData.amount}
      - Items Count: ${orderData.items.length}
      - Has Multiple Orders: ${hasMultipleOrders}
    `);

    // Call the Firebase function to send the email
    const sendEmail = httpsCallable(functions, 'sendEmail');
    const result = await sendEmail(emailData);

    return result.data.success;
  } catch (error) {
    console.error('Error sending buyer order confirmation email:', error);
    return false;
  }
};

/**
 * Send an order notification email to the seller
 * @param {Object} orderData - Order data including seller email, items, etc.
 * @returns {Promise<boolean>} - Whether the email was sent successfully
 */
export const sendSellerOrderNotificationEmail = async (orderData) => {
  try {
    if (!orderData || !orderData.sellerEmail) {
      throw new Error('Invalid order data or missing seller email');
    }

    if (!orderData.sellerId) {
      console.warn(`No seller ID provided for order ${orderData.id}. This may cause issues with item filtering.`);
    }

    console.log(`Processing seller email for order ${orderData.id} with ${orderData.items.length} items to ${orderData.sellerEmail}`);

    // Format the order items for the email - these should already be filtered to only include this seller's items
    const formattedItems = orderData.items.map(item => ({
      name: item.title || 'Untitled Item',
      price: item.price ? `₹${item.price.toFixed(2)}` : 'N/A',
      quantity: item.quantity || 1,
      category: item.category || 'Uncategorized',
      brand: item.brand || 'Unknown Brand',
    }));

    // Calculate the total amount based on the items in this email
    // This ensures the total is correct even if the passed amount is for all items
    const calculatedTotal = orderData.items.reduce((total, item) => {
      return total + (item.price || 0) * (item.quantity || 1);
    }, 0);

    // Use the calculated total or fall back to the provided amount
    const totalAmount = `₹${calculatedTotal.toFixed(2)}`;

    // Add a unique identifier to the email subject to prevent email threading
    const uniqueId = Date.now().toString().slice(-6);

    // CRITICAL FIX: Ensure we're using the correct order ID for this seller
    // Priority: documentId > orderId > id
    // The documentId is explicitly set in CheckoutScreen.js and represents the actual Firestore document ID
    const orderIdForEmail = orderData.documentId || orderData.orderId || orderData.id;

    console.log(`Preparing seller email with order ID: ${orderIdForEmail} for seller: ${orderData.sellerId || 'unknown'}`);

    // Get buyer name with fallback
    const buyerName = orderData.buyerName || 'Customer';

    // Prepare the email data
    const emailData = {
      to: orderData.sellerEmail,
      subject: `New Order from ${buyerName}`,
      templateId: 2, // Replace with your Brevo template ID
      params: {
        orderDate: new Date().toLocaleDateString(),
        buyerName: buyerName,
        customerName: buyerName, // For backward compatibility
        items: formattedItems,
        totalAmount: totalAmount,
        shippingAddress: formatAddress(orderData.shippingAddress),
        paymentMethod: orderData.paymentMethod || 'Online Payment',
        paymentStatus: orderData.paymentStatus || 'Pending',
        expectedPayout: calculateExpectedPayout(calculatedTotal),
        sellerName: orderData.sellerName || 'Seller',
        uniqueId: uniqueId, // Add this to ensure email uniqueness
        itemCount: orderData.items.length, // Add item count for reference
        sellerId: orderData.sellerId || 'unknown', // Include seller ID for tracking
      },
      apiKey: emailConfig.brevoApiKey,
      // Add headers to prevent duplicate emails
      headers: {
        'X-Order-Id': orderIdForEmail,
        'X-Seller-Id': orderData.sellerId || 'unknown',
        'X-Unique-Id': uniqueId
      }
    };

    // Call the Firebase function to send the email
    const sendEmail = httpsCallable(functions, 'sendEmail');
    const result = await sendEmail(emailData);

    return result.data.success;
  } catch (error) {
    console.error('Error sending seller order notification email:', error);
    return false;
  }
};

/**
 * Format an address object into a string
 * @param {Object} address - Address object
 * @returns {string} - Formatted address string
 */
const formatAddress = (address) => {
  if (!address) return 'No address provided';

  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.zipCode) parts.push(address.zipCode);
  if (address.country) parts.push(address.country);

  return parts.join(', ');
};

/**
 * Get an estimated delivery date (7 days from now)
 * @returns {string} - Formatted date string
 */
const getEstimatedDeliveryDate = () => {
  const date = new Date();
  date.setDate(date.getDate() + 7);
  return date.toLocaleDateString();
};

/**
 * Calculate the expected payout for the seller (90% of the order amount)
 * @param {number} amount - Order amount
 * @returns {string} - Formatted payout amount
 */
const calculateExpectedPayout = (amount) => {
  if (!amount) return 'N/A';
  const payout = amount * 0.9; // 90% of the order amount
  return `₹${payout.toFixed(2)}`;
};
