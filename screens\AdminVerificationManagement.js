import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ActivityIndicator,
  RefreshControl,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  doc,
  onSnapshot,
  getDoc
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';
import StatusDropdown from '../components/StatusDropdown';

// Bank Account Section Component
const BankAccountSection = ({ selectedVerification }) => {
  const [userBankDetails, setUserBankDetails] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchUserBankDetails = async () => {
      try {
        if (selectedVerification?.userId) {
          const userDocRef = doc(db, 'users', selectedVerification.userId);
          const userDoc = await getDoc(userDocRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setUserBankDetails(userData.bankAccount || null);
          }
        }
      } catch (error) {
        console.error('Error fetching user bank details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserBankDetails();
  }, [selectedVerification?.userId]);

  const styles = StyleSheet.create({
    modalSection: {
      backgroundColor: '#fff',
      marginHorizontal: 16,
      marginVertical: 8,
      borderRadius: 12,
      padding: 16,
    },
    modalSectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#333',
      marginBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#f0f0f0',
      paddingBottom: 8,
    },
    modalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    modalLabel: {
      fontSize: 14,
      fontWeight: '600',
      color: '#333',
      flex: 1,
    },
    modalValue: {
      fontSize: 14,
      color: '#666',
      flex: 2,
      textAlign: 'right',
    },
    accountNumber: {
      fontFamily: 'monospace',
      fontWeight: 'bold',
    },
    noBankAccount: {
      alignItems: 'center',
      paddingVertical: 20,
    },
    noBankAccountText: {
      fontSize: 16,
      color: '#999',
      marginTop: 12,
      textAlign: 'center',
    },
    loadingContainer: {
      alignItems: 'center',
      paddingVertical: 20,
    },
    loadingText: {
      fontSize: 14,
      color: '#666',
      marginTop: 8,
    },
  });

  return (
    <View style={styles.modalSection}>
      <Text style={styles.modalSectionTitle}>Bank Account Details</Text>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading bank details...</Text>
        </View>
      ) : userBankDetails ? (
        <>
          <View style={styles.modalRow}>
            <Text style={styles.modalLabel}>Account Holder:</Text>
            <Text style={styles.modalValue}>{userBankDetails.accountHolderName || 'N/A'}</Text>
          </View>
          <View style={styles.modalRow}>
            <Text style={styles.modalLabel}>Account Number:</Text>
            <Text style={[styles.modalValue, styles.accountNumber]}>
              {userBankDetails.accountNumber ?
                '****' + userBankDetails.accountNumber.slice(-4) : 'N/A'}
            </Text>
          </View>
          <View style={styles.modalRow}>
            <Text style={styles.modalLabel}>IFSC Code:</Text>
            <Text style={styles.modalValue}>{userBankDetails.ifscCode || 'N/A'}</Text>
          </View>
          <View style={styles.modalRow}>
            <Text style={styles.modalLabel}>Bank Name:</Text>
            <Text style={styles.modalValue}>{userBankDetails.bankName || 'N/A'}</Text>
          </View>
          <View style={styles.modalRow}>
            <Text style={styles.modalLabel}>Branch Name:</Text>
            <Text style={styles.modalValue}>{userBankDetails.branchName || 'N/A'}</Text>
          </View>
          {userBankDetails.updatedAt && (
            <View style={styles.modalRow}>
              <Text style={styles.modalLabel}>Updated:</Text>
              <Text style={styles.modalValue}>
                {userBankDetails.updatedAt.toDate ?
                  userBankDetails.updatedAt.toDate().toLocaleDateString() : 'N/A'}
              </Text>
            </View>
          )}
        </>
      ) : (
        <View style={styles.noBankAccount}>
          <Ionicons name="card-outline" size={40} color="#ccc" />
          <Text style={styles.noBankAccountText}>No bank account details added by seller</Text>
        </View>
      )}
    </View>
  );
};

const AdminVerificationManagement = ({ navigation }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [verifications, setVerifications] = useState([]);
  const [filteredVerifications, setFilteredVerifications] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedVerification, setSelectedVerification] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);


  const statusOptions = [
    { label: 'All Statuses', value: 'all' },
    { label: 'Needs Code', value: 'needsCode' },
    { label: 'Verified', value: 'verified' },
    { label: 'Rejected', value: 'rejected' }
  ];

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchVerifications();
      setupRealtimeListener();
    }
  }, [isAdmin]);

  useEffect(() => {
    filterVerifications();
  }, [verifications, searchQuery, selectedStatus]);

  const checkAdminAccess = async () => {
    try {
      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        Alert.alert(
          'Access Denied',
          'You do not have administrator privileges.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeListener = () => {
    const verificationsRef = collection(db, 'sellerVerifications');
    const q = query(verificationsRef, orderBy('createdAt', 'desc'));

    const unsubscribe = onSnapshot(q, async (snapshot) => {
      try {
        const verificationsData = await Promise.all(
          snapshot.docs.map(async (docSnapshot) => {
            const verificationData = docSnapshot.data();

            // If name is empty, try to fetch it from the user document
            let displayName = verificationData.name;
            if (!displayName || displayName.trim() === '') {
              try {
                const userDocRef = doc(db, 'users', verificationData.userId);
                const userDoc = await getDoc(userDocRef);
                if (userDoc.exists()) {
                  const userData = userDoc.data();
                  displayName = userData.name || userData.displayName || verificationData.email.split('@')[0];
                } else {
                  displayName = verificationData.email.split('@')[0];
                }
              } catch (error) {
                console.error('Error fetching user name:', error);
                displayName = verificationData.email.split('@')[0];
              }
            }

            return {
              id: docSnapshot.id,
              ...verificationData,
              displayName // Add the resolved display name
            };
          })
        );
        setVerifications(verificationsData);
      } catch (error) {
        console.error('Error processing verification updates:', error);
      }
    }, (error) => {
      console.error('Error listening to verifications:', error);
    });

    return unsubscribe;
  };

  const fetchVerifications = async () => {
    try {
      setLoading(true);
      const verificationsRef = collection(db, 'sellerVerifications');
      const q = query(verificationsRef, orderBy('createdAt', 'desc'), limit(100));
      const querySnapshot = await getDocs(q);

      console.log('Fetched verifications count:', querySnapshot.size);

      const verificationsData = await Promise.all(
        querySnapshot.docs.map(async (docSnapshot) => {
          const verificationData = docSnapshot.data();

          // If name is empty, try to fetch it from the user document
          let displayName = verificationData.name;
          if (!displayName || displayName.trim() === '') {
            try {
              const userDocRef = doc(db, 'users', verificationData.userId);
              const userDoc = await getDoc(userDocRef);
              if (userDoc.exists()) {
                const userData = userDoc.data();
                displayName = userData.name || userData.displayName || verificationData.email.split('@')[0];
              } else {
                displayName = verificationData.email.split('@')[0];
              }
            } catch (error) {
              console.error('Error fetching user name:', error);
              displayName = verificationData.email.split('@')[0];
            }
          }

          return {
            id: docSnapshot.id,
            ...verificationData,
            displayName // Add the resolved display name
          };
        })
      );

      console.log('Processed verifications data:', verificationsData.length);
      setVerifications(verificationsData);
    } catch (error) {
      console.error('Error fetching verifications:', error);
      Alert.alert('Error', 'Failed to fetch verification requests');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterVerifications = () => {
    let filtered = verifications;

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(verification => verification.status === selectedStatus);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(verification =>
        verification.email?.toLowerCase().includes(query) ||
        verification.name?.toLowerCase().includes(query) ||
        verification.displayName?.toLowerCase().includes(query) ||
        verification.phoneNumber?.includes(query) ||
        verification.verificationCode?.toLowerCase().includes(query)
      );
    }

    setFilteredVerifications(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchVerifications();
    setRefreshing(false);
  };



  const getStatusColor = (status) => {
    switch (status) {
      case 'verified': return '#4CAF50';
      case 'needsCode': return '#FF9800';
      case 'rejected': return '#F44336';
      default: return '#666';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified': return 'checkmark-circle';
      case 'needsCode': return 'time';
      case 'rejected': return 'close-circle';
      default: return 'help-circle';
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderVerificationItem = ({ item }) => (
    <TouchableOpacity
      style={styles.verificationCard}
      onPress={() => {
        setSelectedVerification(item);
        setModalVisible(true);
      }}
    >
      <View style={styles.verificationHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.displayName || item.name || 'No Name'}</Text>
          <Text style={styles.userEmail}>{item.email}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Ionicons name={getStatusIcon(item.status)} size={16} color="#fff" />
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>

      <View style={styles.verificationDetails}>
        <Text style={styles.detailText}>Phone: {item.phoneNumber || 'N/A'}</Text>
        <Text style={styles.detailText}>Code: {item.verificationCode}</Text>
        <Text style={styles.detailText}>Created: {formatDate(item.createdAt)}</Text>
      </View>


    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have administrator privileges.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Verification Management</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      {/* Search and Filter Section */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by email, name, phone, or code..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <StatusDropdown
          options={statusOptions}
          selectedValue={selectedStatus}
          onValueChange={setSelectedStatus}
          placeholder="Filter by status"
        />
      </View>

      {/* Statistics */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{verifications.length}</Text>
          <Text style={styles.statLabel}>Total</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {verifications.filter(v => v.status === 'needsCode').length}
          </Text>
          <Text style={styles.statLabel}>Pending</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {verifications.filter(v => v.status === 'verified').length}
          </Text>
          <Text style={styles.statLabel}>Verified</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {verifications.filter(v => v.status === 'rejected').length}
          </Text>
          <Text style={styles.statLabel}>Rejected</Text>
        </View>
      </View>

      {/* Verifications List */}
      <FlatList
        data={filteredVerifications}
        renderItem={renderVerificationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="document-outline" size={80} color="#ccc" />
            <Text style={styles.emptyText}>No verification requests found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery || selectedStatus !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'Verification requests will appear here when sellers submit them'
              }
            </Text>
          </View>
        }
      />

      {/* Verification Details Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaWrapper>
          <SafeAreaHeader>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Verification Details</Text>
            <View style={styles.backButton} />
          </SafeAreaHeader>

          {selectedVerification && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>User Information</Text>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Name:</Text>
                  <Text style={styles.modalValue}>{selectedVerification.displayName || selectedVerification.name || 'N/A'}</Text>
                </View>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Email:</Text>
                  <Text style={styles.modalValue}>{selectedVerification.email}</Text>
                </View>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Phone:</Text>
                  <Text style={styles.modalValue}>{selectedVerification.phoneNumber || 'N/A'}</Text>
                </View>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Website:</Text>
                  <Text style={styles.modalValue}>{selectedVerification.website || 'N/A'}</Text>
                </View>
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Address</Text>
                {selectedVerification.address ? (
                  <>
                    <View style={styles.modalRow}>
                      <Text style={styles.modalLabel}>Street:</Text>
                      <Text style={styles.modalValue}>{selectedVerification.address.street || 'N/A'}</Text>
                    </View>
                    <View style={styles.modalRow}>
                      <Text style={styles.modalLabel}>City:</Text>
                      <Text style={styles.modalValue}>{selectedVerification.address.city || 'N/A'}</Text>
                    </View>
                    <View style={styles.modalRow}>
                      <Text style={styles.modalLabel}>State:</Text>
                      <Text style={styles.modalValue}>{selectedVerification.address.state || 'N/A'}</Text>
                    </View>
                    <View style={styles.modalRow}>
                      <Text style={styles.modalLabel}>Country:</Text>
                      <Text style={styles.modalValue}>{selectedVerification.address.country || 'N/A'}</Text>
                    </View>
                    <View style={styles.modalRow}>
                      <Text style={styles.modalLabel}>Zip Code:</Text>
                      <Text style={styles.modalValue}>{selectedVerification.address.zipCode || 'N/A'}</Text>
                    </View>
                  </>
                ) : (
                  <Text style={styles.modalValue}>No address information</Text>
                )}
              </View>

              {/* Bank Account Details Section */}
              <BankAccountSection selectedVerification={selectedVerification} />

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Verification Details</Text>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Code:</Text>
                  <Text style={[styles.modalValue, styles.verificationCode]}>{selectedVerification.verificationCode}</Text>
                </View>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Status:</Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedVerification.status) }]}>
                    <Ionicons name={getStatusIcon(selectedVerification.status)} size={16} color="#fff" />
                    <Text style={styles.statusText}>{selectedVerification.status}</Text>
                  </View>
                </View>
                <View style={styles.modalRow}>
                  <Text style={styles.modalLabel}>Created:</Text>
                  <Text style={styles.modalValue}>{formatDate(selectedVerification.createdAt)}</Text>
                </View>
                {selectedVerification.verifiedAt && (
                  <View style={styles.modalRow}>
                    <Text style={styles.modalLabel}>Verified:</Text>
                    <Text style={styles.modalValue}>{formatDate(selectedVerification.verifiedAt)}</Text>
                  </View>
                )}
                {selectedVerification.rejectedAt && (
                  <View style={styles.modalRow}>
                    <Text style={styles.modalLabel}>Rejected:</Text>
                    <Text style={styles.modalValue}>{formatDate(selectedVerification.rejectedAt)}</Text>
                  </View>
                )}
              </View>


            </ScrollView>
          )}
        </SafeAreaWrapper>
      </Modal>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  refreshButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchSection: {
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  verificationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  verificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  verificationDetails: {
    marginBottom: 12,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 20,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  modalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    flex: 1,
  },
  modalValue: {
    fontSize: 14,
    color: '#333',
    flex: 2,
    textAlign: 'right',
  },
  verificationCode: {
    fontFamily: 'monospace',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    fontWeight: 'bold',
  },

});

export default AdminVerificationManagement;
