import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, FlatList, Image, StyleSheet, ActivityIndicator, SafeAreaView, Platform, StatusBar, TouchableOpacity, Alert } from 'react-native';
import { collection, query, where, getDocs, orderBy, doc, getDoc, deleteDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

const UserUploads = ({ route, navigation }) => {
  const { userId } = route.params;
  const [userName, setUserName] = useState('');
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const currentUserId = auth.currentUser?.uid;
  const isCurrentUser = userId === currentUserId;

  // Fetch the user's display name for header
  useEffect(() => {
    const fetchUserName = async () => {
      try {
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
          setUserName(userDoc.data().name || 'User');
        } else {
          setUserName('User');
        }
      } catch (e) {
        console.error('Error fetching user name:', e);
        setUserName('User');
      }
    };
    fetchUserName();
  }, [userId]);

  // Handle edit item
  const handleEditItem = (itemId) => {
    navigation.navigate('EditItem', { itemId });
  };

  // Handle delete item
  const handleDeleteItem = (itemId) => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to delete this item? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            try {
              // Set loading state
              setLoading(true);

              // Delete the item
              deleteDoc(doc(db, 'clothingItems', itemId))
                .then(() => {
                  // Refresh the list after deletion
                  fetchUploads();
                  // Show success message
                  Alert.alert('Success', 'Item deleted successfully');
                })
                .catch((error) => {
                  console.error('Error deleting item:', error);
                  Alert.alert('Error', 'Failed to delete item');
                  setLoading(false);
                });
            } catch (error) {
              console.error('Error in delete handler:', error);
              Alert.alert('Error', 'An unexpected error occurred');
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // Wrap fetchUploads in useCallback
  const fetchUploads = useCallback(async () => {
    setLoading(true);
    try {
      const q = query(
        collection(db, 'clothingItems'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);
      setItems(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
    } catch (e) {
      console.error("Error fetching uploads:", e);
      setItems([]);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Use useFocusEffect to fetch data when the screen is focused
  useFocusEffect(
    useCallback(() => {
      fetchUploads();
    }, [fetchUploads])
  );

  if (loading) return <ActivityIndicator style={{ flex: 1 }} size="large" color="#FF6B6B" />;

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.header}>{userName ? `${userName}'s Uploads` : 'Uploads'}</Text>
        <FlatList
          data={items}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <View style={styles.itemRow}>
              <TouchableOpacity
                onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
                style={styles.itemContent}
              >
                <Image source={{ uri: item.imageUrl }} style={styles.image} />                <View style={{ flex: 1 }}>
                  <Text style={styles.title}>{item.title || item.category}</Text>
                  <Text style={styles.category}>{item.category}</Text>
                  {item.gender && <Text style={styles.gender}>{item.gender}</Text>}
                </View>
              </TouchableOpacity>

              {isCurrentUser && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    onPress={() => handleEditItem(item.id)}
                    style={styles.actionButton}
                  >
                    <Ionicons name="create-outline" size={22} color="#4A90E2" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => handleDeleteItem(item.id)}
                    style={styles.actionButton}
                  >
                    <Ionicons name="trash-outline" size={22} color="#FF6B6B" />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}
          ListEmptyComponent={<Text style={styles.empty}>No uploads found.</Text>}
          contentContainerStyle={styles.listContent}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    paddingTop: 20,
    paddingHorizontal: 16,
    backgroundColor: '#f8f8f8',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 18,
    color: '#FF6B6B',
    alignSelf: 'center',
    letterSpacing: 1,
  },
  listContent: {
    paddingBottom: 30,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 1,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 10,
  },
  actionButton: {
    padding: 8,
    marginLeft: 5,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 14,
    backgroundColor: '#eee',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 4,
  },  category: {
    fontSize: 14,
    color: '#888',
  },
  gender: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },

  empty: {
    textAlign: 'center',
    color: '#888',
    marginTop: 40,
    fontSize: 16,
  },
});

export default UserUploads;
