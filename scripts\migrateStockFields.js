/**
 * Migration Script: Add Stock Management Fields to Existing Items
 * 
 * This script adds stock management fields to existing clothingItems in Firestore.
 * It should be run once after deploying the stock management system.
 * 
 * Usage:
 * 1. Install Firebase Admin SDK: npm install firebase-admin
 * 2. Set up service account credentials
 * 3. Run: node scripts/migrateStockFields.js
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// Make sure to set up your service account key
const serviceAccount = require('../path/to/your/serviceAccountKey.json');

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://your-project-id.firebaseio.com'
});

const db = admin.firestore();

/**
 * Default stock value for existing items
 * You can adjust this based on your business needs
 */
const DEFAULT_STOCK = 10;

/**
 * Migrate stock fields for all existing clothing items
 */
async function migrateStockFields() {
    console.log('🚀 Starting stock fields migration...');

    try {
        // Get all clothing items
        const clothingItemsRef = db.collection('clothingItems');
        const snapshot = await clothingItemsRef.get();

        if (snapshot.empty) {
            console.log('📭 No clothing items found to migrate.');
            return;
        }

        console.log(`📦 Found ${snapshot.size} items to migrate.`);

        // Process items in batches to avoid overwhelming Firestore
        const batchSize = 500;
        const batches = [];
        let currentBatch = db.batch();
        let operationCount = 0;

        for (const doc of snapshot.docs) {
            const data = doc.data();

            // Check if item already has stock fields
            if (data.stock !== undefined) {
                console.log(`⏭️  Skipping ${doc.id} - already has stock fields`);
                continue;
            }

            // Determine stock status based on default stock
            const stockStatus = DEFAULT_STOCK === 0 ? 'out_of_stock' :
                DEFAULT_STOCK <= 5 ? 'low_stock' : 'in_stock';

            // Add stock management fields
            const stockFields = {
                stock: DEFAULT_STOCK,
                initialStock: DEFAULT_STOCK,
                reservedStock: 0,
                soldCount: 0,
                stockStatus: stockStatus,
                lastStockUpdate: admin.firestore.FieldValue.serverTimestamp(),
                // Don't set lastSale until first sale occurs
            };

            currentBatch.update(doc.ref, stockFields);
            operationCount++;

            // Create new batch if current one is full
            if (operationCount === batchSize) {
                batches.push(currentBatch);
                currentBatch = db.batch();
                operationCount = 0;
            }
        }

        // Add the last batch if it has operations
        if (operationCount > 0) {
            batches.push(currentBatch);
        }

        console.log(`📝 Created ${batches.length} batches for migration.`);

        // Execute all batches
        let totalUpdated = 0;
        for (let i = 0; i < batches.length; i++) {
            console.log(`⚡ Executing batch ${i + 1}/${batches.length}...`);

            try {
                await batches[i].commit();
                totalUpdated += (i === batches.length - 1) ? operationCount : batchSize;
                console.log(`✅ Batch ${i + 1} completed successfully.`);

                // Add delay between batches to avoid rate limiting
                if (i < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } catch (error) {
                console.error(`❌ Error executing batch ${i + 1}:`, error);
                throw error;
            }
        }

        console.log(`🎉 Migration completed successfully!`);
        console.log(`📊 Total items updated: ${totalUpdated}`);
        console.log(`📈 Default stock set to: ${DEFAULT_STOCK} per item`);

    } catch (error) {
        console.error('💥 Migration failed:', error);
        throw error;
    }
}

/**
 * Verify migration results
 */
async function verifyMigration() {
    console.log('🔍 Verifying migration results...');

    try {
        const clothingItemsRef = db.collection('clothingItems');
        const snapshot = await clothingItemsRef.limit(10).get();

        let migratedCount = 0;
        let totalCount = 0;

        for (const doc of snapshot.docs) {
            totalCount++;
            const data = doc.data();

            if (data.stock !== undefined &&
                data.initialStock !== undefined &&
                data.reservedStock !== undefined &&
                data.soldCount !== undefined &&
                data.stockStatus !== undefined) {
                migratedCount++;
                console.log(`✅ ${doc.id}: stock=${data.stock}, status=${data.stockStatus}`);
            } else {
                console.log(`❌ ${doc.id}: Missing stock fields`);
            }
        }

        console.log(`📊 Verification complete: ${migratedCount}/${totalCount} items have stock fields`);

    } catch (error) {
        console.error('💥 Verification failed:', error);
    }
}

/**
 * Rollback migration (if needed)
 */
async function rollbackMigration() {
    console.log('🔄 Rolling back stock fields migration...');

    const confirm = process.argv.includes('--confirm-rollback');
    if (!confirm) {
        console.log('⚠️  To confirm rollback, run with --confirm-rollback flag');
        return;
    }

    try {
        const clothingItemsRef = db.collection('clothingItems');
        const snapshot = await clothingItemsRef.get();

        const batchSize = 500;
        const batches = [];
        let currentBatch = db.batch();
        let operationCount = 0;

        for (const doc of snapshot.docs) {
            const fieldsToRemove = {
                stock: admin.firestore.FieldValue.delete(),
                initialStock: admin.firestore.FieldValue.delete(),
                reservedStock: admin.firestore.FieldValue.delete(),
                soldCount: admin.firestore.FieldValue.delete(),
                stockStatus: admin.firestore.FieldValue.delete(),
                lastStockUpdate: admin.firestore.FieldValue.delete(),
                lastSale: admin.firestore.FieldValue.delete(),
            };

            currentBatch.update(doc.ref, fieldsToRemove);
            operationCount++;

            if (operationCount === batchSize) {
                batches.push(currentBatch);
                currentBatch = db.batch();
                operationCount = 0;
            }
        }

        if (operationCount > 0) {
            batches.push(currentBatch);
        }

        // Execute rollback batches
        for (let i = 0; i < batches.length; i++) {
            await batches[i].commit();
            console.log(`🔄 Rollback batch ${i + 1}/${batches.length} completed.`);
        }

        console.log('✅ Rollback completed successfully.');

    } catch (error) {
        console.error('💥 Rollback failed:', error);
    }
}

/**
 * Main execution
 */
async function main() {
    const command = process.argv[2];

    switch (command) {
        case 'migrate':
            await migrateStockFields();
            await verifyMigration();
            break;

        case 'verify':
            await verifyMigration();
            break;

        case 'rollback':
            await rollbackMigration();
            break;

        default:
            console.log(`
📋 Stock Fields Migration Script

Usage:
  node scripts/migrateStockFields.js migrate   - Run the migration
  node scripts/migrateStockFields.js verify    - Verify migration results
  node scripts/migrateStockFields.js rollback  - Rollback migration (use --confirm-rollback)

Examples:
  node scripts/migrateStockFields.js migrate
  node scripts/migrateStockFields.js verify
  node scripts/migrateStockFields.js rollback --confirm-rollback

⚠️  Important Notes:
- Backup your database before running migration
- Test on a development environment first
- Default stock is set to ${DEFAULT_STOCK} per item
- Migration can be rolled back if needed
      `);
    }

    // Close the admin app
    admin.app().delete();
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
    console.error('💥 Unhandled rejection:', error);
    process.exit(1);
});

// Run the script
main().catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
});