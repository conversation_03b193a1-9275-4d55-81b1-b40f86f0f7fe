import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  getCountFromServer
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';

const AdminDashboard = ({ navigation }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalSellers: 0,
    totalBuyers: 0,
    totalListings: 0,
    totalOrders: 0,
    pendingVerifications: 0,
    activeSupportTickets: 0,
    recentActivity: []
  });

  useEffect(() => {
    checkAdminAccess();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchDashboardData();
    }
  }, [isAdmin]);

  const checkAdminAccess = async () => {
    try {
      const adminStatus = await checkAdminStatus(auth.currentUser.uid);
      setIsAdmin(adminStatus);

      if (!adminStatus) {
        Alert.alert(
          'Access Denied',
          'You do not have administrator privileges.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Helper function to safely get count
      const safeGetCount = async (collectionRef) => {
        try {
          const snapshot = await getCountFromServer(collectionRef);
          return snapshot.data().count;
        } catch (error) {
          console.warn('Error getting count for collection:', error);
          return 0;
        }
      };

      // Helper function to safely get docs
      const safeGetDocs = async (queryRef) => {
        try {
          const snapshot = await getDocs(queryRef);
          return snapshot.docs;
        } catch (error) {
          console.warn('Error getting docs for query:', error);
          return [];
        }
      };

      // Fetch user statistics
      const totalUsers = await safeGetCount(collection(db, 'users'));
      const totalSellers = await safeGetCount(
        query(collection(db, 'users'), where('isSeller', '==', true))
      );
      const totalBuyers = await safeGetCount(
        query(collection(db, 'users'), where('isSeller', '==', false))
      );

      // Try both 'items' and 'clothingItems' collections for listings
      let totalListings = await safeGetCount(collection(db, 'items'));
      if (totalListings === 0) {
        totalListings = await safeGetCount(collection(db, 'clothingItems'));
      }

      // Fetch order statistics
      const totalOrders = await safeGetCount(collection(db, 'orders'));

      // Fetch pending verifications
      const pendingVerifications = await safeGetCount(
        query(collection(db, 'sellerVerifications'), where('status', '==', 'needsCode'))
      );

      // Fetch active support tickets
      const activeSupportTickets = await safeGetCount(
        query(collection(db, 'supportTickets'), where('status', 'in', ['open', 'in_progress']))
      );

      // Fetch recent activity (last 5 orders)
      const recentOrdersDocs = await safeGetDocs(
        query(
          collection(db, 'orders'),
          orderBy('createdAt', 'desc'),
          limit(5)
        )
      );

      const recentActivity = recentOrdersDocs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        type: 'order'
      }));

      setStats({
        totalUsers,
        totalSellers,
        totalBuyers,
        totalListings,
        totalOrders,
        pendingVerifications,
        activeSupportTickets,
        recentActivity
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data. Please check your admin permissions.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
  };

  const renderStatCard = (title, value, icon, color, onPress) => (
    <TouchableOpacity
      style={[styles.statCard, { borderLeftColor: color }]}
      onPress={onPress}
    >
      <Ionicons name={icon} size={24} color={color} />
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderQuickAction = (title, icon, color, onPress) => (
    <TouchableOpacity
      style={[styles.quickActionCard, { backgroundColor: color }]}
      onPress={onPress}
    >
      <Ionicons name={icon} size={32} color="white" />
      <Text style={styles.quickActionTitle}>{title}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.loadingText}>Loading Admin Dashboard...</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>
            You don't have administrator privileges.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >

        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Welcome, Administrator</Text>
          <Text style={styles.welcomeSubtext}>
            Manage your SwipeSense platform from here
          </Text>
        </View>

        {/* Statistics Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Platform Overview</Text>
          <View style={styles.statsGrid}>
            {renderStatCard(
              'Total Users',
              stats.totalUsers,
              'people-outline',
              '#4ECDC4',
              () => navigation.navigate('AdminUserManagement')
            )}
            {renderStatCard(
              'Sellers',
              stats.totalSellers,
              'storefront-outline',
              '#FF6B6B',
              () => navigation.navigate('AdminUserManagement', { filter: 'sellers' })
            )}
            {renderStatCard(
              'Buyers',
              stats.totalBuyers,
              'person-outline',
              '#45B7D1',
              () => navigation.navigate('AdminUserManagement', { filter: 'buyers' })
            )}
            {renderStatCard(
              'Listings',
              stats.totalListings,
              'shirt-outline',
              '#96CEB4',
              () => navigation.navigate('AdminContentModeration')
            )}
            {renderStatCard(
              'Orders',
              stats.totalOrders,
              'receipt-outline',
              '#FECA57',
              () => navigation.navigate('AdminOrderManagement')
            )}
            {renderStatCard(
              'Pending Verifications',
              stats.pendingVerifications,
              'time-outline',
              '#FF9FF3',
              () => navigation.navigate('AdminVerificationManagement')
            )}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {renderQuickAction(
              'User Management',
              'people',
              '#4ECDC4',
              () => navigation.navigate('AdminUserManagement')
            )}
            {renderQuickAction(
              'Seller Details',
              'business',
              '#6C5CE7',
              () => navigation.navigate('AdminSellerDetails')
            )}
            {renderQuickAction(
              'Content Moderation',
              'shield-checkmark',
              '#FF6B6B',
              () => navigation.navigate('AdminContentModeration')
            )}
            {renderQuickAction(
              'Support Center',
              'help-circle',
              '#45B7D1',
              () => navigation.navigate('AdminSupport')
            )}
            {renderQuickAction(
              'Analytics',
              'analytics',
              '#96CEB4',
              () => navigation.navigate('AdminAnalytics')
            )}
            {renderQuickAction(
              'Transactions',
              'card',
              '#9B59B6',
              () => navigation.navigate('AdminTransactionManagement')
            )}
            {renderQuickAction(
              'System Settings',
              'settings',
              '#FECA57',
              () => navigation.navigate('AdminSettings')
            )}
            {renderQuickAction(
              'Live Chat',
              'chatbubbles',
              '#FF9FF3',
              () => navigation.navigate('AdminLiveChatScreen')
            )}
          </View>
        </View>

        {/* Alerts Section */}
        {(stats.pendingVerifications > 0 || stats.activeSupportTickets > 0) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Attention Required</Text>
            {stats.pendingVerifications > 0 && (
              <TouchableOpacity
                style={styles.alertCard}
                onPress={() => navigation.navigate('AdminVerificationManagement')}
              >
                <Ionicons name="warning" size={24} color="#FF6B6B" />
                <View style={styles.alertContent}>
                  <Text style={styles.alertTitle}>
                    {stats.pendingVerifications} Pending Verifications
                  </Text>
                  <Text style={styles.alertSubtext}>
                    Seller verification requests need review
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#666" />
              </TouchableOpacity>
            )}
            {stats.activeSupportTickets > 0 && (
              <TouchableOpacity
                style={styles.alertCard}
                onPress={() => navigation.navigate('AdminSupport')}
              >
                <Ionicons name="help-circle" size={24} color="#FFA500" />
                <View style={styles.alertContent}>
                  <Text style={styles.alertTitle}>
                    {stats.activeSupportTickets} Active Support Tickets
                  </Text>
                  <Text style={styles.alertSubtext}>
                    Support tickets require attention
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  welcomeSection: {
    padding: 20,
    backgroundColor: 'white',
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  welcomeSubtext: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  statsGrid: {
    paddingHorizontal: 20,
  },
  statCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statContent: {
    marginLeft: 16,
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statTitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 8,
  },
  alertCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  alertContent: {
    flex: 1,
    marginLeft: 12,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  alertSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default AdminDashboard;
