import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Alert, Platform, ScrollView, ActivityIndicator, KeyboardAvoidingView, Keyboard } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db, storage } from '../firebase.config';
import { updatePassword, deleteUser, EmailAuthProvider, reauthenticateWithCredential, sendEmailVerification } from 'firebase/auth';
import { doc, updateDoc, deleteDoc, collection, query, where, getDocs, getDoc } from 'firebase/firestore';
import { ref, deleteObject, listAll } from 'firebase/storage';
import RecommendationSettings from '../components/RecommendationSettings';
import { useClothingFeed } from '../hooks/useClothingFeed';

const SettingsScreen = ({ navigation, route }) => {
  const user = auth.currentUser;
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [isBuyer, setIsBuyer] = useState(false);

  // Get recommendation settings from the clothing feed hook
  const clothingFeedData = useClothingFeed(navigation);
  const {
    useRecommendations = true,
    recommendationThreshold = 0.3,
    updateRecommendationSettings
  } = clothingFeedData || {};

  // Fetch user data from Firestore
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return;

      try {
        const userDocRef = doc(db, 'users', user.uid);
        const docSnap = await getDoc(userDocRef);

        if (docSnap.exists()) {
          const userData = docSnap.data();
          setUserInfo(userData);
          setIsBuyer(!userData.isSeller);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, [user]);

  // State for current password (needed for re-authentication)
  const [currentPassword, setCurrentPassword] = useState('');
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);

  const handleChangePassword = async () => {
    // First step: show the confirmation dialog
    if (!showPasswordConfirm) {
      if (!password) {
        Alert.alert('Error', 'Please enter a new password');
        return;
      }
      if (password.length < 6) {
        Alert.alert('Error', 'Password should be at least 6 characters');
        return;
      }
      setShowPasswordConfirm(true);
      return;
    }

    // Second step: validate and update password
    if (!currentPassword) {
      Alert.alert('Error', 'Please enter your current password');
      return;
    }

    setLoading(true);
    try {
      // Re-authenticate user before changing password
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // For buyer accounts, verify email before allowing password change
      if (isBuyer) {
        // Check if email is verified
        if (!user.emailVerified) {
          // Send verification email
          await sendEmailVerification(user);

          // Show alert and navigate to verification screen
          Alert.alert(
            'Email Verification Required',
            'For security reasons, you need to verify your email before changing your password. A verification email has been sent to your inbox.',
            [{
              text: 'OK', onPress: () => {
                // Navigate to VerifyEmail screen
                console.log("Navigating to VerifyEmail from settings");
                navigation.navigate('VerifyEmail', { fromSettings: true });
              }
            }]
          );
          setLoading(false);
          return;
        }
      }

      // Now update the password
      await updatePassword(user, password);

      // Reset states and show success message
      setPassword('');
      setCurrentPassword('');
      setShowPasswordConfirm(false);
      Alert.alert('Success', 'Password updated successfully!');
    } catch (error) {
      console.error('Error updating password:', error);
      let errorMessage = 'Could not update password. Please try again.';
      if (error.code === 'auth/wrong-password') {
        errorMessage = 'Current password is incorrect. Please try again.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'New password is too weak. Please choose a stronger password.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many attempts. Please try again later.';
      } else if (error.code === 'auth/requires-recent-login') {
        errorMessage = 'For security reasons, please log out and log back in before changing your password.';
      }
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // State for account deletion confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletePassword, setDeletePassword] = useState('');
  const [deletingAccount, setDeletingAccount] = useState(false);



  // Handle account deletion
  const handleDeleteAccount = async () => {
    if (!deletePassword) {
      Alert.alert('Error', 'Please enter your password to confirm account deletion');
      return;
    }

    setDeletingAccount(true);
    try {
      // Re-authenticate user before deletion
      const credential = EmailAuthProvider.credential(user.email, deletePassword);
      await reauthenticateWithCredential(user, credential);

      // Delete user's data from Firestore
      await deleteUserData(user.uid);

      // Delete the user's authentication account
      await deleteUser(user);

      Alert.alert('Success', 'Your account has been deleted');
      // Navigation will happen automatically due to the auth state change
    } catch (error) {
      console.error('Error deleting account:', error);
      let errorMessage = 'Failed to delete account. Please try again.';
      if (error.code === 'auth/wrong-password') {
        errorMessage = 'Incorrect password. Please try again.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many attempts. Please try again later.';
      }
      Alert.alert('Error', errorMessage);
    } finally {
      setDeletingAccount(false);
      setShowDeleteConfirm(false);
      setDeletePassword('');
    }
  };

  // Function to delete user data from Firestore
  const deleteUserData = async (userId) => {
    try {
      // 1. Delete user's uploaded items
      const itemsQuery = query(collection(db, 'clothingItems'), where('userId', '==', userId));
      const itemsSnapshot = await getDocs(itemsQuery);

      // Delete each item document and its images
      const itemDeletionPromises = itemsSnapshot.docs.map(async (itemDoc) => {
        const itemData = itemDoc.data();

        // Delete item images from storage if they exist
        if (itemData.imageUrl) {
          try {
            // Extract the path from the URL or use a consistent pattern
            const imagePath = `clothingItems/${userId}/${itemDoc.id}`;
            const imageRef = ref(storage, imagePath);
            await deleteObject(imageRef);
          } catch (storageError) {
            console.error('Error deleting item image:', storageError);
            // Continue with deletion even if image deletion fails
          }
        }

        // Delete the item document
        await deleteDoc(doc(db, 'clothingItems', itemDoc.id));
      });

      await Promise.all(itemDeletionPromises);

      // 2. Delete user's likes
      const likesRef = collection(db, 'users', userId, 'likes');
      const likesSnapshot = await getDocs(likesRef);
      const likesDeletionPromises = likesSnapshot.docs.map(likeDoc =>
        deleteDoc(doc(db, 'users', userId, 'likes', likeDoc.id))
      );
      await Promise.all(likesDeletionPromises);

      // 3. Delete user's messages and chat threads
      // Find threads where user is a participant
      const threadsQuery = query(collection(db, 'threads'), where('participants', 'array-contains', userId));
      const threadsSnapshot = await getDocs(threadsQuery);

      const threadDeletionPromises = threadsSnapshot.docs.map(async (threadDoc) => {
        // Delete all messages in the thread
        const messagesRef = collection(db, 'threads', threadDoc.id, 'messages');
        const messagesSnapshot = await getDocs(messagesRef);

        const messageDeletionPromises = messagesSnapshot.docs.map(messageDoc =>
          deleteDoc(doc(db, 'threads', threadDoc.id, 'messages', messageDoc.id))
        );
        await Promise.all(messageDeletionPromises);

        // Delete the thread itself
        await deleteDoc(doc(db, 'threads', threadDoc.id));
      });

      await Promise.all(threadDeletionPromises);

      // 4. Delete user's profile picture from storage if it exists
      try {
        const userFolderRef = ref(storage, `profilePictures/${userId}`);
        const userFiles = await listAll(userFolderRef);

        const profilePicDeletionPromises = userFiles.items.map(fileRef =>
          deleteObject(fileRef)
        );
        await Promise.all(profilePicDeletionPromises);
      } catch (storageError) {
        console.error('Error deleting profile pictures:', storageError);
        // Continue with deletion even if profile picture deletion fails
      }

      // 5. Finally, delete the user document
      await deleteDoc(doc(db, 'users', userId));

      console.log('Successfully deleted all user data');
    } catch (error) {
      console.error('Error deleting user data:', error);
      throw error; // Re-throw to handle in the calling function
    }
  };

  // Reference to the ScrollView
  const scrollViewRef = useRef(null);

  // Show delete account confirmation
  const confirmDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // First dismiss keyboard to avoid it covering the input field
            Keyboard.dismiss();
            // Then show the confirmation dialog
            setShowDeleteConfirm(true);
            // Scroll to the bottom after a short delay to ensure the UI has updated
            setTimeout(() => {
              if (scrollViewRef.current) {
                scrollViewRef.current.scrollToEnd({ animated: true });
              }
            }, 100); // Reduced delay for faster response
          }
        }
      ]
    );
  };

  // Function to scroll to the delete confirmation section when it appears
  const scrollToDeleteConfirm = () => {
    if (scrollViewRef.current && showDeleteConfirm) {
      // Add a slight delay to ensure the UI has updated
      setTimeout(() => {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }, 50); // Reduced delay for faster response
    }
  };

  // Effect to scroll when delete confirmation appears
  useEffect(() => {
    scrollToDeleteConfirm();
  }, [showDeleteConfirm]);

  // Effect to dismiss keyboard when delete confirmation appears
  useEffect(() => {
    if (showDeleteConfirm) {
      Keyboard.dismiss();
    }
  }, [showDeleteConfirm]);

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      <ScrollView
        style={styles.container}
        ref={scrollViewRef}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => {
              // Get the source parameter from route params
              const source = route.params?.source;
              const isSeller = route.params?.isSeller; // Retrieve isSeller
              console.log("Navigating back from Settings. Source:", source, "isSeller:", isSeller);

              // If we have a source, navigate back to it, passing isSeller if the source is MyProfile
              if (source) {
                if (source === 'MyProfile') {
                  navigation.navigate('MainApp', { screen: source, params: { isSeller: isSeller } });
                } else {
                  navigation.navigate('MainApp', { screen: source });
                }
              } else {
                // Default fallback
                navigation.goBack();
              }
            }}
            style={styles.headerButton}
          >
            <Ionicons name="chevron-back" size={28} color="#FF6B6B" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Settings</Text>
          <View style={{ width: 40 }} />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Change Password</Text>
          <TextInput            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="New Password"
            secureTextEntry
            placeholderTextColor="#000"
          />

          {showPasswordConfirm ? (
            <View style={styles.passwordConfirmContainer}>
              <View style={styles.confirmHeaderRow}>
                <Ionicons name="lock-closed-outline" size={22} color="#FF6B6B" />
                <Text style={styles.confirmTitle}>Confirm Password Change</Text>
              </View>
              <Text style={styles.confirmText}>Please enter your current password to verify your identity:</Text>
              <TextInput                style={[styles.input, styles.confirmInput]}
                value={currentPassword}
                onChangeText={setCurrentPassword}
                placeholder="Current Password"
                secureTextEntry
                autoFocus
                placeholderTextColor="#000"
              />
              <View style={styles.buttonRow}>
                <TouchableOpacity
                  style={styles.passwordCancelButton}
                  onPress={() => {
                    setShowPasswordConfirm(false);
                    setCurrentPassword('');
                  }}
                  disabled={loading}
                >
                  <Text style={styles.passwordCancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.passwordConfirmButton}
                  onPress={handleChangePassword}
                  disabled={loading || !currentPassword}
                >
                  {loading ? (
                    <View style={styles.loadingButtonContent}>
                      <ActivityIndicator size="small" color="#fff" />
                      <Text style={styles.passwordConfirmButtonText}>Updating...</Text>
                    </View>
                  ) : (
                    <Text style={styles.passwordConfirmButtonText}>Confirm Change</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleChangePassword}
              disabled={loading || !password}
            >
              <Text style={styles.saveButtonText}>Update Password</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.label}>Help & Support</Text>
          <TouchableOpacity
            style={styles.supportButton}
            onPress={() => navigation.navigate('Support')}
          >
            <Ionicons name="help-circle-outline" size={20} color="#fff" style={styles.supportIcon} />
            <Text style={styles.supportButtonText}>Contact Support</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.supportButton, { marginTop: 10, backgroundColor: '#4CAF50' }]}
            onPress={() => navigation.navigate('FAQ')}
          >
            <Ionicons name="document-text-outline" size={20} color="#fff" style={styles.supportIcon} />
            <Text style={styles.supportButtonText}>FAQ & Help</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.supportButton, { marginTop: 10, backgroundColor: '#2196F3' }]}
            onPress={() => navigation.navigate('LiveChat')}
          >
            <Ionicons name="chatbubble-outline" size={20} color="#fff" style={styles.supportIcon} />
            <Text style={styles.supportButtonText}>Live Chat Support</Text>
          </TouchableOpacity>
        </View>



        {/* Feed Customization for Buyers */}
        {!userInfo?.isSeller && (
          <View style={styles.section}>
            <Text style={styles.label}>Feed Customization</Text>

            {/* Recommendation Settings */}
            {useRecommendations !== undefined && recommendationThreshold !== undefined && updateRecommendationSettings && (
              <RecommendationSettings
                useRecommendations={useRecommendations}
                recommendationThreshold={recommendationThreshold}
                onUpdateSettings={updateRecommendationSettings}
                style={styles.recommendationSettings}
              />
            )}


          </View>
        )}

        <View style={styles.dangerSection}>
          <Text style={styles.dangerLabel}>Danger Zone</Text>
          <Text style={styles.dangerText}>
            Deleting your account will permanently remove all your data, including uploads, likes, and messages.
            This action cannot be undone.
          </Text>

          {showDeleteConfirm ? (
            <View style={styles.deleteConfirmContainer}>
              <View style={styles.confirmHeaderRow}>
                <Ionicons name="warning-outline" size={22} color="#FF3B30" />
                <Text style={[styles.confirmTitle, { color: '#FF3B30' }]}>Confirm Account Deletion</Text>
              </View>
              <Text style={styles.deleteConfirmText}>Please enter your password to confirm that you want to permanently delete your account:</Text>
              <TextInput
                style={[styles.input, styles.deleteInput]}                value={deletePassword}
                onChangeText={setDeletePassword}
                placeholder="Your Password"
                secureTextEntry
                autoFocus
                placeholderTextColor="#000"
                onFocus={() => {
                  // When the input is focused, scroll to make it visible above the keyboard
                  // No need for additional scrolling with the fixed KeyboardAvoidingView
                }}
              />
              <View style={styles.deleteButtonsRow}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => {
                    setShowDeleteConfirm(false);
                    setDeletePassword('');
                  }}
                  disabled={deletingAccount}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={handleDeleteAccount}
                  disabled={deletingAccount || !deletePassword}
                >
                  {deletingAccount ? (
                    <View style={styles.loadingButtonContent}>
                      <ActivityIndicator size="small" color="#fff" />
                      <Text style={styles.deleteButtonText}>Deleting...</Text>
                    </View>
                  ) : (
                    <Text style={styles.deleteButtonText}>Confirm Delete</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity style={styles.deleteAccountButton} onPress={confirmDeleteAccount}>
              <Ionicons name="trash-outline" size={20} color="#fff" style={styles.deleteIcon} />
              <Text style={styles.deleteAccountButtonText}>Delete Account</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 10,
    paddingHorizontal: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  headerButton: { padding: 8, width: 40 },
  headerButtonText: { color: '#FF6B6B', fontWeight: 'bold' },
  headerTitle: { fontSize: 22, fontWeight: 'bold', color: '#FF6B6B', letterSpacing: 1 },
  section: { padding: 20, borderBottomWidth: 1, borderBottomColor: '#eee' },
  label: { fontSize: 16, fontWeight: 'bold', color: '#333', marginBottom: 10 },
  input: { backgroundColor: '#f8f8f8', borderRadius: 8, paddingHorizontal: 15, paddingVertical: 12, fontSize: 16, borderWidth: 1, borderColor: '#eee', marginBottom: 15 },
  saveButton: { backgroundColor: '#FF6B6B', borderRadius: 25, height: 45, justifyContent: 'center', alignItems: 'center', marginBottom: 10 },
  saveButtonText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },



  // Support button styles
  supportButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  supportButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  supportIcon: {
    marginRight: 8,
  },

  // Password confirmation styles
  passwordConfirmContainer: {
    backgroundColor: '#FFF5F5',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FFDDDD',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  confirmHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  confirmTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF6B6B',
    marginLeft: 8,
  },
  confirmText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 15,
    lineHeight: 20,
  },
  confirmInput: {
    backgroundColor: '#fff',
    borderColor: '#FFDDDD',
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  passwordCancelButton: {
    backgroundColor: '#f8f8f8',
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  passwordCancelButtonText: {
    color: '#555',
    fontSize: 16,
    fontWeight: '500',
  },
  passwordConfirmButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 25,
    flex: 1,
    marginLeft: 15,
  },
  passwordConfirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Danger zone styles
  dangerSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginBottom: 50, // Reduced from 100 to 50 to avoid extra space
  },
  dangerLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginBottom: 10
  },
  dangerText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  deleteAccountButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  deleteAccountButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  deleteIcon: {
    marginRight: 8,
  },
  deleteConfirmContainer: {
    backgroundColor: '#FFF0F0',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FFCCCC',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  deleteConfirmText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 15,
    lineHeight: 20,
  },
  deleteInput: {
    backgroundColor: '#fff',
    borderColor: '#FFCCCC',
    marginBottom: 20,
  },
  deleteButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f8f8f8',
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#555',
    fontSize: 16,
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    marginLeft: 15,
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  recommendationSettings: {
    marginBottom: 10,
  },
});

export default SettingsScreen;