import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { migrateAllClothingItems, checkMigrationStatus } from '../utils/categoryMigration';

const CategoryMigrationScreen = ({ navigation }) => {
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [migrating, setMigrating] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState(null);
  const [migrationResults, setMigrationResults] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check migration status on component mount
  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    setLoading(true);
    try {
      const status = await checkMigrationStatus();
      setMigrationStatus(status);
    } catch (error) {
      console.error('Error checking migration status:', error);
      Alert.alert('Error', 'Failed to check migration status');
    } finally {
      setLoading(false);
    }
  };

  const startMigration = async () => {
    Alert.alert(
      'Start Migration',
      'This will update all clothing items with the new hierarchical category system. This process may take a few minutes. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Migration',
          onPress: async () => {
            setMigrating(true);
            setMigrationProgress(null);
            setMigrationResults(null);

            try {
              const results = await migrateAllClothingItems(50, (progress) => {
                setMigrationProgress(progress);
              });

              setMigrationResults(results);
              
              if (results.success) {
                Alert.alert('Success', results.message);
                // Refresh status
                await checkStatus();
              } else {
                Alert.alert('Error', results.error || 'Migration failed');
              }
            } catch (error) {
              console.error('Migration error:', error);
              Alert.alert('Error', 'Migration failed: ' + error.message);
            } finally {
              setMigrating(false);
              setMigrationProgress(null);
            }
          }
        }
      ]
    );
  };

  const renderMigrationStatus = () => {
    if (loading) {
      return (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" />
          <Text style={styles.statusText}>Checking migration status...</Text>
        </View>
      );
    }

    if (!migrationStatus) {
      return (
        <View style={styles.statusContainer}>
          <Ionicons name="warning" size={48} color="#FF9800" />
          <Text style={styles.statusText}>Unable to check migration status</Text>
        </View>
      );
    }

    const { totalItems, migratedItems, pendingItems, migrationComplete, percentage } = migrationStatus;

    return (
      <View style={styles.statusContainer}>
        <View style={styles.statusHeader}>
          <Ionicons 
            name={migrationComplete ? "checkmark-circle" : "time"} 
            size={48} 
            color={migrationComplete ? "#4CAF50" : "#FF9800"} 
          />
          <Text style={styles.statusTitle}>
            {migrationComplete ? 'Migration Complete' : 'Migration Needed'}
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{totalItems}</Text>
            <Text style={styles.statLabel}>Total Items</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: '#4CAF50' }]}>{migratedItems}</Text>
            <Text style={styles.statLabel}>Migrated</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: '#FF9800' }]}>{pendingItems}</Text>
            <Text style={styles.statLabel}>Pending</Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${percentage}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>{percentage}% Complete</Text>
        </View>
      </View>
    );
  };

  const renderMigrationProgress = () => {
    if (!migrationProgress) return null;

    const { processed, total, percentage } = migrationProgress;

    return (
      <View style={styles.progressContainer}>
        <Text style={styles.progressTitle}>Migration in Progress...</Text>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${percentage}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {processed} / {total} items ({percentage}%)
        </Text>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="chevron-back" size={28} color="#FF6B6B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Category Migration</Text>
        <TouchableOpacity
          onPress={checkStatus}
          style={styles.headerButton}
          disabled={loading || migrating}
        >
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.infoSection}>
          <Ionicons name="information-circle" size={24} color="#2196F3" />
          <Text style={styles.infoText}>
            This tool migrates existing clothing items to use the new hierarchical category system. 
            Items will be automatically categorized into broad and detailed categories, and tags will be extracted from titles and descriptions.
          </Text>
        </View>

        {renderMigrationStatus()}

        {migrating && renderMigrationProgress()}

        {migrationResults && (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Migration Results</Text>
            <Text style={styles.resultsText}>
              {migrationResults.success 
                ? `✅ ${migrationResults.message}`
                : `❌ ${migrationResults.error}`
              }
            </Text>
            {migrationResults.totalProcessed && (
              <Text style={styles.resultsDetail}>
                Processed {migrationResults.totalProcessed} items
              </Text>
            )}
          </View>
        )}

        <View style={styles.actionSection}>
          {!migrationStatus?.migrationComplete && (
            <TouchableOpacity
              style={[
                styles.migrateButton,
                (migrating || loading) && styles.migrateButtonDisabled
              ]}
              onPress={startMigration}
              disabled={migrating || loading}
            >
              {migrating ? (
                <View style={styles.buttonContent}>
                  <ActivityIndicator size="small" color="#fff" />
                  <Text style={styles.migrateButtonText}>Migrating...</Text>
                </View>
              ) : (
                <View style={styles.buttonContent}>
                  <Ionicons name="arrow-up-circle" size={20} color="#fff" />
                  <Text style={styles.migrateButtonText}>Start Migration</Text>
                </View>
              )}
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={checkStatus}
            disabled={loading || migrating}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="refresh" size={20} color="#2196F3" />
              <Text style={styles.refreshButtonText}>Refresh Status</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 10,
    paddingHorizontal: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  headerButton: {
    padding: 8,
    width: 40,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  content: {
    padding: 20,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
  },
  statusContainer: {
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  statusHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 10,
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 10,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
  },
  resultsContainer: {
    backgroundColor: '#F0F8F0',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 8,
  },
  resultsText: {
    fontSize: 14,
    color: '#2E7D32',
    marginBottom: 4,
  },
  resultsDetail: {
    fontSize: 12,
    color: '#4CAF50',
  },
  actionSection: {
    marginTop: 20,
  },
  migrateButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
  },
  migrateButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  refreshButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  migrateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  refreshButtonText: {
    color: '#2196F3',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default CategoryMigrationScreen;
