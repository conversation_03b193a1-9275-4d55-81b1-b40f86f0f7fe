import { db } from '../firebase.config';
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  serverTimestamp, 
  increment, 
  arrayUnion, 
  arrayRemove 
} from 'firebase/firestore';
import { getCurrentUserId, withAuthCheck } from './authUtils';

/**
 * Safely fetch a document from Firestore
 * @param {string} collectionPath - Path to the collection
 * @param {string} docId - Document ID
 * @returns {Promise<Object|null>} Document data or null if error/not found
 */
export const safeGetDocument = async (collectionPath, docId) => {
  return withAuthCheck(async () => {
    const docRef = doc(db, collectionPath, docId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() };
    }
    return null;
  });
};

/**
 * Safely fetch documents from a collection
 * @param {string} collectionPath - Path to the collection
 * @param {Object} queryOptions - Query options (where, orderBy, limit)
 * @returns {Promise<Array|null>} Array of documents or null if error
 */
export const safeGetCollection = async (collectionPath, queryOptions = {}) => {
  return withAuthCheck(async () => {
    const collectionRef = collection(db, collectionPath);
    
    // Build query based on options
    let queryRef = collectionRef;
    if (queryOptions.where) {
      queryRef = query(queryRef, where(queryOptions.where.field, queryOptions.where.operator, queryOptions.where.value));
    }
    if (queryOptions.orderBy) {
      queryRef = query(queryRef, orderBy(queryOptions.orderBy.field, queryOptions.orderBy.direction || 'asc'));
    }
    if (queryOptions.limitTo) {
      queryRef = query(queryRef, limit(queryOptions.limitTo));
    }
    
    const querySnapshot = await getDocs(queryRef);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  });
};

/**
 * Safely add a document to a collection
 * @param {string} collectionPath - Path to the collection
 * @param {Object} data - Document data
 * @returns {Promise<string|null>} Document ID or null if error
 */
export const safeAddDocument = async (collectionPath, data) => {
  return withAuthCheck(async () => {
    // Validate data to ensure no undefined values
    const validatedData = Object.entries(data).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {});
    
    const docRef = await addDoc(collection(db, collectionPath), validatedData);
    return docRef.id;
  });
};

/**
 * Safely update a document
 * @param {string} collectionPath - Path to the collection
 * @param {string} docId - Document ID
 * @param {Object} data - Data to update
 * @returns {Promise<boolean>} Success status
 */
export const safeUpdateDocument = async (collectionPath, docId, data) => {
  return withAuthCheck(async () => {
    // Validate data to ensure no undefined values
    const validatedData = Object.entries(data).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {});
    
    const docRef = doc(db, collectionPath, docId);
    await updateDoc(docRef, validatedData);
    return true;
  });
};

/**
 * Safely delete a document
 * @param {string} collectionPath - Path to the collection
 * @param {string} docId - Document ID
 * @returns {Promise<boolean>} Success status
 */
export const safeDeleteDocument = async (collectionPath, docId) => {
  return withAuthCheck(async () => {
    const docRef = doc(db, collectionPath, docId);
    await deleteDoc(docRef);
    return true;
  });
};
