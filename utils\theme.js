import { scaleFontSize, scaleWidth, scaleHeight } from './responsiveUtils';
import { createShadow, createBorderRadius, createTextStyle, createInputStyle } from './platformUtils';

// Color palette
const colors = {
  // Primary colors
  primary: '#FF6B6B',
  primaryDark: '#E55A5A',
  primaryLight: '#FF8A8A',
  
  // Secondary colors
  secondary: '#FFC0CB',
  secondaryDark: '#E5A9B3',
  secondaryLight: '#FFD6DE',
  
  // Neutral colors
  background: '#FFFFFF',
  backgroundLight: '#F8F8F8',
  backgroundDark: '#F0F0F0',
  
  // Text colors
  text: '#333333',
  textLight: '#666666',
  textLighter: '#999999',
  
  // Border colors
  border: '#EEEEEE',
  borderDark: '#DDDDDD',
  
  // Status colors
  success: '#4CD964',
  warning: '#FFCC00',
  error: '#FF3B30',
  info: '#5AC8FA',
  
  // Misc
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
};

// Typography
const typography = {
  // Font sizes
  fontSizes: {
    xs: scaleFontSize(12),
    sm: scaleFontSize(14),
    md: scaleFontSize(16),
    lg: scaleFontSize(18),
    xl: scaleFontSize(20),
    xxl: scaleFontSize(24),
    xxxl: scaleFontSize(28),
    display: scaleFontSize(32),
    title: scaleFontSize(40),
  },
  
  // Font weights
  fontWeights: {
    regular: 'normal',
    medium: '500',
    semibold: '600',
    bold: 'bold',
  },
  
  // Line heights
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    loose: 1.8,
  },
  
  // Create text styles with platform-specific adjustments
  createTextStyle: (options) => createTextStyle(options),
};

// Spacing
const spacing = {
  xs: scaleWidth(4),
  sm: scaleWidth(8),
  md: scaleWidth(16),
  lg: scaleWidth(24),
  xl: scaleWidth(32),
  xxl: scaleWidth(48),
};

// Borders
const borders = {
  radius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    round: 9999,
  },
  width: {
    thin: 0.5,
    normal: 1,
    thick: 2,
  },
  createBorderRadius: (radius) => createBorderRadius(radius),
};

// Shadows
const shadows = {
  light: createShadow({
    elevation: 2,
    opacity: 0.1,
    radius: 2,
    offset: { width: 0, height: 1 },
  }),
  medium: createShadow({
    elevation: 4,
    opacity: 0.15,
    radius: 3,
    offset: { width: 0, height: 2 },
  }),
  heavy: createShadow({
    elevation: 8,
    opacity: 0.2,
    radius: 5,
    offset: { width: 0, height: 3 },
  }),
  createShadow,
};

// Layout
const layout = {
  screenPadding: scaleWidth(16),
  contentWidth: '100%',
  maxContentWidth: 500, // Max width for tablets
};

// Input styles
const inputs = {
  height: scaleHeight(50),
  borderRadius: borders.radius.md,
  borderWidth: borders.width.normal,
  paddingHorizontal: spacing.md,
  createInputStyle,
};

// Button styles
const buttons = {
  height: scaleHeight(50),
  borderRadius: borders.radius.md,
  paddingHorizontal: spacing.lg,
};

// Animation durations
const animations = {
  fast: 150,
  medium: 300,
  slow: 500,
};

// Z-index values
const zIndex = {
  base: 1,
  card: 10,
  modal: 100,
  overlay: 1000,
  tooltip: 2000,
  max: 9999,
};

// Export the theme
export default {
  colors,
  typography,
  spacing,
  borders,
  shadows,
  layout,
  inputs,
  buttons,
  animations,
  zIndex,
};
