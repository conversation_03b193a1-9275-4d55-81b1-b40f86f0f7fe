/**
 * System Override Configuration
 * 
 * This file contains system-level overrides that can be toggled during development
 * to force certain behaviors or bypass environment checks.
 * 
 * IMPORTANT: These overrides should typically be set to false in production!
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * System override state
 */
export const systemOverrides = {
  // Force the app to act like a production build
  forceProductionMode: false,
  
  // Force the app to act like it's not running in Expo Go
  forceNotExpoGo: true,
  
  // Force Razorpay to be available
  forceRazorpayAvailable: true
};

/**
 * Save override settings to AsyncStorage
 */
export const saveOverrides = async () => {
  try {
    await AsyncStorage.setItem('systemOverrides', JSON.stringify(systemOverrides));
  } catch (error) {
    console.error('Failed to save system overrides:', error);
  }
};

/**
 * Load override settings from AsyncStorage
 */
export const loadOverrides = async () => {
  try {
    // Retrieve stored overrides (if any)
    const storedOverrides = await AsyncStorage.getItem('systemOverrides');
    if (storedOverrides) {
      const parsed = JSON.parse(storedOverrides);
      Object.assign(systemOverrides, parsed);
    }
  } catch (error) {
    console.error('Failed to load system overrides:', error);
  }
};

/**
 * Toggle a specific override setting
 */
export const toggleOverride = async (key) => {
  if (key in systemOverrides) {
    systemOverrides[key] = !systemOverrides[key];
    await saveOverrides();
    return true;
  }
  return false;
};

// Load overrides when this module is imported
loadOverrides().catch(console.error);
