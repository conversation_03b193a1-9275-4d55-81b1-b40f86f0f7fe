import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { db, auth } from '../firebase.config';
import { doc, updateDoc, getDoc, collection, addDoc } from 'firebase/firestore';

// Configure how notifications are handled when the app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

/**
 * Register for push notifications and get the Expo push token
 * @returns {Promise<string|null>} The Expo push token or null if registration failed
 */
export const registerForPushNotificationsAsync = async () => {
  let token = null;

  // Check if device supports push notifications
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('support', {
      name: 'Support Notifications',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF6B6B',
    });

    await Notifications.setNotificationChannelAsync('orders', {
      name: 'Order Notifications',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#4CAF50',
    });

    await Notifications.setNotificationChannelAsync('messages', {
      name: 'Message Notifications',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#2196F3',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.warn('Failed to get push token for push notification!');
      return null;
    }
    
    try {
      token = (await Notifications.getExpoPushTokenAsync()).data;
      console.log('Got push token:', token);
      
      // Save token to user's Firestore document
      if (auth.currentUser && token) {
        await savePushTokenToFirestore(token);
      }
    } catch (error) {
      console.error('Error getting push token:', error);
    }
  } else {
    console.warn('Must use physical device for Push Notifications');
  }

  return token;
};

/**
 * Save push token to user's Firestore document
 * @param {string} token - The Expo push token
 */
const savePushTokenToFirestore = async (token) => {
  if (!auth.currentUser) return;
  
  try {
    const userRef = doc(db, 'users', auth.currentUser.uid);
    await updateDoc(userRef, {
      pushToken: token,
      lastTokenUpdate: new Date()
    });
    console.log('Push token saved to Firestore');
  } catch (error) {
    console.error('Error saving push token:', error);
  }
};

/**
 * Send a push notification to a specific user
 * @param {string} userId - The user ID to send notification to
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data to send with notification
 * @param {string} channelId - Android notification channel ID
 */
export const sendPushNotificationToUser = async (userId, title, body, data = {}, channelId = 'default') => {
  try {
    // Get user's push token from Firestore
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.warn('User document not found:', userId);
      return;
    }
    
    const userData = userDoc.data();
    const pushToken = userData.pushToken;
    
    if (!pushToken) {
      console.warn('No push token found for user:', userId);
      return;
    }
    
    // Prepare notification payload
    const message = {
      to: pushToken,
      sound: 'default',
      title: title,
      body: body,
      data: data,
      channelId: channelId,
      priority: 'high',
    };
    
    // Send notification using Expo's push notification service
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
    
    const result = await response.json();
    
    if (result.data && result.data[0] && result.data[0].status === 'error') {
      console.error('Push notification error:', result.data[0].details);
    } else {
      console.log('Push notification sent successfully');
      
      // Log notification to Firestore for tracking
      await logNotification(userId, title, body, data, 'push');
    }
    
    return result;
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
};

/**
 * Log notification to Firestore for tracking and history
 * @param {string} userId - The user ID
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data
 * @param {string} type - Notification type (push, email, etc.)
 */
const logNotification = async (userId, title, body, data, type) => {
  try {
    await addDoc(collection(db, 'notificationLogs'), {
      userId,
      title,
      body,
      data,
      type,
      sentAt: new Date(),
      read: false
    });
  } catch (error) {
    console.error('Error logging notification:', error);
  }
};

/**
 * Send support ticket notification to user
 * @param {string} userId - The user ID
 * @param {string} ticketId - The support ticket ID
 * @param {string} subject - Ticket subject
 * @param {string} status - Ticket status
 * @param {string} message - Notification message
 */
export const sendSupportTicketNotification = async (userId, ticketId, subject, status, message) => {
  const title = `Support Ticket Update`;
  const body = `Your ticket "${subject}" has been ${status.toLowerCase()}`;
  
  const data = {
    type: 'support_ticket',
    ticketId,
    subject,
    status,
    screen: 'SupportScreen'
  };
  
  await sendPushNotificationToUser(userId, title, body, data, 'support');
};

/**
 * Send order notification to user
 * @param {string} userId - The user ID
 * @param {string} orderId - The order ID
 * @param {string} status - Order status
 * @param {string} message - Custom message
 */
export const sendOrderNotification = async (userId, orderId, status, message) => {
  const title = `Order Update`;
  const body = message || `Your order status has been updated to ${status}`;
  
  const data = {
    type: 'order',
    orderId,
    status,
    screen: 'OrderDetailsScreen'
  };
  
  await sendPushNotificationToUser(userId, title, body, data, 'orders');
};

/**
 * Send message notification to user
 * @param {string} userId - The user ID
 * @param {string} senderId - The sender ID
 * @param {string} senderName - The sender name
 * @param {string} messageText - The message text
 * @param {string} threadId - The chat thread ID
 */
export const sendMessageNotification = async (userId, senderId, senderName, messageText, threadId) => {
  const title = `New message from ${senderName}`;
  const body = messageText.length > 50 ? `${messageText.substring(0, 50)}...` : messageText;
  
  const data = {
    type: 'message',
    senderId,
    senderName,
    threadId,
    screen: 'ChatThreadScreen'
  };
  
  await sendPushNotificationToUser(userId, title, body, data, 'messages');
};

/**
 * Handle notification response when user taps on notification
 * @param {Object} response - The notification response
 * @param {Object} navigation - Navigation object
 */
export const handleNotificationResponse = (response, navigation) => {
  const data = response.notification.request.content.data;
  
  if (!data || !data.type) return;
  
  switch (data.type) {
    case 'support_ticket':
      navigation.navigate('SupportScreen', { ticketId: data.ticketId });
      break;
    case 'order':
      navigation.navigate('OrderDetailsScreen', { orderId: data.orderId });
      break;
    case 'message':
      navigation.navigate('ChatThreadScreen', { 
        threadId: data.threadId,
        otherUserId: data.senderId,
        otherUserName: data.senderName
      });
      break;
    default:
      console.log('Unknown notification type:', data.type);
  }
};

/**
 * Get user's notification preferences
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} Notification preferences
 */
export const getNotificationPreferences = async (userId) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      return userData.notificationPreferences || {
        pushNotifications: true,
        emailNotifications: true,
        supportTickets: true,
        orderUpdates: true,
        messages: true,
        marketing: false
      };
    }
  } catch (error) {
    console.error('Error getting notification preferences:', error);
  }
  
  // Default preferences
  return {
    pushNotifications: true,
    emailNotifications: true,
    supportTickets: true,
    orderUpdates: true,
    messages: true,
    marketing: false
  };
};

/**
 * Update user's notification preferences
 * @param {string} userId - The user ID
 * @param {Object} preferences - New notification preferences
 */
export const updateNotificationPreferences = async (userId, preferences) => {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      notificationPreferences: preferences,
      preferencesUpdatedAt: new Date()
    });
    console.log('Notification preferences updated');
  } catch (error) {
    console.error('Error updating notification preferences:', error);
  }
};

/**
 * Schedule a local notification (for reminders, etc.)
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Date} scheduledDate - When to show the notification
 * @param {Object} data - Additional data
 */
export const scheduleLocalNotification = async (title, body, scheduledDate, data = {}) => {
  try {
    const identifier = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: 'default',
      },
      trigger: {
        date: scheduledDate,
      },
    });
    
    console.log('Local notification scheduled:', identifier);
    return identifier;
  } catch (error) {
    console.error('Error scheduling local notification:', error);
  }
};

/**
 * Cancel a scheduled local notification
 * @param {string} identifier - The notification identifier
 */
export const cancelLocalNotification = async (identifier) => {
  try {
    await Notifications.cancelScheduledNotificationAsync(identifier);
    console.log('Local notification cancelled:', identifier);
  } catch (error) {
    console.error('Error cancelling local notification:', error);
  }
};

/**
 * Get all pending notifications for the user
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} Array of notification logs
 */
export const getUserNotifications = async (userId) => {
  try {
    const notificationsRef = collection(db, 'notificationLogs');
    const q = query(
      notificationsRef,
      where('userId', '==', userId),
      orderBy('sentAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      sentAt: doc.data().sentAt?.toDate() || new Date()
    }));
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return [];
  }
};

/**
 * Mark notification as read
 * @param {string} notificationId - The notification ID
 */
export const markNotificationAsRead = async (notificationId) => {
  try {
    const notificationRef = doc(db, 'notificationLogs', notificationId);
    await updateDoc(notificationRef, {
      read: true,
      readAt: new Date()
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
  }
};
