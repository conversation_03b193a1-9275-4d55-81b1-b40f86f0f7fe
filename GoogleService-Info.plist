<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>900945998905-6gtlmfru9mou7iqiespf8nhjis5ot1k6.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.900945998905-6gtlmfru9mou7iqiespf8nhjis5ot1k6</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>900945998905-540eimi0u8gr2jgpjvanbltkmumpa5fh.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyAqcvg_jUU80kQ5mqleIcoQtr20L2iy1h8</string>
	<key>GCM_SENDER_ID</key>
	<string>900945998905</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.styleswipe.styleswipe</string>
	<key>PROJECT_ID</key>
	<string>styleswipe-67cb3</string>
	<key>STORAGE_BUCKET</key>
	<string>styleswipe-67cb3.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:900945998905:ios:2d401ab1278bc5d73a49e8</string>
</dict>
</plist>