import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  TextInput,
  Platform,
  StatusBar,
  Dimensions // Import Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { collection, query, getDocs, doc, deleteDoc, getDoc, updateDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { initializeRazorpayPayment } from '../utils/paymentUtils';
// Import the custom modal
import QuantitySelectionModal from '../components/QuantitySelectionModal';
import { batchCheckStock } from '../utils/stockUtils';
import { StockWarning } from '../components/StockDisplay';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Responsive constants
const ITEM_IMAGE_SIZE = SCREEN_WIDTH * 0.28; // Approx 110 on a ~390dp screen
const ITEM_PADDING = SCREEN_WIDTH * 0.035; // Approx 14
const TITLE_FONT_SIZE = SCREEN_WIDTH * 0.04; // Approx 16
const BRAND_FONT_SIZE = SCREEN_WIDTH * 0.035; // Approx 14
const SIZE_FONT_SIZE = SCREEN_WIDTH * 0.033; // Approx 13
const SUBTOTAL_FONT_SIZE = SCREEN_WIDTH * 0.04; // Approx 16
const SUBTOTAL_LABEL_FONT_SIZE = SCREEN_WIDTH * 0.035; // Approx 14
const DROPDOWN_TEXT_FONT_SIZE = SCREEN_WIDTH * 0.035; // Approx 14
const CHECKOUT_BUTTON_FONT_SIZE = SCREEN_WIDTH * 0.04; // Approx 16
const TOTAL_PRICE_FONT_SIZE = SCREEN_WIDTH * 0.045; // Approx 18
const TOTAL_LABEL_FONT_SIZE = SCREEN_WIDTH * 0.04; // Approx 16
const HEADER_TITLE_FONT_SIZE = SCREEN_WIDTH * 0.045; // Approx 18
const EMPTY_TEXT_FONT_SIZE = SCREEN_WIDTH * 0.045; // Approx 18
const BROWSE_BUTTON_TEXT_FONT_SIZE = SCREEN_WIDTH * 0.04; // Approx 16
const LOADING_TEXT_FONT_SIZE = SCREEN_WIDTH * 0.04; // Approx 16
const UPDATE_BUTTON_TEXT_FONT_SIZE = SCREEN_WIDTH * 0.03; // Approx 12

const CartScreen = ({ navigation }) => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processingCheckout, setProcessingCheckout] = useState(false);
  const [userAddress, setUserAddress] = useState(null);
  const [inputQuantities, setInputQuantities] = useState({});
  const [showCustomInput, setShowCustomInput] = useState({});
  const [validationErrors, setValidationErrors] = useState({});
  // Add state for quantity selection modal
  const [isQuantityModalVisible, setIsQuantityModalVisible] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState(null);
  const [stockValidation, setStockValidation] = useState([]);
  const [showStockWarning, setShowStockWarning] = useState(false);
  const currentUserId = auth.currentUser?.uid;

  // Calculate total price
  const totalPrice = useMemo(() => {
    return cartItems.reduce((total, item) => {
      const itemPrice = item.price || 0;
      const quantity = item.quantity || 1;
      return total + (itemPrice * quantity);
    }, 0);
  }, [cartItems]);

  useEffect(() => {
    fetchCartItems();
    if (currentUserId) {
      fetchUserAddress();
    }
  }, [currentUserId]);

  // Validate stock for all cart items
  useEffect(() => {
    if (cartItems.length > 0) {
      validateCartStock();
    }
  }, [cartItems]);

  const fetchCartItems = async () => {
    if (!currentUserId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const cartCollectionRef = collection(db, 'users', currentUserId, 'cart');
      const querySnapshot = await getDocs(cartCollectionRef);

      const items = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Initialize input quantities with current quantities
      const initialQuantities = {};
      items.forEach(item => {
        initialQuantities[item.id] = (item.quantity || 1).toString();
      });

      setCartItems(items);
      setInputQuantities(initialQuantities);
      console.log(`[Cart] Loaded ${items.length} cart items`);
    } catch (error) {
      console.error('[Cart] Error fetching cart items:', error);
      Alert.alert('Error', 'Failed to load cart items');
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (cartItemId) => {
    if (!currentUserId) return;

    try {
      await deleteDoc(doc(db, 'users', currentUserId, 'cart', cartItemId));
      setCartItems(prevItems => prevItems.filter(item => item.id !== cartItemId));

      // Also remove from input quantities
      setInputQuantities(prev => {
        const newQuantities = { ...prev };
        delete newQuantities[cartItemId];
        return newQuantities;
      });

      console.log(`[Cart] Item ${cartItemId} removed from cart`);
    } catch (error) {
      console.error('[Cart] Error removing item from cart:', error);
      Alert.alert('Error', 'Failed to remove item from cart');
    }
  };

  const updateItemQuantity = async (itemId, newQuantity) => {
    if (!currentUserId) return;

    try {
      // Update quantity in Firestore
      const itemRef = doc(db, 'users', currentUserId, 'cart', itemId);
      await updateDoc(itemRef, { quantity: newQuantity });

      // Update local state
      setCartItems(prevItems =>
        prevItems.map(item =>
          item.id === itemId
            ? { ...item, quantity: newQuantity }
            : item
        )
      );

      // Update input quantity state
      setInputQuantities(prev => ({
        ...prev,
        [itemId]: newQuantity.toString()
      }));

      console.log(`[Cart] Updated quantity for item ${itemId} to ${newQuantity}`);
    } catch (error) {
      console.error('[Cart] Error updating item quantity:', error);
      Alert.alert('Error', 'Failed to update item quantity');
    }
  };

  const fetchUserAddress = async () => {
    try {
      const userDocRef = doc(db, 'users', currentUserId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.address) {
          setUserAddress(userData.address);
        }
      }
    } catch (error) {
      console.error('[Cart] Error fetching user address:', error);
    }
  };

  // Validate stock for all cart items
  const validateCartStock = async () => {
    try {
      const itemsToCheck = cartItems.map(item => ({
        itemId: item.itemId,
        quantity: item.quantity || 1
      }));

      const stockResults = await batchCheckStock(itemsToCheck);
      setStockValidation(stockResults);

      // Check if any items have stock issues
      const hasStockIssues = stockResults.some(result => !result.available);
      setShowStockWarning(hasStockIssues);

      // Show alert for out of stock items
      const outOfStockItems = stockResults.filter(result => !result.available);
      if (outOfStockItems.length > 0) {
        const itemNames = outOfStockItems.map(result => {
          const cartItem = cartItems.find(item => item.itemId === result.itemId);
          return cartItem?.title || 'Unknown item';
        });

        Alert.alert(
          'Stock Alert',
          `The following items are no longer available or have insufficient stock:\n\n${itemNames.join('\n')}\n\nPlease update your cart.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error validating cart stock:', error);
    }
  };

  // Listen for navigation focus events to refresh address data
  useFocusEffect(
    React.useCallback(() => {
      if (currentUserId) {
        fetchUserAddress();
      }
      return () => { };
    }, [currentUserId])
  );

  const handleCheckout = async () => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    if (cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Your cart is empty. Add items to proceed with checkout.');
      return;
    }

    // Check for out-of-stock items before proceeding
    const outOfStockItems = stockValidation.filter(result => !result.available);
    if (outOfStockItems.length > 0) {
      const itemNames = outOfStockItems.map(result => {
        const cartItem = cartItems.find(item => item.itemId === result.itemId);
        return cartItem?.title || 'Unknown item';
      });

      Alert.alert(
        'Cannot Proceed to Checkout',
        `The following items are out of stock or have insufficient quantity:\n\n${itemNames.join('\n')}\n\nPlease remove these items or update quantities before checkout.`,
        [{ text: 'OK' }]
      );
      return;
    }

    if (!userAddress) {
      Alert.alert(
        'Address Required',
        'Please add your shipping address before checkout.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Add Address',
            onPress: () => navigation.navigate('AddressScreen')
          }
        ]
      );
      return;
    }

    // Navigate to checkout screen
    navigation.navigate('Checkout', {
      cartItems,
      totalPrice,
      userAddress
    });
  };

  // Function to open the quantity modal
  const openQuantityModal = (itemId) => {
    setSelectedItemId(itemId);
    setIsQuantityModalVisible(true);
  };

  // Function to handle quantity selection from modal
  const handleQuantitySelection = (value) => {
    if (!selectedItemId) return;

    if (value === 'custom') {
      // Switch to custom input mode with empty field
      setInputQuantities(prev => ({
        ...prev,
        [selectedItemId]: ''  // Start with empty field
      }));
      setShowCustomInput(prev => ({
        ...prev,
        [selectedItemId]: true
      }));
      setValidationErrors(prev => ({
        ...prev,
        [selectedItemId]: false
      }));
    } else if (value === 'remove') {
      // Remove item from cart
      removeFromCart(selectedItemId);
    } else {
      // Update with selected quantity
      const newQuantity = parseInt(value, 10);
      updateItemQuantity(selectedItemId, newQuantity);
      setShowCustomInput(prev => ({
        ...prev,
        [selectedItemId]: false
      }));
    }

    // Reset selected item
    setSelectedItemId(null);
  };

  const renderCartItem = ({ item }) => {
    const quantity = item.quantity || 1;
    const price = item.price || 0;
    const subtotal = price * quantity;
    // Use empty string for custom input mode, otherwise use the current quantity
    const inputQuantity = inputQuantities[item.id] !== undefined ? inputQuantities[item.id] : quantity.toString();
    const isCustomInput = showCustomInput[item.id] || false;
    const hasValidationError = validationErrors[item.id] || false;

    // Handle dropdown quantity selection
    const handleQuantitySelect = (itemId, value) => {
      if (value === 'custom') {
        // Switch to custom input mode with empty field
        setInputQuantities(prev => ({
          ...prev,
          [itemId]: ''  // Start with empty field
        }));
        setShowCustomInput(prev => ({
          ...prev,
          [itemId]: true
        }));
        setValidationErrors(prev => ({
          ...prev,
          [itemId]: false
        }));
      } else if (value === 'remove') {
        // Remove item from cart
        removeFromCart(itemId);
      } else {
        // Update with selected quantity
        const newQuantity = parseInt(value, 10);
        updateItemQuantity(itemId, newQuantity);
        setShowCustomInput(prev => ({
          ...prev,
          [itemId]: false
        }));
      }
    };

    const handleUpdateQuantity = () => {
      // Check if the input is blank/empty
      if (inputQuantity === '') {
        // Show validation error
        setValidationErrors(prev => ({
          ...prev,
          [item.id]: true
        }));
        return;
      }

      // Clear validation error
      setValidationErrors(prev => ({
        ...prev,
        [item.id]: false
      }));

      const newQuantity = parseInt(inputQuantity, 10);

      // Make sure it's a valid number and within range
      if (!isNaN(newQuantity) && newQuantity <= 999) {
        if (newQuantity === 0) {
          // If quantity is 0, remove the item
          removeFromCart(item.id);
        } else {
          // Update the quantity
          updateItemQuantity(item.id, newQuantity);
          // Exit custom input mode
          setShowCustomInput(prev => ({
            ...prev,
            [item.id]: false
          }));
        }
      }
    };

    // Simple function to update the input value
    const handleInputChange = (text) => {
      setInputQuantities(prev => ({
        ...prev,
        [item.id]: text
      }));
    };

    return (
      <View style={styles.cartItem}>
        <TouchableOpacity
          onPress={() => navigation.navigate('ItemDetails', { itemId: item.itemId })}
          style={styles.itemImageContainer}
        >
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.itemImage}
            resizeMode="cover"
          />
        </TouchableOpacity>

        <View style={styles.itemContent}>
          {/* Item Title, Brand and Size */}
          <View style={styles.itemHeaderRow}>
            <View style={styles.itemBasicInfo}>
              <Text style={styles.itemTitle} numberOfLines={1} ellipsizeMode="tail">
                {item.title || 'Untitled Item'}
              </Text>
              {item.brand && <Text style={styles.itemBrand}>{item.brand}</Text>}
              {item.gender && <Text style={styles.itemGender}>{item.gender}</Text>}
            </View>
            <View style={styles.sizeContainer}>
              {item.size && <Text style={styles.itemSize}>Size: {item.size}</Text>}
            </View>
          </View>

          {/* Quantity Controls and Subtotal */}
          <View style={styles.controlsRow}>
            {isCustomInput ? (
              <View style={styles.customInputContainer}>
                <TextInput
                  style={[styles.quantityText, hasValidationError && styles.errorInput]}
                  value={inputQuantity}
                  keyboardType="numeric"
                  maxLength={3}
                  onChangeText={handleInputChange}
                  placeholder="Enter quantity"
                  selectTextOnFocus={true}
                  autoFocus={true}
                  caretHidden={false}
                  placeholderTextColor="#000"
                />
                <TouchableOpacity
                  style={styles.updateButton}
                  onPress={handleUpdateQuantity}
                >
                  <Text style={styles.updateButtonText}>Update</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.quantityDropdown}
                  onPress={() => openQuantityModal(item.id)}
                >
                  <Text style={styles.dropdownText}>Qty: {quantity}</Text>
                  <Ionicons name="chevron-down" size={16} color="#666" />
                </TouchableOpacity>

                <View style={styles.subtotalContainer}>
                  <Text style={styles.subtotalLabel}>Subtotal:</Text>
                  <Text style={styles.itemSubtotal}>₹{Math.round(subtotal)}</Text>
                </View>
              </>
            )}
          </View>

          {/* Validation error message */}
          {hasValidationError && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Please enter a quantity</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderEmptyCart = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="cart-outline" size={80} color="#ccc" />
      <Text style={styles.emptyText}>Your cart is empty</Text>
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => navigation.navigate('ClothingFeed')}
      >
        <Text style={styles.browseButtonText}>Browse Items</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Quantity Selection Modal */}
      <QuantitySelectionModal
        visible={isQuantityModalVisible}
        onClose={() => setIsQuantityModalVisible(false)}
        onSelect={handleQuantitySelection}
      />

      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>My Cart</Text>
          <View style={styles.rightPlaceholder} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Loading cart items...</Text>
          </View>
        ) : (
          <>
            {/* Stock Warning */}
            {showStockWarning && cartItems.length > 0 && (
              <StockWarning
                items={cartItems.map(item => ({
                  title: item.title,
                  stock: item.stock || 0
                }))}
              />
            )}

            <FlatList
              data={cartItems}
              renderItem={renderCartItem}
              keyExtractor={item => item.id}
              contentContainerStyle={[
                styles.listContent,
                cartItems.length > 0 ? { paddingBottom: 140 } : {}
              ]}
              ListEmptyComponent={renderEmptyCart}
              showsVerticalScrollIndicator={true}
              style={styles.flatListStyle}
            />

            {cartItems.length > 0 && (
              <View style={styles.checkoutContainer}>
                <View style={styles.totalContainer}>
                  <Text style={styles.totalLabel}>Total:</Text>
                  <Text style={styles.totalPrice}>₹{Math.round(totalPrice)}</Text>
                </View>
                <TouchableOpacity
                  style={styles.checkoutButton}
                  onPress={handleCheckout}
                  disabled={processingCheckout}
                >
                  {processingCheckout ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
                  )}
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 // Added for Android
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa', // Light gray background for the whole screen content
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ITEM_PADDING, // Use responsive padding
    paddingVertical: ITEM_PADDING,   // Use responsive padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
    backgroundColor: '#fff',
    // elevation: 2, // Keep or remove based on Android appearance
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 1 },
    // shadowOpacity: 0.1,
    // shadowRadius: 2,
  },
  backButton: {
    padding: ITEM_PADDING / 2, // Responsive padding
    borderRadius: 20,
    // backgroundColor: 'rgba(0,0,0,0.03)', // Optional: subtle background
  },
  headerTitle: {
    fontSize: HEADER_TITLE_FONT_SIZE, // Responsive font size
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  rightPlaceholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    fontSize: LOADING_TEXT_FONT_SIZE, // Responsive font size
    fontWeight: '500',
  },
  listContent: {
    padding: ITEM_PADDING, // Use responsive padding
    flexGrow: 1,
  },
  flatListStyle: {
    flex: 1,
  },
  cartItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: ITEM_PADDING, // Use responsive margin
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: Platform.OS === 'android' ? 0.5 : 1, // Thinner border for Android
    borderColor: 'rgba(0,0,0,0.08)', // Slightly more visible border
  },
  itemImageContainer: {
    width: ITEM_IMAGE_SIZE,
    height: ITEM_IMAGE_SIZE,
    borderRadius: 10, // Slightly less rounded corners for the image container
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
    margin: ITEM_PADDING * 0.6, // Responsive margin around image
  },
  itemImage: {
    width: '100%',
    height: '100%',
    // borderRadius: 12, // Already handled by parent's overflow:hidden
  },
  itemContent: {
    flex: 1,
    paddingHorizontal: ITEM_PADDING, // Horizontal padding
    paddingVertical: ITEM_PADDING * 0.8, // Vertical padding
    justifyContent: 'space-between',
  },
  itemHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start', // Align items to the top
    width: '100%',
    marginBottom: ITEM_PADDING / 3, // Responsive margin
  },
  itemBasicInfo: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    flexShrink: 1, // Allow this to shrink if title is long
    paddingRight: ITEM_PADDING / 2, // Responsive padding
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginTop: ITEM_PADDING / 2, // Responsive margin
  },
  itemTitle: {
    fontSize: TITLE_FONT_SIZE,
    fontWeight: '600',
    color: '#333',
    marginBottom: 3,
    letterSpacing: 0.2,
  },
  itemBrand: {
    fontSize: BRAND_FONT_SIZE,
    color: '#666',
    marginBottom: 3,
    fontWeight: '500',
  },
  itemGender: {
    fontSize: BRAND_FONT_SIZE * 0.9,
    color: '#888',
    fontStyle: 'italic',
    fontWeight: '400',
  },
  itemCategory: {
    fontSize: 14,
    color: '#888',
    marginBottom: 3,
    fontWeight: '500',
  },
  itemSize: {
    fontSize: SIZE_FONT_SIZE,
    color: '#666',
    fontWeight: '500',
    backgroundColor: 'rgba(0,0,0,0.03)',
    paddingHorizontal: ITEM_PADDING * 0.5, // Responsive padding
    paddingVertical: ITEM_PADDING * 0.2,   // Responsive padding
    borderRadius: 12,
    alignSelf: 'flex-start', // Keep this
    textAlign: 'right', // Try to align text to right within its container
    overflow: 'hidden', // Ensure text doesn't overflow rounded corners
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SCREEN_HEIGHT * 0.1, // Responsive padding
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: ITEM_PADDING, // Responsive margin
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: Platform.OS === 'android' ? 0.5 : 1,
    borderColor: 'rgba(0,0,0,0.08)',
  },
  emptyText: {
    fontSize: EMPTY_TEXT_FONT_SIZE, // Responsive font size
    color: '#999',
    marginTop: 20,
    marginBottom: 24,
    fontWeight: '500',
    textAlign: 'center',
  },
  browseButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: ITEM_PADDING * 0.8,    // Responsive padding
    paddingHorizontal: ITEM_PADDING * 1.5, // Responsive padding
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  browseButtonText: {
    color: '#fff',
    fontSize: BROWSE_BUTTON_TEXT_FONT_SIZE, // Responsive font size
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4CAF50',
    marginTop: 4,
  },
  customInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  quantityDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: ITEM_PADDING * 0.6, // Responsive padding
    paddingVertical: ITEM_PADDING * 0.4,   // Responsive padding
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
    minWidth: 80,
  },
  dropdownText: {
    fontSize: DROPDOWN_TEXT_FONT_SIZE,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
  },
  quantityButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  quantityText: {
    fontSize: DROPDOWN_TEXT_FONT_SIZE, // Consistent font size
    fontWeight: '600',
    color: '#333',
    width: '65%',
    textAlign: 'center',
    height: ITEM_PADDING * 2.2, // Responsive height
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    paddingHorizontal: 8,
    paddingVertical: 0,
  },
  errorInput: {
    borderColor: '#FF3B30',
    borderWidth: 1,
  },
  errorContainer: {
    marginTop: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 12,
    color: '#FF3B30',
    fontWeight: '500',
  },
  updateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: ITEM_PADDING * 0.5,    // Responsive padding
    paddingHorizontal: ITEM_PADDING * 0.75, // Responsive padding
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
    height: ITEM_PADDING * 2.2, // Responsive height
  },
  updateButtonText: {
    color: '#fff',
    fontSize: UPDATE_BUTTON_TEXT_FONT_SIZE, // Responsive font size
    fontWeight: '600',
  },
  sizeContainer: {
    // alignItems: 'flex-end', // Content within itemSize will handle its alignment
    // justifyContent: 'flex-start',
    // No specific flex needed, let it take natural width
    // marginLeft: 'auto', // Push to the right if itemBasicInfo doesn't fill
  },
  subtotalContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    // flexGrow: 1, // Allow it to take space if needed
    // marginLeft: ITEM_PADDING / 2, // Add some space from quantity
  },
  subtotalLabel: {
    fontSize: SUBTOTAL_LABEL_FONT_SIZE,
    color: '#666',
    marginRight: 6,
    fontWeight: '500',
  },
  itemSubtotal: {
    fontSize: SUBTOTAL_FONT_SIZE,
    color: '#4CAF50',
    fontWeight: '700',
  },
  checkoutContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    paddingHorizontal: ITEM_PADDING, // Responsive padding
    paddingVertical: ITEM_PADDING * 0.8, // Responsive padding
    paddingBottom: Platform.OS === 'ios' ? ITEM_PADDING * 1.5 : ITEM_PADDING * 0.8, // Extra padding for iOS home indicator
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 8,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    minHeight: 120, // Ensure minimum height for proper spacing calculation
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
  },
  totalLabel: {
    fontSize: TOTAL_LABEL_FONT_SIZE, // Responsive font size
    color: '#333',
    fontWeight: '600',
  },
  totalPrice: {
    fontSize: TOTAL_PRICE_FONT_SIZE, // Responsive font size
    color: '#FF6B6B', // Match button color for emphasis
    fontWeight: '700',
  },
  checkoutButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: ITEM_PADDING * 0.8,    // Responsive padding
    paddingHorizontal: ITEM_PADDING * 1.2, // Responsive padding
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  checkoutButtonText: {
    color: '#fff',
    fontSize: CHECKOUT_BUTTON_FONT_SIZE, // Responsive font size
    fontWeight: '700',
    letterSpacing: 0.3,
  },
});

export default CartScreen;
