import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db, functions } from '../firebase.config';
import { signOut } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

const VerificationPendingScreen = ({ navigation }) => {
  const user = auth.currentUser;
  const [loading, setLoading] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [error, setError] = useState('');

  // Check if user is logged in when the screen loads
  useEffect(() => {
    // We're not automatically sending the verification code anymore
    // Just check if the user is logged in
    if (!user) {
      console.log('No user found in VerificationPendingScreen');
      // Navigate back to login if no user is found
      navigation.reset({
        index: 0,
        routes: [{ name: 'Welcome' }],
      });
    } else {
      console.log('User found in VerificationPendingScreen:', user.email);
    }
  }, [user, navigation]);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      // Explicitly navigate to Welcome screen after logout
      console.log("User logged out from verification pending screen");
      navigation.reset({
        index: 0,
        routes: [{ name: 'Welcome' }],
      });
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'For any questions about your verification status, please email <NAME_EMAIL>',
      [{ text: 'OK' }]
    );
  };

  const handleEnterVerificationCode = () => {
    // Navigate to seller code verification screen
    console.log('Navigating to SellerCodeVerification screen');

    try {
      // Get the current navigation state
      const state = navigation.getState();
      console.log('Current navigation state:', JSON.stringify(state));

      // Check if we're in the AuthStack
      if (state.routes[0].name === 'AuthFlow') {
        // We're in the AuthStack, so navigate differently
        navigation.navigate('AuthFlow', {
          screen: 'SellerCodeVerification'
        });
      } else {
        // We're in a different stack, use direct navigation
        navigation.navigate('SellerCodeVerification');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to reset navigation
      navigation.reset({
        index: 0,
        routes: [{ name: 'SellerCodeVerification' }],
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.iconContainer}>
          <Ionicons name="time-outline" size={100} color="#FF6B6B" />
        </View>

        <Text style={styles.title}>Verification Pending</Text>

        <Text style={styles.message}>
          Your account is under verification. This process may take 1-2 days.
          Click the "Send Verification Code" button below to send your details to the admin.
          You will then receive a verification code via email from the admin.
        </Text>

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>What happens next?</Text>

          <View style={styles.infoItem}>
            <Ionicons name="send-outline" size={24} color="#4CAF50" style={styles.infoIcon} />
            <Text style={styles.infoText}>Click "Send Verification Code" to send your details to admin</Text>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="checkmark-circle-outline" size={24} color="#4CAF50" style={styles.infoIcon} />
            <Text style={styles.infoText}>Our team will review your seller details</Text>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="mail-outline" size={24} color="#4CAF50" style={styles.infoIcon} />
            <Text style={styles.infoText}>The admin will send you a verification code via email</Text>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="key-outline" size={24} color="#4CAF50" style={styles.infoIcon} />
            <Text style={styles.infoText}>Enter the verification code to complete verification</Text>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="storefront-outline" size={24} color="#4CAF50" style={styles.infoIcon} />
            <Text style={styles.infoText}>Start selling on SwipeSense!</Text>
          </View>
        </View>

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Sending verification code to admin...</Text>
          </View>
        )}

        {error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={24} color="#FF6B6B" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : null}

        {codeSent && !error && (
          <View style={styles.successContainer}>
            <Ionicons name="checkmark-circle-outline" size={24} color="#4CAF50" />
            <Text style={styles.successText}>Verification code sent to admin successfully!</Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.verifyButton}
          onPress={handleEnterVerificationCode}
        >
          <Ionicons name="key-outline" size={20} color="#fff" style={{ marginRight: 8 }} />
          <Text style={styles.verifyButtonText}>Enter Verification Code</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.resendButton, loading && styles.disabledButton]}
          disabled={loading}
          onPress={async () => {
            if (loading) return;

            try {
              setLoading(true);
              setError('');
              setCodeSent(false);

              // Call the Firebase function to send the verification code to admin
              const sendVerificationCodeFn = httpsCallable(functions, 'sendVerificationCodeToAdmin');
              const result = await sendVerificationCodeFn({ email: user.email });

              if (result.data && result.data.success) {
                setCodeSent(true);
                Alert.alert(
                  'Success',
                  'Verification code has been sent to the admin.',
                  [{ text: 'OK' }]
                );
              } else {
                setError('Failed to send verification code. Please try again later.');
              }
            } catch (error) {
              console.error('Error resending verification code:', error);
              setError('Failed to send verification code. Please try again later.');
            } finally {
              setLoading(false);
            }
          }}
        >
          <Ionicons name="refresh-outline" size={20} color="#FF6B6B" />
          <Text style={styles.resendButtonText}>
            {loading ? 'Sending...' : codeSent ? 'Resend Verification Code' : 'Send Verification Code to Admin'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.supportButton} onPress={handleContactSupport}>
          <Ionicons name="help-circle-outline" size={20} color="#FF6B6B" />
          <Text style={styles.supportButtonText}>Contact Support</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <Text style={styles.signOutButtonText}>Sign Out</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginTop: 40,
    marginBottom: 20,
    padding: 20,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  infoContainer: {
    width: '100%',
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  infoIcon: {
    marginRight: 10,
  },
  infoText: {
    fontSize: 16,
    color: '#555',
    flex: 1,
  },
  verifyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  verifyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 15,
    borderWidth: 1,
    borderColor: '#FF6B6B',
    borderRadius: 8,
    backgroundColor: 'white',
    width: '100%',
  },
  supportButtonText: {
    color: '#FF6B6B',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  signOutButton: {
    width: '100%',
    padding: 15,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    alignItems: 'center',
  },
  signOutButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    width: '100%',
  },
  loadingText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    width: '100%',
  },
  errorText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#FF6B6B',
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e9',
    borderRadius: 8,
    width: '100%',
  },
  successText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#4CAF50',
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 15,
    borderWidth: 1,
    borderColor: '#FF6B6B',
    borderRadius: 8,
    backgroundColor: 'white',
    width: '100%',
  },
  resendButtonText: {
    color: '#FF6B6B',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default VerificationPendingScreen;
