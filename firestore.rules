rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if the user is accessing their own data
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Helper function to check if user is admin
    function isAdmin() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Global collections that anyone can read
    match /clothingItems/{itemId} {
      allow read: if true;

      // Allow creating new items
      allow create: if isAuthenticated();

      // Allow updating by owner or for likes/saves
      allow update: if isAuthenticated() && (
        // Owner can update everything
        resource.data.uploaderId == request.auth.uid ||

        // Anyone can update like/save related fields
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['likeCount', 'dislikeCount', 'likedBy', 'dislikedBy', 'saveCount', 'viewCount', 'swipeViewCount'])
      );

      // Allow deletion by owner or admin
      allow delete: if isAuthenticated() && (
        resource.data.uploaderId == request.auth.uid || isAdmin()
      );
    }

    // Items collection (alternative name for clothingItems)
    match /items/{itemId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        resource.data.uploaderId == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['likeCount', 'dislikeCount', 'likedBy', 'dislikedBy', 'saveCount', 'viewCount', 'swipeViewCount']) ||
        isAdmin() // Admins can update for moderation
      );
      allow delete: if isAuthenticated() && (
        resource.data.uploaderId == request.auth.uid || isAdmin()
      );
    }

    // Categories can be read by anyone
    match /categories/{categoryId} {
      allow read: if true;
      // Only admins can write to categories (handled by backend)
      allow write: if false;
    }

    // Global custom categories
    match /customCategories/{categoryId} {
      allow read: if true;
      allow write: if isAuthenticated();
    }

    // User data
    match /users/{userId} {
      allow read: if true; // Allow reading user profiles
      allow write: if isOwner(userId) || isAdmin(); // Allow users to write their own data, admins can write any user data

      // User subcollections
      match /cart/{cartItemId} {
        allow read, write: if isOwner(userId);
      }

      match /likes/{likeId} {
        allow read, write: if isOwner(userId);
      }

      match /dislikes/{dislikeId} {
        allow read, write: if isOwner(userId);
      }

      match /wishlist/{wishlistItemId} {
        allow read, write: if isOwner(userId);
      }

      match /interactedItems/{interactedItemId} {
        allow read, write: if isOwner(userId);
      }

      match /customCategories/{categoryId} {
        allow read, write: if isOwner(userId);
      }

      match /collections/{collectionId} {
        allow read, write: if isOwner(userId);
      }

      match /savedCollections/{collectionId} {
        allow read, write: if isOwner(userId);
      }
    }

    // Comments on items
    match /clothingItems/{itemId}/comments/{commentId} {
      allow read: if true;
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        // Only allow the comment author to update/delete
        resource.data.userId == request.auth.uid
      );

      // Allow replies to comments
      match /replies/{replyId} {
        allow read: if true;
        allow create: if isAuthenticated();
        allow update, delete: if isAuthenticated() && (
          // Only allow the reply author to update/delete
          resource.data.userId == request.auth.uid
        );
      }
    }

    // Allow collection group queries for customCategories
    match /{path=**}/customCategories/{categoryId} {
      allow read: if true;
    }

    // Allow collection group queries for collections
    match /{path=**}/collections/{collectionId} {
      allow read: if isAuthenticated();
    }

    // Seller verification collection
    match /sellerVerifications/{verificationId} {
      allow read: if isAuthenticated() && (
        request.auth.uid == resource.data.userId || isAdmin()
      );
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.userId;
      allow update: if isAuthenticated() && (
        request.auth.uid == resource.data.userId || isAdmin()
      );
    }

    // Orders collection - allow users to create and read their own orders, admins can access all
    match /orders/{orderId} {
      // Allow users to create orders
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;

      // Allow users to read their own orders, admins can read all
      allow read: if isAuthenticated() && (
        // Buyers can read their own orders
        resource.data.userId == request.auth.uid ||
        // Sellers can read orders that contain their products
        // This is a simplified rule - in production, you might want to check if the order contains items uploaded by the seller
        true || // Temporarily allow all authenticated users to read orders
        isAdmin() // Admins can read all orders
      );

      // Allow users to update orders, admins can update all
      allow update: if isAuthenticated() && (
        // Buyers can update their own orders (for cancellation, etc.)
        resource.data.userId == request.auth.uid ||
        // Sellers can update orders that contain their products
        // This is a simplified rule - in production, you might want to check if the order contains items uploaded by the seller
        true || // Temporarily allow all authenticated users to update orders
        isAdmin() // Admins can update all orders
      );
    }

    // Support system rules
    // Support tickets - users can create and read their own tickets, admins can read all
    match /supportTickets/{ticketId} {
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
    }

    // Support chat sessions - users can create and access their own chats, admins can access all
    match /supportChats/{chatId} {
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow delete: if isAdmin(); // Only admins can delete chat sessions
    }

    // Chat messages collection - separate from supportChats
    match /chatMessages/{messageId} {
      allow create: if isAuthenticated() && (
        request.resource.data.senderId == request.auth.uid || // User can send their own messages
        request.resource.data.senderId == 'system' || // Allow system messages
        isAdmin() // Admins can send messages
      );
      allow read: if isAuthenticated() && (
        // User can read messages from their own chats
        exists(/databases/$(database)/documents/supportChats/$(resource.data.chatId)) &&
        get(/databases/$(database)/documents/supportChats/$(resource.data.chatId)).data.userId == request.auth.uid ||
        // Admins can read all messages
        isAdmin()
      );
      allow update: if isAuthenticated() && (
        resource.data.senderId == request.auth.uid || isAdmin()
      );
      allow delete: if isAdmin(); // Only admins can delete messages
    }

    // Live chat sessions (alternative collection name for compatibility)
    match /liveChatSessions/{sessionId} {
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow delete: if isAdmin();
    }

    // Support FAQ - readable by all authenticated users, writable by admins only
    match /supportFAQ/{faqId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Support configuration - readable by all authenticated users, writable by admins only
    match /supportConfig/{configId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Support templates - readable by admins, writable by admins only
    match /supportTemplates/{templateId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // Support statistics - readable by admins, writable by admins only
    match /supportStats/{statsId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // Support chat analytics and metrics (for admin dashboard)
    match /chatAnalytics/{analyticsId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // Support agent status and availability
    match /supportAgents/{agentId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || request.auth.uid == agentId;
    }

    // Support system notifications
    match /supportNotifications/{notificationId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow delete: if isAdmin();
    }

    // Admin settings - publicly readable for basic app config, writable by admins only
    match /adminSettings/{settingId} {
      // Allow public read access for basic app configuration (maintenance mode, etc.)
      // This prevents permission errors when checking app status
      allow read: if true;
      // Only admins can write/update settings
      allow write: if isAdmin();
    }

    // Seller transactions - sellers can read their own transactions, admins can access all
    // Buyers can create transactions when completing payments
    match /sellerTransactions/{transactionId} {
      allow create: if isAuthenticated(); // Allow any authenticated user to create transactions (for payment completion)
      allow read: if isAuthenticated() && (
        resource.data.sellerId == request.auth.uid || 
        resource.data.buyerId == request.auth.uid || 
        isAdmin()
      );
      allow update: if isAuthenticated() && (
        resource.data.sellerId == request.auth.uid || isAdmin()
      );
      allow delete: if isAdmin(); // Only admins can delete transactions
    }

    // Transaction audit trail - for tracking transaction status changes
    match /transactionAudit/{auditId} {
      allow create: if isAuthenticated(); // Allow creation for audit trail
      allow read: if isAuthenticated(); // Allow reading audit trail
      allow update: if false; // Audit entries should not be updated
      allow delete: if isAdmin(); // Only admins can delete audit entries
    }

    // Payment transactions - admins only for security
    match /paymentTransactions/{transactionId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // Seller verification records - used by admin transaction management
    match /sellerVerifications/{verificationId} {
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || isAdmin()
      );
      allow update: if isAdmin(); // Only admins can update verification status
      allow delete: if isAdmin(); // Only admins can delete verifications
    }

    // Fallback rule - deny access by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}