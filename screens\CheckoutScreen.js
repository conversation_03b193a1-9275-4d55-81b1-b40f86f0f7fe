import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
  SafeAreaView,
  Platform, // Added Platform
  StatusBar, // Added StatusBar
  Dimensions // Import Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { db, auth } from '../firebase.config';
import { collection, doc, deleteDoc, serverTimestamp, getDoc, updateDoc, addDoc } from 'firebase/firestore';
import { initializeRazorpayPayment } from '../utils/paymentUtils';
import { isRunningInExpoGo } from '../utils/platformUtils';
import { showEnvironmentDebugInfo } from '../utils/debugUtils';

// Helper function to detect if running in Expo Go - legacy version kept for reference
const _legacyIsRunningInExpoGo = () => {
  try {
    const Constants = require('expo-constants').default;

    // Production build detection - this is the most important check
    // In a real production build, NODE_ENV should be 'production' or __DEV__ should be false
    const isProduction = process.env.NODE_ENV === 'production' || !__DEV__;
    if (isProduction) {
      console.log('🏭 Production build detected - not Expo Go');
      return false; // Production builds are NEVER Expo Go
    }

    // Multiple indicators to check if we're in Expo Go:
    // 1. App ownership should be 'expo' in Expo Go
    const isExpoOwnership = Constants.appOwnership === 'expo';

    // 2. Execution environment should not be 'standalone' in Expo Go
    const isNotStandalone = Constants.executionEnvironment !== 'standalone';

    // 3. In a production build, __DEV__ should be false
    const isDevMode = typeof __DEV__ !== 'undefined' ? __DEV__ : false;

    // 4. Check if we're in a release channel (production builds usually have this)
    const hasReleaseChannel = Constants.manifest && Constants.manifest.releaseChannel;

    console.log('Environment check:', {
      isProduction,
      isExpoOwnership,
      isNotStandalone,
      isDevMode,
      hasReleaseChannel,
      executionEnv: Constants.executionEnvironment,
      appOwnership: Constants.appOwnership
    });

    // For a production build, at least one of these should indicate we're NOT in Expo Go
    if (Constants.appOwnership === 'standalone' ||
      (Constants.executionEnvironment === 'standalone') ||
      (hasReleaseChannel && !isDevMode)) {
      return false; // Definitely a production build
    }

    return isExpoOwnership; // Final decision based on appOwnership
  } catch (e) {
    console.log('Error checking Expo environment:', e);
    return false; // If we can't determine, assume it's a production build
  }
};

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Responsive constants for CheckoutScreen item rendering
const ITEM_IMAGE_SIZE_CHECKOUT = SCREEN_WIDTH * 0.22; // Adjusted for checkout screen
const ITEM_PADDING_CHECKOUT_IMG = SCREEN_WIDTH * 0.03; // Padding around image and details
const TITLE_FONT_SIZE_CHECKOUT_ITEM = SCREEN_WIDTH * 0.038;
const BRAND_FONT_SIZE_CHECKOUT_ITEM = SCREEN_WIDTH * 0.032;
const PRICE_QUANTITY_FONT_SIZE_CHECKOUT_ITEM = SCREEN_WIDTH * 0.035;

const CheckoutScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [sellerInfo, setSellerInfo] = useState({});

  const { cartItems, totalPrice, userAddress } = route.params || {};
  const currentUserId = auth.currentUser?.uid;
  const currentUserEmail = auth.currentUser?.email;
  const currentUserName = auth.currentUser?.displayName;

  // Calculate tax and total with tax
  const subtotal = totalPrice || 0;
  const taxRate = 0.05; // 5% tax
  const taxAmount = subtotal * taxRate;
  const totalWithTax = subtotal + taxAmount;

  useEffect(() => {
    if (!cartItems || cartItems.length === 0) {
      Alert.alert('Error', 'No items in cart', [
        { text: 'OK', onPress: () => navigation.navigate('Cart') }
      ]);
      return;
    }

    // Show warning when running in Expo Go
    if (isRunningInExpoGo()) {
      Alert.alert(
        'Expo Go Environment',
        'Razorpay payments cannot be processed in Expo Go. Payments will be simulated for testing purposes. For real payment processing, please use a development build.',
        [{ text: 'I Understand' }]
      );
    }

    // Fetch seller information for each item
    fetchSellerInfo();
  }, []);

  // Refresh user address when returning from AddressScreen
  useFocusEffect(
    React.useCallback(() => {
      if (currentUserId) {
        fetchUserAddress();
      }
      return () => { };
    }, [currentUserId])
  );

  // Function to fetch the latest user address
  const fetchUserAddress = async () => {
    try {
      const userDocRef = doc(db, 'users', currentUserId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.address) {
          // Update the route params with the new address
          navigation.setParams({
            ...route.params,
            userAddress: userData.address
          });
        }
      }
    } catch (error) {
      console.error('Error fetching user address:', error);
    }
  };

  const fetchSellerInfo = async () => {
    setLoading(true);
    try {
      const sellerIds = new Set();
      const itemSellerMap = {};

      // Get unique seller IDs from cart items and map items to sellers
      for (const item of cartItems) {
        if (item.itemId) {
          const itemDoc = await getDoc(doc(db, 'clothingItems', item.itemId));
          if (itemDoc.exists()) {
            const itemData = itemDoc.data();
            if (itemData.uploaderId) {
              sellerIds.add(itemData.uploaderId);
              // Store the seller ID for each item
              itemSellerMap[item.id] = itemData.uploaderId;
            }
          }
        }
      }

      // Fetch seller information
      const sellerData = {};
      for (const sellerId of sellerIds) {
        const sellerDoc = await getDoc(doc(db, 'users', sellerId));
        if (sellerDoc.exists()) {
          sellerData[sellerId] = sellerDoc.data();
        }
      }

      // Group cart items by seller
      const itemsBySeller = {};
      for (const item of cartItems) {
        const sellerId = itemSellerMap[item.id];
        if (sellerId) {
          if (!itemsBySeller[sellerId]) {
            itemsBySeller[sellerId] = [];
          }
          itemsBySeller[sellerId].push(item);
        }
      }

      setSellerInfo({
        ...sellerData,
        itemsBySeller: itemsBySeller,
        itemSellerMap: itemSellerMap
      });
    } catch (error) {
      console.error('Error fetching seller info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!currentUserId) {
      navigation.navigate('Auth');
      return;
    }

    if (!userAddress) {
      Alert.alert('Address Required', 'Please add your shipping address before checkout.');
      return;
    }

    // Validate cart items
    if (!cartItems || cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Your cart is empty. Add items to proceed with checkout.');
      return;
    }    // Validate total price
    if (!totalWithTax || totalWithTax <= 0) {
      Alert.alert('Invalid Price', 'The total price appears to be invalid. Please try again.');
      return;
    }

    // Check if we have seller information
    if (!sellerInfo.itemsBySeller) {
      Alert.alert('Error', 'Failed to load seller information. Please try again.');
      return;
    }

    // Extra warning for Expo Go users
    if (isRunningInExpoGo()) {
      Alert.alert(
        'Expo Go Payment Simulation',
        'Since you are running the app in Expo Go, this will be a simulated payment. No actual payment will be processed.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Continue with Simulation', onPress: () => startPaymentProcess() }
        ]
      );
      return;
    }

    startPaymentProcess();
  };

  // Separate function to start the actual payment process
  const startPaymentProcess = async () => {
    setProcessingPayment(true); try {
      console.log('Starting payment process with total price (including tax):', totalWithTax);

      // Prepare order data for Razorpay
      const orderData = {
        amount: totalWithTax,
        currency: 'INR',
        items: cartItems,
        shippingAddress: userAddress,
        contact: userAddress.phone || '',
        name: currentUserName || 'Customer',
        // Add seller information for creating separate orders
        sellerInfo: sellerInfo
      };

      console.log('Prepared order data:', JSON.stringify({
        amount: orderData.amount,
        currency: orderData.currency,
        itemCount: orderData.items.length,
        contact: orderData.contact,
        name: orderData.name,
        sellerCount: Object.keys(sellerInfo.itemsBySeller || {}).length
      }));

      // Initialize Razorpay payment
      const orderId = await initializeRazorpayPayment(
        orderData,
        // Success callback
        async (paymentData) => {
          console.log('Payment successful:', JSON.stringify(paymentData));

          // Check if this was a simulated payment
          if (paymentData.isSimulated) {
            if (paymentData.isExpoGo) {
              Alert.alert(
                'Expo Go - Simulated Payment',
                '⚠️ Razorpay cannot process real payments in Expo Go. This was a simulated payment for testing purposes. To process real payments, create a development build using EAS Build or Expo Development Build.',
                [{ text: 'OK' }]
              );
            } else {
              Alert.alert(
                'Simulated Payment',
                '⚠️ This was a simulated payment for testing purposes. In a production build with properly configured Razorpay integration, this would be a real payment.',
                [{ text: 'OK' }]
              );
            }
          }

          try {
            // Create separate orders for each seller
            const orderIds = await createSellerOrders(paymentData);

            // Clear cart
            await clearCart();

            // Navigate to order confirmation screen with all order IDs
            navigation.navigate('OrderConfirmation', {
              orderIds: orderIds,
              paymentId: paymentData.paymentId,
              isSimulated: paymentData.isSimulated || false
            });
          } catch (error) {
            console.error('Error after successful payment:', error);
            Alert.alert(
              'Order Processing Issue',
              'Your payment was successful, but we encountered an issue processing your order. Please contact support.',
              [
                {
                  text: 'OK',
                  onPress: () => navigation.navigate('Cart')
                }
              ]
            );
          } finally {
            setProcessingPayment(false);
          }
        },
        // Error callback
        (error) => {
          console.error('Payment failed:', error);

          // Parse nested error objects if available
          let errorMessage = error.message || 'Unknown error';
          let isCancelled = false;
          let suggestionText = 'Please try again.';

          // Check for specific Razorpay errors
          if (error.error) {
            // Handle specific error cases
            if (error.error.code === 'BAD_REQUEST_ERROR' && error.error.reason === 'payment_cancelled') {
              isCancelled = true;
              errorMessage = 'Payment was cancelled or UPI app did not respond in time.';
              suggestionText = 'Please ensure your UPI app is running properly and try again.';
            }
            else if (error.error.description) {
              errorMessage = error.error.description;
            }
          }

          // Different alert based on cancellation vs. other errors
          if (isCancelled) {
            Alert.alert(
              'Payment Cancelled',
              `${errorMessage} ${suggestionText}`,
              [
                {
                  text: 'Try Again',
                  onPress: () => handlePayment()
                },
                {
                  text: 'Cancel',
                  style: 'cancel'
                }
              ]
            );
          } else {
            Alert.alert(
              'Payment Failed',
              `There was an error processing your payment.${suggestionText}`
            );
          }

          setProcessingPayment(false);
        }
      );

      if (!orderId) {
        throw new Error('Failed to initialize payment: No order ID returned');
      }

      // Note: Don't set processingPayment to false here as the payment is still in progress
      // It will be set to false in the callbacks

    } catch (error) {
      console.error('Error in payment process:', error);
      Alert.alert(
        'Payment Error',
        `There was an error initializing the payment: ${error.message || 'Unknown error'}. Please try again later.`
      );
      setProcessingPayment(false);
    }
  };

  const createSellerOrders = async (paymentData) => {
    try {
      console.log('Creating seller orders with payment data:', JSON.stringify(paymentData));

      const { itemsBySeller } = sellerInfo;
      const orderIds = [];

      // Ensure we have a valid order ID
      if (!paymentData || !paymentData.orderId) {
        console.error('Invalid payment data, missing orderId:', paymentData);
        throw new Error('Invalid payment data: missing order ID');
      }

      const mainOrderId = paymentData.orderId;

      // Confirm stock depletion for all items in the order
      try {
        const { confirmStockSale } = await import('../utils/stockUtils');

        for (const item of cartItems) {
          if (item.itemId) {
            const result = await confirmStockSale(item.itemId, item.quantity || 1);
            if (!result.success) {
              console.warn(`Failed to confirm stock sale for item ${item.itemId}:`, result.message);
            }
          }
        }
      } catch (error) {
        console.error('Error confirming stock sales:', error);
        // Don't fail the order creation if stock confirmation fails
      }

      // Add the main order ID to the list (this was created during payment initialization)
      orderIds.push(mainOrderId);

      // Get the main order details
      const mainOrderRef = doc(db, 'orders', mainOrderId);
      const mainOrderDoc = await getDoc(mainOrderRef);

      if (!mainOrderDoc.exists()) {
        throw new Error('Main order not found');
      }

      // Update the main order with the first seller's items
      const sellerIds = Object.keys(itemsBySeller);
      if (sellerIds.length > 0) {
        const firstSellerId = sellerIds[0];
        const firstSellerItems = itemsBySeller[firstSellerId];

        // Calculate total for first seller
        const firstSellerTotal = firstSellerItems.reduce((total, item) => {
          return total + (item.price || 0) * (item.quantity || 1);
        }, 0);

        // Update the main order with first seller's details
        await updateDoc(mainOrderRef, {
          sellerId: firstSellerId,
          items: firstSellerItems,
          totalAmount: firstSellerTotal,
          orderStatus: 'processing',
          updatedAt: serverTimestamp(),
          paymentId: paymentData.paymentId,
          paymentStatus: 'completed',
          createdViaCheckout: true, // Flag to indicate this was created via checkout
          emailSent: false // Will be set to true after email is sent
        });

        // Send email for the first seller's order
        await sendSellerOrderEmail(mainOrderId, firstSellerId, firstSellerItems, firstSellerTotal);

        // Create separate orders for other sellers
        for (let i = 1; i < sellerIds.length; i++) {
          // Add a delay between processing different sellers to avoid email conflicts
          await new Promise(resolve => setTimeout(resolve, 2000));

          const sellerId = sellerIds[i];
          const sellerItems = itemsBySeller[sellerId];

          // Calculate total for this seller
          const sellerTotal = sellerItems.reduce((total, item) => {
            return total + (item.price || 0) * (item.quantity || 1);
          }, 0);

          // Create a new order for this seller
          const newOrderRef = await addDoc(collection(db, 'orders'), {
            userId: currentUserId,
            sellerId: sellerId,
            items: sellerItems,
            totalAmount: sellerTotal,
            shippingAddress: userAddress,
            paymentId: paymentData.paymentId,
            paymentStatus: 'completed',
            orderStatus: 'processing',
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            relatedOrderId: mainOrderId, // Reference to the main order
            sellerEmail: sellerInfo[sellerId]?.email, // Store seller email for reference
            sellerName: sellerInfo[sellerId]?.name || sellerInfo[sellerId]?.displayName, // Store seller name
            createdViaCheckout: true, // Flag to indicate this was created via checkout
            emailSent: false // Will be set to true after email is sent
          });

          // Add this order ID to the list
          orderIds.push(newOrderRef.id);

          console.log(`Created order ${newOrderRef.id} for seller ${sellerId}`);

          // Send email for this seller's order
          await sendSellerOrderEmail(newOrderRef.id, sellerId, sellerItems, sellerTotal);
        }
      }

      // Add a delay before sending the buyer email
      await new Promise(resolve => setTimeout(resolve, 3000));

      console.log(`Sending buyer confirmation email for orders: ${orderIds.join(', ')}`);

      // Collect seller names for the buyer email
      const sellerNames = [];
      for (const sellerId of Object.keys(sellerInfo)) {
        if (sellerId !== 'itemsBySeller' && sellerId !== 'itemSellerMap') {
          const sellerName = sellerInfo[sellerId]?.name || sellerInfo[sellerId]?.displayName;
          if (sellerName) {
            sellerNames.push(sellerName);
          }
        }
      }

      console.log(`Collected seller names for buyer email: ${sellerNames.join(', ')}`);

      // Note: Order confirmation emails will be sent automatically by Cloud Functions
      // when the payment status is updated to 'completed'

      return orderIds;
    } catch (error) {
      console.error('Error creating seller orders:', error);
      throw error;
    }
  };

  const sendSellerOrderEmail = async (orderId, sellerId, items, totalAmount) => {
    try {
      if (!sellerId || !sellerInfo[sellerId]?.email) {
        console.log(`No email found for seller ${sellerId}`);
        return;
      }

      const sellerEmail = sellerInfo[sellerId].email;
      const sellerName = sellerInfo[sellerId]?.displayName || sellerInfo[sellerId]?.name || 'Seller';

      // Double-check that we're only sending this seller's items
      // This is a safety check in case the items weren't properly filtered upstream
      const sellerItems = items.filter(item => {
        // Check if this item belongs to this seller using the itemSellerMap
        const itemSellerId = sellerInfo.itemSellerMap[item.id];
        return itemSellerId === sellerId;
      });

      console.log(`Sending order email to seller ${sellerName} (${sellerEmail}) with ${sellerItems.length} items for order ${orderId}`);

      // Add a small delay to ensure emails are processed separately
      await new Promise(resolve => setTimeout(resolve, 500));

      // Ensure we have a valid total amount
      const validTotalAmount = typeof totalAmount === 'number' ? totalAmount :
        sellerItems.reduce((total, item) => total + ((item.price || 0) * (item.quantity || 1)), 0);

      // Get the actual order document from Firestore to ensure we're using the correct ID
      // This is critical for multi-seller orders to ensure each seller gets their specific order ID
      const orderDoc = await getDoc(doc(db, 'orders', orderId));
      if (!orderDoc.exists()) {
        console.error(`Order document ${orderId} not found in Firestore`);
        return;
      }

      // Prepare order data for emails - only include this seller's items
      const orderData = {
        id: orderId, // This is the specific order ID for this seller
        orderId: orderId, // Explicitly set orderId to ensure it's used in the email template
        buyerEmail: currentUserEmail,
        buyerName: currentUserName || 'Customer', // Ensure buyer name is set
        items: sellerItems, // Use the filtered items to ensure only this seller's items are included
        amount: validTotalAmount,
        totalAmount: validTotalAmount, // Add this explicitly for the email template
        shippingAddress: userAddress,
        paymentMethod: 'Razorpay',
        paymentStatus: 'Completed',
        sellerEmail: sellerEmail,
        sellerName: sellerName,
        sellerId: sellerId, // Add seller ID to ensure uniqueness
        timestamp: Date.now(), // Add timestamp to ensure uniqueness
        // Add order date for the email template
        orderDate: new Date().toLocaleDateString(),
        // Add the document ID explicitly to ensure it's used in the email
        documentId: orderId
      };

      console.log(`Order created for seller ${sellerId} with order ID: ${orderId}`);

      // Note: Seller notification emails will be sent automatically by Cloud Functions
      // when the payment status is updated to 'completed'

    } catch (error) {
      console.error('Error sending seller order email:', error);
    }
  };

  const clearCart = async () => {
    try {
      // Delete all items from cart
      for (const item of cartItems) {
        await deleteDoc(doc(db, 'users', currentUserId, 'cart', item.id));
      }
    } catch (error) {
      console.error('Error clearing cart:', error);
    }
  };

  // Helper function to get an estimated delivery date (7 days from now)
  const getEstimatedDeliveryDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderCartItem = (item, index) => (
    <View key={item.id || index} style={styles.cartItem}>
      <View style={styles.itemImageContainer}>
        <Image
          source={{ uri: item.imageUrl }}
          style={styles.itemImage}
          resizeMode="cover"
        />
      </View>
      <View style={styles.itemDetails}>
        <Text style={styles.itemTitle} numberOfLines={1} ellipsizeMode="tail">{item.title || 'Untitled Item'}</Text>
        {item.brand && <Text style={styles.itemBrand} numberOfLines={1} ellipsizeMode="tail">{item.brand}</Text>}
        <View style={styles.priceQuantityRow}>
          <Text style={styles.itemPrice}>₹{(item.price || 0).toFixed(2)}</Text>
          <Text style={styles.itemQuantity}>Qty: {item.quantity || 1}</Text>
        </View>
      </View>
    </View>
  );

  const renderAddress = () => (
    <View style={styles.addressContainer}>
      <View style={styles.addressHeader}>
        <Text style={styles.addressTitle}>Shipping Address</Text>
        <TouchableOpacity
          onPress={() => navigation.navigate('AddressScreen')}
        >
          <Text style={styles.editText}>Edit</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.addressContent}>
        <Text style={styles.addressText}>{userAddress.street}</Text>
        <Text style={styles.addressText}>{userAddress.city}, {userAddress.state} {userAddress.zipCode}</Text>
        <Text style={styles.addressText}>{userAddress.country}</Text>
        <Text style={styles.addressText}>Phone: {userAddress.phone}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            disabled={processingPayment}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Checkout</Text>
          <View style={styles.rightPlaceholder} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Preparing checkout...</Text>
          </View>
        ) : (
          <>
            <ScrollView style={styles.content}>
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Summary</Text>
                {cartItems.map(renderCartItem)}
              </View>

              {userAddress && renderAddress()}              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Payment Details</Text>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Subtotal</Text>
                  <Text style={styles.paymentValue}>₹{subtotal.toFixed(2)}</Text>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Shipping</Text>
                  <Text style={styles.freeShippingText}>Free</Text>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Tax (5%)</Text>
                  <Text style={styles.paymentValue}>₹{taxAmount.toFixed(2)}</Text>
                </View>
                <View style={[styles.paymentRow, styles.totalRow]}>
                  <Text style={styles.totalLabel}>Total</Text>
                  <Text style={styles.totalValue}>₹{totalWithTax.toFixed(2)}</Text>
                </View>
              </View>
            </ScrollView>

            <View style={styles.footer}>
              <TouchableOpacity
                style={styles.paymentButton}
                onPress={handlePayment}
                disabled={processingPayment}
              >
                {processingPayment ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.paymentButtonText}>Pay Now</Text>
                )}
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 // Added for Android
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
    backgroundColor: '#fff',
    // Removed elevation for Android, handled by safeArea paddingTop
    // elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  rightPlaceholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  cartItem: {
    flexDirection: 'row',
    alignItems: 'center', // Vertically center align items in the row
    paddingVertical: ITEM_PADDING_CHECKOUT_IMG * 0.75,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  itemImageContainer: {
    width: ITEM_IMAGE_SIZE_CHECKOUT,
    height: ITEM_IMAGE_SIZE_CHECKOUT,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: ITEM_PADDING_CHECKOUT_IMG,
    backgroundColor: '#f0f0f0',
  },
  itemImage: {
    width: '100%',
    height: '100%',
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'center', // Center details vertically if needed
    paddingVertical: ITEM_PADDING_CHECKOUT_IMG / 2, // Add some vertical padding if content is short
  },
  itemTitle: {
    fontSize: TITLE_FONT_SIZE_CHECKOUT_ITEM,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  itemBrand: {
    fontSize: BRAND_FONT_SIZE_CHECKOUT_ITEM,
    color: '#777',
    marginBottom: 4,
  },
  priceQuantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  itemPrice: {
    fontSize: PRICE_QUANTITY_FONT_SIZE_CHECKOUT_ITEM,
    fontWeight: '500',
    color: '#4CAF50',
  },
  itemQuantity: {
    fontSize: PRICE_QUANTITY_FONT_SIZE_CHECKOUT_ITEM,
    color: '#555',
  },
  addressContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    marginBottom: 16,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
    paddingBottom: 12,
  },
  addressTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  editText: {
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(255,107,107,0.1)',
  },
  addressContent: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  addressText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 6,
    lineHeight: 20,
    fontWeight: '500',
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
  },
  paymentLabel: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  }, paymentValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '600',
  },
  freeShippingText: {
    fontSize: 15,
    color: '#4CAF50',
    fontWeight: '600',
  },
  totalRow: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  totalValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#4CAF50',
    letterSpacing: 0.3,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
  },
  paymentButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  paymentButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  expoGoWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ff9500',
    paddingVertical: 8,
    width: '100%',
  },
  expoGoWarningText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 5,
    fontSize: 12,
  },
  debugButton: {
    backgroundColor: '#444',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginTop: 10,
    alignItems: 'center',
  },
  debugButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default CheckoutScreen;
