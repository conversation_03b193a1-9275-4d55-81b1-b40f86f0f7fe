import React from 'react';
import { View, Text, ActivityIndicator, Animated, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

const PreloadingScreen = ({ currentQuote, progress }) => {
  const fadeAnim = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    // Fade animation for quote changes
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0.7,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [currentQuote, fadeAnim]);

  return (
    <View style={styles.container}>
      {/* Background gradient effect */}
      <View style={styles.backgroundGradient} />

      {/* Main content */}
      <View style={styles.content}>
        {/* Logo or app name area */}
        <View style={styles.logoContainer}>
          <Text style={styles.appName}>SwipeSense</Text>
          <Text style={styles.tagline}>Curating Your Perfect Style</Text>
        </View>

        {/* Loading indicator */}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B6B" style={styles.spinner} />

          {/* Progress bar */}
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBackground}>
              <View
                style={[
                  styles.progressBarFill,
                  { width: `${progress}%` }
                ]}
              />
            </View>
            <Text style={styles.progressText}>{Math.round(progress)}%</Text>
          </View>
        </View>

        {/* Quote */}
        <Animated.View
          style={[
            styles.quoteContainer,
            { opacity: fadeAnim }
          ]}
        >
          <Text style={styles.quote}>{currentQuote}</Text>
        </Animated.View>

        {/* Bottom decoration */}
        <View style={styles.bottomDecoration}>
          <View style={styles.dot} />
          <View style={[styles.dot, styles.dotActive]} />
          <View style={styles.dot} />
        </View>
      </View>
    </View>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FAFAFA',
    opacity: 0.8,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 80,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333333',
    letterSpacing: 1,
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: '#666666',
    letterSpacing: 0.5,
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  spinner: {
    marginBottom: 20,
  },
  progressBarContainer: {
    alignItems: 'center',
    width: width * 0.6,
  },
  progressBarBackground: {
    width: '100%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#FF6B6B',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#999999',
    fontWeight: '500',
  },
  quoteContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
    paddingHorizontal: 20,
  },
  quote: {
    fontSize: 18,
    color: '#555555',
    textAlign: 'center',
    lineHeight: 26,
    fontStyle: 'italic',
    letterSpacing: 0.3,
  },
  bottomDecoration: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 60,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#DDDDDD',
    marginHorizontal: 4,
  },
  dotActive: {
    backgroundColor: '#FF6B6B',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
};

export default PreloadingScreen;
