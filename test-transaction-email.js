/**
 * Test script to verify transaction email functionality
 */

import { httpsCallable } from 'firebase/functions';
import { functions } from './firebase.config.js';

const testTransactionEmail = async () => {
    try {
        console.log('Testing transaction email functionality...');

        // Test data
        const testTransactionData = {
            transactionNumber: 'TXN123456789TEST',
            sellerId: 'test-seller-id',
            sellerName: 'Test Seller',
            sellerEmail: '<EMAIL>', // Change this to your test email
            buyerId: 'test-buyer-id',
            orderId: 'test-order-id-123',
            amount: 3000.00,
            itemsCount: 1,
            status: 'completed',
            transactionDetails: {
                method: 'Bank Transfer',
                referenceNumber: 'REF123456789',
                bankName: 'Test Bank'
            },
            createdAt: new Date(),
            updatedAt: new Date(),
            transferredAt: new Date()
        };

        // Test sending email to seller
        console.log('Sending test email to seller...');
        const sendTransactionStatusUpdateToSeller = httpsCallable(functions, 'sendTransactionStatusUpdateToSeller');

        const result = await sendTransactionStatusUpdateToSeller({
            transactionData: testTransactionData,
            newStatus: 'completed',
            adminNotes: 'This is a test transaction completion notification. Your funds have been transferred successfully.'
        });

        console.log('Email sent successfully:', result.data);
        console.log('✅ Transaction email test completed successfully!');

    } catch (error) {
        console.error('❌ Error testing transaction email:', error);
        console.error('Error details:', error.message);
    }
};

// Run the test
testTransactionEmail();
