import { auth, db } from '../firebase.config';
import { collection, query, where, getDocs, doc, updateDoc, limit } from 'firebase/firestore';

/**
 * Test function to check and set admin access
 * Call this from your app to test admin functionality
 */
export const testAndSetAdminAccess = async () => {
  try {
    if (!auth.currentUser) {
      console.log('❌ No user logged in');
      return false;
    }

    const currentUserEmail = auth.currentUser.email;
    const currentUserId = auth.currentUser.uid;

    console.log('🔍 Testing admin access for:', currentUserEmail);

    // Find user document
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('uid', '==', currentUserId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.log('❌ User document not found in Firestore');
      return false;
    }

    const userDoc = querySnapshot.docs[0];
    const userData = userDoc.data();

    console.log('📄 User document found:', {
      id: userDoc.id,
      email: userData.email,
      name: userData.name,
      isAdmin: userData.isAdmin
    });

    if (userData.isAdmin === true) {
      console.log('✅ User is already an admin!');
      return true;
    } else {
      console.log('⚠️ User is not an admin. Setting admin status...');

      // Set admin status
      await updateDoc(doc(db, 'users', userDoc.id), {
        isAdmin: true,
        adminGrantedAt: new Date(),
        adminGrantedBy: 'self-granted' // or currentUserId
      });

      console.log('✅ Admin status granted successfully!');
      return true;
    }

  } catch (error) {
    console.error('❌ Error testing/setting admin access:', error);
    return false;
  }
};

/**
 * Test function to check admin permissions on collections
 */
export const testAdminPermissions = async () => {
  try {
    console.log('🧪 Testing admin permissions on collections...');

    const collections = [
      'users',
      'items',
      'clothingItems',
      'orders',
      'sellerVerifications',
      'supportTickets'
    ];

    const results = {};

    for (const collectionName of collections) {
      try {
        const snapshot = await getDocs(query(collection(db, collectionName), limit(1)));
        results[collectionName] = {
          accessible: true,
          count: snapshot.size,
          error: null
        };
        console.log(`✅ ${collectionName}: Accessible (${snapshot.size} docs)`);
      } catch (error) {
        results[collectionName] = {
          accessible: false,
          count: 0,
          error: error.message
        };
        console.log(`❌ ${collectionName}: ${error.message}`);
      }
    }

    return results;

  } catch (error) {
    console.error('❌ Error testing admin permissions:', error);
    return {};
  }
};

/**
 * Quick function to make current user admin
 * Use this in your app console or as a button action
 */
export const makeCurrentUserAdmin = async () => {
  try {
    if (!auth.currentUser) {
      throw new Error('No user logged in');
    }

    const currentUserId = auth.currentUser.uid;
    const currentUserEmail = auth.currentUser.email;

    // Find user document
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('uid', '==', currentUserId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      throw new Error('User document not found');
    }

    const userDoc = querySnapshot.docs[0];

    // Update user to admin
    await updateDoc(doc(db, 'users', userDoc.id), {
      isAdmin: true,
      adminGrantedAt: new Date(),
      adminGrantedBy: currentUserId
    });

    console.log(`✅ Successfully made ${currentUserEmail} an admin!`);
    return true;

  } catch (error) {
    console.error('❌ Error making user admin:', error);
    throw error;
  }
};

// Usage examples:
//
// 1. Test and set admin access:
// import { testAndSetAdminAccess } from './utils/testAdminAccess';
// await testAndSetAdminAccess();
//
// 2. Test permissions:
// import { testAdminPermissions } from './utils/testAdminAccess';
// await testAdminPermissions();
//
// 3. Make current user admin:
// import { makeCurrentUserAdmin } from './utils/testAdminAccess';
// await makeCurrentUserAdmin();
