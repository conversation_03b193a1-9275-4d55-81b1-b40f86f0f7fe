
import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, ScrollView, Platform, SafeAreaView, KeyboardAvoidingView } from 'react-native';
import { db } from '../firebase.config'; // Import db
import { Ionicons } from '@expo/vector-icons';
import { doc, getDoc, updateDoc } from 'firebase/firestore'; // Import Firestore functions

// This screen is shown to new users (especially after Google Sign-In)
// to complete their profile information, including seller status and details.
const CompleteProfileScreen = ({ route, navigation }) => {
  const { userId } = route.params; // Get userId passed from LoginScreen

  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true); // For fetching initial data

  // Fetch existing basic data (like name from Google) when screen loads
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userId) {
          Alert.alert('Error', 'User ID is missing.');
          setInitialLoading(false);
          navigation.goBack(); // Go back if no user ID
          return;
      }
      try {
        const userDocRef = doc(db, 'users', userId);
        const docSnap = await getDoc(userDocRef);

        if (docSnap.exists()) {
          const userData = docSnap.data();
          setName(userData.name || ''); // Pre-fill name if available
          // You could pre-fill other fields if they were partially set
        } else {
          Alert.alert('Error', 'User profile not found. Please try logging in again.');
          navigation.navigate('Login'); // Redirect to login if profile doesn't exist
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        Alert.alert('Error', 'Could not load profile data.');
        navigation.navigate('Login');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchUserData();
  }, [userId, navigation]);

  const handleCompleteProfile = async () => {
    // Validation
    if (!name) {
      Alert.alert('Error', 'Please enter your name.');
      return;
    }

    setLoading(true);
    try {
      const userDocRef = doc(db, 'users', userId);

      // Prepare data to update
      const updateData = {
        name: name,
        name_lowercase: name.toLowerCase(),
        isSeller: false, // All users are created as buyers by default
        address: null,
        website: '',
        // You might add a flag indicating profile completion
        profileCompleted: true,
      };


      await updateDoc(userDocRef, updateData);
      console.log('User profile updated successfully!');

      // Navigation after completion is handled by the main App navigator's auth state listener
      // No explicit navigation here is needed if the listener correctly detects the logged-in state.
      // If immediate navigation is required: navigation.navigate('Home'); (or appropriate main screen)

    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert('Update Failed', 'Could not update your profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
      return (
          <View style={styles.container}>
              <ActivityIndicator size="large" color="#FF6B6B" />
              <Text>Loading profile...</Text>
          </View>
      );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.container}>
        <Text style={styles.title}>Complete Your Profile</Text>
        <Text style={styles.subtitle}>Welcome! Please provide a few more details.</Text>

        <TextInput
          style={styles.input}
          placeholder="Full Name"
          value={name}
          onChangeText={setName}
          autoCapitalize="words"
          editable={!loading}
        />

        {/* Account Type Indicator */}
        <View style={styles.accountTypeContainer}>
          <View style={styles.accountTypeBadge}>
            <Ionicons
              name="person-outline"
              size={18}
              color="#fff"
            />
            <Text style={styles.accountTypeText}>
              Buyer Account
            </Text>
          </View>
        </View>

        {loading ? (
          <ActivityIndicator size="large" color="#FF6B6B" style={{ marginTop: 20 }} />
        ) : (
          <TouchableOpacity
            style={styles.button}
            onPress={handleCompleteProfile}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Complete Profile</Text>
          </TouchableOpacity>
        )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// Reusing styles similar to SignUpScreen for consistency
const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingTop: Platform.OS === 'ios' ? 40 : 20, // Reduced padding for iOS devices
    paddingBottom: 20, // Reduced padding at the bottom
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
      fontSize: 16,
      color: '#666',
      marginBottom: 25,
      textAlign: 'center',
  },
  accountTypeContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  accountTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  accountTypeText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
    fontSize: 14,
  },
  input: {
    width: '100%',
    height: 50,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontSize: 16,
    backgroundColor: '#f8f8f8',
  },
  button: {
    width: '100%',
    height: 50,
    backgroundColor: '#FF6B6B',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 25,
    marginTop: 20, // Increased top margin
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#555',
    marginBottom: 15,
    alignSelf: 'flex-start',
    marginLeft: 5,
  },
});

export default CompleteProfileScreen;

