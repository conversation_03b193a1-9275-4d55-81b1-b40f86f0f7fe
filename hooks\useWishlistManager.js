import { useState, useCallback, useEffect } from 'react';
import { collection, query, where, getDocs, addDoc, deleteDoc, doc } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';
import { db, auth } from '../firebase.config';

export const useWishlistManager = (currentItemId) => {
  const [isCurrentItemWishlisted, setIsCurrentItemWishlisted] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);

  // Listen to authentication state changes
  useEffect(() => {
    const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      if (user) {
        setCurrentUserId(user.uid);
      } else {
        setCurrentUserId(null);
        setIsCurrentItemWishlisted(false); // Clear wishlist status when user logs out
      }
    });

    return () => unsubscribeAuth();
  }, []);

  // Check if item is in wishlist
  const checkWishlistStatus = useCallback(async (itemIdToCheck) => {
    if (!currentUserId || !itemIdToCheck) {
      setIsCurrentItemWishlisted(false);
      return;
    }

    // Double-check authentication
    if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
      console.log('[WishlistManager] User no longer authenticated, skipping wishlist check');
      setIsCurrentItemWishlisted(false);
      return false;
    }

    try {
      const wishlistQuery = query(
        collection(db, 'users', currentUserId, 'wishlist'),
        where('itemId', '==', itemIdToCheck)
      );
      const querySnapshot = await getDocs(wishlistQuery);
      const isWishlisted = !querySnapshot.empty;
      setIsCurrentItemWishlisted(isWishlisted);
      return isWishlisted;
    } catch (error) {
      console.error('[WishlistManager] Error checking wishlist status:', error);
      if (error.code === 'permission-denied') {
        console.log('[WishlistManager] Permission denied, clearing wishlist status');
        setIsCurrentItemWishlisted(false);
      }
      return false;
    }
  }, [currentUserId]);

  // Toggle wishlist status
  const toggleWishlist = useCallback(async (item) => {
    if (!currentUserId) {
      console.log("[WishlistManager] User not logged in, cannot wishlist.");
      return;
    }

    // Double-check authentication
    if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
      console.log('[WishlistManager] User no longer authenticated, cannot toggle wishlist');
      return;
    }

    const itemToUse = item || { id: currentItemId };
    if (!itemToUse || !itemToUse.id) {
      console.log("[WishlistManager] No item to wishlist");
      return;
    }

    try {
      const wishlistCollectionRef = collection(db, 'users', currentUserId, 'wishlist');
      const wishlistQuery = query(wishlistCollectionRef, where('itemId', '==', itemToUse.id));
      const querySnapshot = await getDocs(wishlistQuery);

      if (querySnapshot.empty) {
        // Add to wishlist
        await addDoc(wishlistCollectionRef, {
          itemId: itemToUse.id,
          title: itemToUse.title || 'Untitled',
          brand: itemToUse.brand || '',
          price: itemToUse.price || 0,
          imageUrl: itemToUse.imageUrl || '',
          category: itemToUse.category || '',
          addedAt: new Date()
        });
        setIsCurrentItemWishlisted(true);
        console.log(`[WishlistManager] Item ${itemToUse.id} added to wishlist`);
      } else {
        // Remove from wishlist
        const docToDelete = querySnapshot.docs[0];
        await deleteDoc(doc(db, 'users', currentUserId, 'wishlist', docToDelete.id));
        setIsCurrentItemWishlisted(false);
        console.log(`[WishlistManager] Item ${itemToUse.id} removed from wishlist`);
      }
    } catch (error) {
      console.error('[WishlistManager] Error toggling wishlist:', error);
      if (error.code === 'permission-denied') {
        console.log('[WishlistManager] Permission denied, cannot toggle wishlist');
      }
    }
  }, [currentUserId, currentItemId]);

  // Add to wishlist
  const addToWishlist = useCallback(async (item) => {
    if (!currentUserId || !item) {
      console.log("[WishlistManager] Cannot add to wishlist: missing user or item");
      return;
    }

    // Double-check authentication
    if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
      console.log('[WishlistManager] User no longer authenticated, cannot add to wishlist');
      return;
    }

    try {
      const wishlistCollectionRef = collection(db, 'users', currentUserId, 'wishlist');

      // Check if already in wishlist
      const wishlistQuery = query(wishlistCollectionRef, where('itemId', '==', item.id));
      const querySnapshot = await getDocs(wishlistQuery);

      if (querySnapshot.empty) {
        await addDoc(wishlistCollectionRef, {
          itemId: item.id,
          title: item.title || 'Untitled',
          brand: item.brand || '',
          price: item.price || 0,
          imageUrl: item.imageUrl || '',
          category: item.category || '',
          addedAt: new Date()
        });
        setIsCurrentItemWishlisted(true);
        console.log(`[WishlistManager] Item ${item.id} added to wishlist`);
      }
    } catch (error) {
      console.error('[WishlistManager] Error adding to wishlist:', error);
      if (error.code === 'permission-denied') {
        console.log('[WishlistManager] Permission denied, cannot add to wishlist');
      }
    }
  }, [currentUserId]);

  // Remove from wishlist
  const removeFromWishlist = useCallback(async (itemId) => {
    if (!currentUserId || !itemId) {
      console.log("[WishlistManager] Cannot remove from wishlist: missing user or item");
      return;
    }

    // Double-check authentication
    if (!auth.currentUser || auth.currentUser.uid !== currentUserId) {
      console.log('[WishlistManager] User no longer authenticated, cannot remove from wishlist');
      return;
    }

    try {
      const wishlistCollectionRef = collection(db, 'users', currentUserId, 'wishlist');
      const wishlistQuery = query(wishlistCollectionRef, where('itemId', '==', itemId));
      const querySnapshot = await getDocs(wishlistQuery);

      if (!querySnapshot.empty) {
        const docToDelete = querySnapshot.docs[0];
        await deleteDoc(doc(db, 'users', currentUserId, 'wishlist', docToDelete.id));
        setIsCurrentItemWishlisted(false);
        console.log(`[WishlistManager] Item ${itemId} removed from wishlist`);
      }
    } catch (error) {
      console.error('[WishlistManager] Error removing from wishlist:', error);
      if (error.code === 'permission-denied') {
        console.log('[WishlistManager] Permission denied, cannot remove from wishlist');
      }
    }
  }, [currentUserId]);

  return {
    isCurrentItemWishlisted,
    setIsCurrentItemWishlisted,
    checkWishlistStatus,
    toggleWishlist,
    addToWishlist,
    removeFromWishlist
  };
};
