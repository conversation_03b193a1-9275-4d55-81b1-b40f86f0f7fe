import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  Modal,
  TextInput,
  Platform,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
  collection,
  query,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  getDocs,
  where,
  limit
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import {
  getTicketsByStatus,
  updateTicketStatus,
  addTicketResponse,
  getSupportStatistics
} from '../utils/supportUtils';
import { checkAdminStatus as verifyAdminStatus } from '../utils/adminUtils';
import StatusDropdown from '../components/StatusDropdown';

const AdminSupportDashboard = ({ navigation }) => {
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [responseModalVisible, setResponseModalVisible] = useState(false);
  const [responseText, setResponseText] = useState('');
  const [stats, setStats] = useState({});
  const [isAdmin, setIsAdmin] = useState(false);

  const ticketFilters = [
    { id: 'all', title: 'All Tickets', count: 0 },
    { id: 'open', title: 'Open', count: 0 },
    { id: 'in_progress', title: 'In Progress', count: 0 },
    { id: 'resolved', title: 'Resolved', count: 0 },
    { id: 'closed', title: 'Closed', count: 0 }
  ];

  // Dropdown options for status filter
  const statusDropdownOptions = [
    { value: 'all', label: 'All Tickets', icon: 'list-outline', color: '#666' },
    { value: 'open', label: 'Open', icon: 'alert-circle-outline', color: '#FF6B6B' },
    { value: 'in_progress', label: 'In Progress', icon: 'time-outline', color: '#FFA500' },
    { value: 'resolved', label: 'Resolved', icon: 'checkmark-circle-outline', color: '#4CAF50' },
    { value: 'closed', label: 'Closed', icon: 'close-circle-outline', color: '#9E9E9E' }
  ];

  useEffect(() => {
    checkAdminStatus();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      fetchTickets();
      fetchStatistics();
    }
  }, [isAdmin, selectedFilter]);

  const checkAdminStatus = async () => {
    try {
      if (auth.currentUser) {
        const adminStatus = await verifyAdminStatus(auth.currentUser.uid);
        setIsAdmin(adminStatus);
      } else {
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    }
  };

  const fetchTickets = async () => {
    setLoading(true);
    try {
      let ticketsData;
      if (selectedFilter === 'all') {
        const q = query(collection(db, 'supportTickets'), orderBy('createdAt', 'desc'));
        const snapshot = await getDocs(q);
        ticketsData = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      } else {
        ticketsData = await getTicketsByStatus(selectedFilter);
      }
      setTickets(ticketsData);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      Alert.alert('Error', 'Failed to fetch support tickets');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const statistics = await getSupportStatistics();
      setStats(statistics);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleStatusUpdate = async (ticketId, newStatus) => {
    try {
      await updateTicketStatus(ticketId, newStatus, auth.currentUser.uid);
      Alert.alert('Success', 'Ticket status updated successfully');
      fetchTickets(); // Refresh tickets
    } catch (error) {
      console.error('Error updating ticket status:', error);
      Alert.alert('Error', 'Failed to update ticket status');
    }
  };

  const handleSendResponse = async () => {
    if (!responseText.trim() || !selectedTicket) return;

    try {
      await addTicketResponse(selectedTicket.id, {
        message: responseText,
        responderId: auth.currentUser.uid,
        responderName: auth.currentUser.displayName || 'Support Agent',
        isAdminResponse: true
      });

      setResponseText('');
      setResponseModalVisible(false);
      setSelectedTicket(null);

      Alert.alert('Success', 'Response sent successfully');
      fetchTickets(); // Refresh tickets
    } catch (error) {
      console.error('Error sending response:', error);
      Alert.alert('Error', 'Failed to send response');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return '#FF6B6B';
      case 'in_progress': return '#FFA500';
      case 'resolved': return '#4CAF50';
      case 'closed': return '#9E9E9E';
      default: return '#666';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#FF4444';
      case 'medium': return '#FFA500';
      case 'low': return '#4CAF50';
      default: return '#666';
    }
  };

  const renderTicketItem = ({ item }) => (
    <TouchableOpacity
      style={styles.ticketCard}
      onPress={() => navigation.navigate('TicketDetails', { ticketId: item.id })}
    >
      <View style={styles.ticketHeader}>
        <Text style={styles.ticketId}>#{item.id.substring(0, 8)}</Text>
        <View style={styles.ticketBadges}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{item.status.replace('_', ' ').toUpperCase()}</Text>
          </View>
          <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
            <Text style={styles.priorityText}>{item.priority?.toUpperCase() || 'MEDIUM'}</Text>
          </View>
        </View>
      </View>

      <Text style={styles.ticketSubject}>{item.subject}</Text>
      <Text style={styles.ticketCategory}>{item.category.replace('_', ' ').toUpperCase()}</Text>
      <Text style={styles.ticketMessage} numberOfLines={2}>{item.message}</Text>

      <View style={styles.ticketFooter}>
        <Text style={styles.ticketDate}>
          {item.createdAt?.toDate?.()?.toLocaleDateString() || 'Unknown date'}
        </Text>
        <View style={styles.ticketActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              setSelectedTicket(item);
              setResponseModalVisible(true);
            }}
          >
            <Ionicons name="chatbubble-outline" size={16} color="#666" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => showStatusMenu(item)}
          >
            <Ionicons name="settings-outline" size={16} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const showStatusMenu = (ticket) => {
    const statusOptions = [
      { text: 'Mark as In Progress', status: 'in_progress' },
      { text: 'Mark as Resolved', status: 'resolved' },
      { text: 'Mark as Closed', status: 'closed' },
      { text: 'Reopen Ticket', status: 'open' }
    ];

    Alert.alert(
      'Update Ticket Status',
      `Current status: ${ticket.status.replace('_', ' ')}`,
      [
        ...statusOptions.map(option => ({
          text: option.text,
          onPress: () => handleStatusUpdate(ticket.id, option.status)
        })),
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const renderStatCard = (title, value, icon, color) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <Ionicons name={icon} size={24} color={color} />
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
      </View>
    </View>
  );

  if (!isAdmin) {
    return (
      <SafeAreaWrapper>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={80} color="#ccc" />
          <Text style={styles.accessDeniedText}>Access Denied</Text>
          <Text style={styles.accessDeniedSubtext}>You don't have permission to access this area.</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Support Dashboard</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={fetchTickets}
        >
          <Ionicons name="refresh" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </SafeAreaHeader>

      <View style={styles.container}>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity
            style={[styles.quickActionButton, { backgroundColor: '#4ECDC4' }]}
            onPress={() => navigation.navigate('AdminLiveChatScreen')}
          >
            <Ionicons name="chatbubbles" size={24} color="white" />
            <Text style={styles.quickActionText}>Live Chat</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionButton, { backgroundColor: '#FF6B6B' }]}
            onPress={() => setSelectedFilter('open')}
          >
            <Ionicons name="alert-circle" size={24} color="white" />
            <Text style={styles.quickActionText}>Open Tickets</Text>
          </TouchableOpacity>
        </View>

        {/* Statistics Cards */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statsContainer}>
          {renderStatCard('Total Tickets', stats.total || 0, 'receipt-outline', '#FF6B6B')}
          {renderStatCard('Open', stats.open || 0, 'alert-circle-outline', '#FF6B6B')}
          {renderStatCard('In Progress', stats.inProgress || 0, 'time-outline', '#FFA500')}
          {renderStatCard('Resolved', stats.resolved || 0, 'checkmark-circle-outline', '#4CAF50')}
        </ScrollView>

        {/* Status Filter Dropdown */}
        <View style={styles.filterContainer}>
          <StatusDropdown
            options={statusDropdownOptions}
            selectedValue={selectedFilter}
            onValueChange={setSelectedFilter}
            label="Filter Tickets by Status:"
            style={styles.statusDropdown}
          />
        </View>

        {/* Tickets List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Loading tickets...</Text>
          </View>
        ) : (
          <FlatList
            data={tickets}
            renderItem={renderTicketItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.ticketsList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="document-outline" size={60} color="#ccc" />
                <Text style={styles.emptyText}>No tickets found</Text>
              </View>
            }
          />
        )}

        {/* Response Modal */}
        <Modal
          visible={responseModalVisible}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <SafeAreaWrapper>
            <View style={styles.responseModal}>
              <View style={styles.responseHeader}>
                <TouchableOpacity onPress={() => setResponseModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.responseTitle}>Send Response</Text>
                <TouchableOpacity onPress={handleSendResponse} disabled={!responseText.trim()}>
                  <Text style={[styles.sendButton, !responseText.trim() && styles.sendButtonDisabled]}>
                    Send
                  </Text>
                </TouchableOpacity>
              </View>

              {selectedTicket && (
                <View style={styles.ticketPreview}>
                  <Text style={styles.previewSubject}>{selectedTicket.subject}</Text>
                  <Text style={styles.previewMessage}>{selectedTicket.message}</Text>
                </View>
              )}

              <TextInput
                style={styles.responseInput}
                placeholder="Type your response here..."
                multiline
                numberOfLines={6}
                value={responseText}
                onChangeText={setResponseText}
                textAlignVertical="top"
              />
            </View>
          </SafeAreaWrapper>
        </Modal>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  statsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginRight: 12,
    minWidth: 120,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  statContent: {
    marginLeft: 12,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statTitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  quickActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  statusDropdown: {
    marginBottom: 0,
  },
  ticketsList: {
    paddingHorizontal: 16,
  },
  ticketCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ticketId: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  ticketBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  ticketSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  ticketCategory: {
    fontSize: 12,
    color: '#FF6B6B',
    fontWeight: '500',
    marginBottom: 8,
  },
  ticketMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  ticketFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ticketDate: {
    fontSize: 12,
    color: '#999',
  },
  ticketActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  accessDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  accessDeniedText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  accessDeniedSubtext: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  responseModal: {
    flex: 1,
    backgroundColor: '#fff',
  },
  responseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  responseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sendButton: {
    color: '#FF6B6B',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sendButtonDisabled: {
    color: '#ccc',
  },
  ticketPreview: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    margin: 16,
    borderRadius: 12,
  },
  previewSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  previewMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  responseInput: {
    margin: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 12,
    fontSize: 16,
    minHeight: 150,
    backgroundColor: '#fff',
  },
});

export default AdminSupportDashboard;
