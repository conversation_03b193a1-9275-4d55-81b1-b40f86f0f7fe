import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  getBroadCategories,
  getDetailedCategories,
} from '../utils/categoryHierarchy';

// Color palette for filtering
const FILTER_COLORS = [
  { name: 'All Colors', value: null },
  // Basic Colors
  { name: 'Red', value: '#FF0000' },
  { name: 'Blue', value: '#0000FF' },
  { name: 'Green', value: '#008000' },
  { name: 'Yellow', value: '#FFFF00' },
  { name: 'Black', value: '#000000' },
  { name: 'White', value: '#FFFFFF' },
  { name: 'Pink', value: '#FFC0CB' },
  { name: 'Purple', value: '#800080' },
  { name: 'Orange', value: '#FFA500' },
  { name: '<PERSON>', value: '#A52A2A' },
  { name: '<PERSON>', value: '#808080' },
  { name: 'Navy', value: '#000080' },
  { name: 'Be<PERSON>', value: '#F5F5DC' },
  
  // Extended Colors
  { name: 'Coral', value: '#FF7F50' },
  { name: 'Turquoise', value: '#40E0D0' },
  { name: 'Lavender', value: '#E6E6FA' },
  { name: 'Mint', value: '#98FB98' },
  { name: 'Gold', value: '#FFD700' },
  { name: 'Silver', value: '#C0C0C0' },
  { name: 'Maroon', value: '#800000' },
  { name: 'Teal', value: '#008080' },
  { name: 'Olive', value: '#808000' },
  { name: 'Khaki', value: '#F0E68C' },
  { name: 'Cream', value: '#FFFDD0' },
  { name: 'Ivory', value: '#FFFFF0' },
  { name: 'Tan', value: '#D2B48C' },
  { name: 'Rose Gold', value: '#E8B4B8' },
  { name: 'Crimson', value: '#DC143C' },
  { name: 'Burgundy', value: '#800020' },
  { name: 'Hot Pink', value: '#FF69B4' },
  { name: 'Fuchsia', value: '#FF00FF' },
  { name: 'Violet', value: '#8A2BE2' },
  { name: 'Indigo', value: '#4B0082' },
  { name: 'Sky Blue', value: '#87CEEB' },
  { name: 'Royal Blue', value: '#4169E1' },
  { name: 'Forest Green', value: '#228B22' },
  { name: 'Lime', value: '#00FF00' },
  { name: 'Emerald', value: '#50C878' },
  { name: 'Sage', value: '#9CAF88' },
  { name: 'Mustard', value: '#FFDB58' },
  { name: 'Amber', value: '#FFBF00' },
  { name: 'Peach', value: '#FFCBA4' },
  { name: 'Salmon', value: '#FA8072' },
  { name: 'Chocolate', value: '#7B3F00' },
  { name: 'Coffee', value: '#6F4E37' },
  { name: 'Charcoal', value: '#36454F' },
  { name: 'Slate', value: '#708090' },
  { name: 'Neon Green', value: '#39FF14' },
  { name: 'Electric Blue', value: '#7DF9FF' },
  { name: 'Millennial Pink', value: '#F2D2BD' },
];

// Gender options for filtering
const GENDER_OPTIONS = [
  { name: 'All', value: null },
  { name: 'Male', value: 'male' },
  { name: 'Female', value: 'female' },
  { name: 'Unisex', value: 'unisex' },
];

const EnhancedCategoryFilter = ({
  activeCategoryType,
  activeSpecificCategories = [],
  activeColor,
  activeGender,
  onCategoryTypeSelect,
  onSpecificCategoriesChange,
  onColorSelect,
  onGenderSelect,
  isLoadingMoreCategories,
  onEndReached
}) => {
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showColorModal, setShowColorModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [modalType, setModalType] = useState(''); // 'categoryType', 'specificCategories', 'colors'

  const [broadCategories] = useState(['All', ...getBroadCategories()]);
  const [availableSpecificCategories, setAvailableSpecificCategories] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);

  // Update available specific categories when category type changes
  useEffect(() => {
    if (activeCategoryType && activeCategoryType !== 'All') {
      const specific = getDetailedCategories(activeCategoryType);
      setAvailableSpecificCategories(['All', ...specific]);
    } else {
      // Get all specific categories from all broad categories
      const allSpecific = new Set(['All']);
      getBroadCategories().forEach(broadCat => {
        getDetailedCategories(broadCat).forEach(specific => {
          allSpecific.add(specific);
        });
      });
      setAvailableSpecificCategories(Array.from(allSpecific));
    }
  }, [activeCategoryType]);

  // Filter items based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      if (modalType === 'categoryType') {
        setFilteredItems(broadCategories);
      } else if (modalType === 'specificCategories') {
        setFilteredItems(availableSpecificCategories);
      } else if (modalType === 'colors') {
        setFilteredItems(FILTER_COLORS);
      } else if (modalType === 'genders') {
        setFilteredItems(GENDER_OPTIONS);
      }
    } else {
      const query = searchQuery.toLowerCase();
      if (modalType === 'categoryType') {
        setFilteredItems(broadCategories.filter(cat =>
          cat.toLowerCase().includes(query)
        ));
      } else if (modalType === 'specificCategories') {
        setFilteredItems(availableSpecificCategories.filter(cat =>
          cat.toLowerCase().includes(query)
        ));
      } else if (modalType === 'colors') {
        setFilteredItems(FILTER_COLORS.filter(color =>
          color.name.toLowerCase().includes(query)
        ));
      } else if (modalType === 'genders') {
        setFilteredItems(GENDER_OPTIONS.filter(gender =>
          gender.name.toLowerCase().includes(query)
        ));
      }
    }
  }, [searchQuery, modalType, broadCategories, availableSpecificCategories]);

  const openModal = (type) => {
    setModalType(type);
    setSearchQuery('');
    setShowCategoryModal(true);
  };

  const closeModal = () => {
    setShowCategoryModal(false);
    setSearchQuery('');
    setModalType('');
  };

  const handleCategoryTypeSelect = (categoryType) => {
    onCategoryTypeSelect(categoryType);
    // Clear specific categories when category type changes
    if (categoryType !== activeCategoryType) {
      onSpecificCategoriesChange([]);
    }
    closeModal();
  };

  const handleSpecificCategoryToggle = (category) => {
    if (category === 'All') {
      onSpecificCategoriesChange([]);
    } else {
      const newSelected = activeSpecificCategories.includes(category)
        ? activeSpecificCategories.filter(cat => cat !== category)
        : [...activeSpecificCategories, category];
      onSpecificCategoriesChange(newSelected);
    }
  };
  const handleColorSelect = (color) => {
    // Ensure we only pass the value, not the entire object
    const colorValue = typeof color === 'object' ? color.value : color;
    onColorSelect(colorValue);
    closeModal();
  };

  const handleGenderSelect = (gender) => {
    // Ensure we only pass the value, not the entire object
    const genderValue = typeof gender === 'object' ? gender.value : gender;
    onGenderSelect(genderValue);
    closeModal();
  };
  const renderFilterButton = (title, value, onPress, isActive = false) => {
    // Ensure title is always a string
    const displayTitle = typeof title === 'object' ? JSON.stringify(title) : String(title || '');
    
    return (
      <TouchableOpacity
        style={[
          styles.filterButton,
          isActive && styles.activeFilterButton
        ]}
        onPress={onPress}
      >
        <Text style={[
          styles.filterButtonText,
          isActive && styles.activeFilterText
        ]}>
          {displayTitle}
        </Text>
        <Ionicons name="chevron-down" size={14} color={isActive ? "#fff" : "#666"} />
      </TouchableOpacity>
    );
  };

  const renderColorIndicator = (color) => {
    if (!color) return null;
    return (
      <View
        style={[
          styles.colorIndicator,
          { backgroundColor: color },
          color === '#FFFFFF' && styles.whiteColorBorder
        ]}
      />
    );
  };

  const renderModal = () => (
    <Modal
      visible={showCategoryModal}
      transparent={true}
      animationType="slide"
      onRequestClose={closeModal}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {modalType === 'categoryType' && 'Select Category Type'}
              {modalType === 'specificCategories' && 'Select Specific Categories'}
              {modalType === 'colors' && 'Select Color'}
              {modalType === 'genders' && 'Select Gender'}
            </Text>
            <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />            <TextInput
              style={styles.searchInput}
              placeholder={`Search ${modalType === 'colors' ? 'colors' : 'categories'}...`}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              placeholderTextColor="#000"
            />
          </View>          <FlatList
            data={filteredItems}
            keyExtractor={(item, index) => {
              if (modalType === 'colors') return item.name || `color_${index}`;
              if (modalType === 'genders') return item.value || `gender_${index}`;
              if (typeof item === 'object') return item.id || item.name || `item_${index}`;
              return typeof item === 'string' ? item : `item_${index}`;
            }}
            renderItem={({ item }) => {
              if (modalType === 'colors') {
                const isSelected = activeColor === item.value;
                return (
                  <TouchableOpacity
                    style={[styles.modalItem, isSelected && styles.selectedModalItem]}
                    onPress={() => handleColorSelect(item)}
                  >
                    <View style={styles.colorItemContent}>
                      {item.value && (
                        <View
                          style={[
                            styles.colorCircle,
                            { backgroundColor: item.value },
                            item.value === '#FFFFFF' && styles.whiteColorBorder
                          ]}
                        />
                      )}
                      <Text style={[styles.modalItemText, isSelected && styles.selectedModalItemText]}>
                        {item.name}
                      </Text>
                    </View>
                    {isSelected && <Ionicons name="checkmark" size={20} color="#FF6B6B" />}
                  </TouchableOpacity>
                );              } else if (modalType === 'categoryType') {
                const isSelected = activeCategoryType === item;
                return (
                  <TouchableOpacity
                    style={[styles.modalItem, isSelected && styles.selectedModalItem]}
                    onPress={() => handleCategoryTypeSelect(item)}
                  >
                    <Text style={[styles.modalItemText, isSelected && styles.selectedModalItemText]}>
                      {String(item)}
                    </Text>
                    {isSelected && <Ionicons name="checkmark" size={20} color="#FF6B6B" />}
                  </TouchableOpacity>
                );
              } else if (modalType === 'specificCategories') {
                const isSelected = item === 'All'
                  ? activeSpecificCategories.length === 0
                  : activeSpecificCategories.includes(item);
                return (                  <TouchableOpacity
                    style={[styles.modalItem, isSelected && styles.selectedModalItem]}
                    onPress={() => handleSpecificCategoryToggle(item)}
                  >
                    <Text style={[styles.modalItemText, isSelected && styles.selectedModalItemText]}>
                      {String(item)}
                    </Text>
                    <Ionicons
                      name={isSelected ? "checkbox" : "square-outline"}
                      size={20}
                      color={isSelected ? "#FF6B6B" : "#666"}
                    />
                  </TouchableOpacity>
                );
              } else if (modalType === 'genders') {
                const isSelected = activeGender === item.value;
                return (
                  <TouchableOpacity
                    style={[styles.modalItem, isSelected && styles.selectedModalItem]}
                    onPress={() => handleGenderSelect(item)}
                  >
                    <Text style={[styles.modalItemText, isSelected && styles.selectedModalItemText]}>
                      {item.name}
                    </Text>
                    {isSelected && <Ionicons name="checkmark" size={20} color="#FF6B6B" />}
                  </TouchableOpacity>
                );
              }
            }}
            style={styles.modalList}
          />
        </View>
      </View>
    </Modal>
  );
  const getSpecificCategoriesText = () => {
    // Debug logging
    console.log('EnhancedCategoryFilter - activeSpecificCategories:', activeSpecificCategories, typeof activeSpecificCategories);
    if (activeSpecificCategories.length === 0) return 'All Categories';
    if (activeSpecificCategories.length === 1) return String(activeSpecificCategories[0]);
    return `${activeSpecificCategories.length} Selected`;
  };const getColorText = () => {
    if (!activeColor) return 'All Colors';
    // Debug logging
    console.log('EnhancedCategoryFilter - activeColor:', activeColor, typeof activeColor);
    // Ensure activeColor is a string, not an object
    const colorValue = typeof activeColor === 'object' ? activeColor.value : activeColor;
    const colorObj = FILTER_COLORS.find(c => c.value === colorValue);
    return colorObj ? colorObj.name : 'Custom Color';
  };

  const getGenderText = () => {
    if (!activeGender) return 'All Genders';
    // Debug logging
    console.log('EnhancedCategoryFilter - activeGender:', activeGender, typeof activeGender);
    // Ensure activeGender is a string, not an object
    const genderValue = typeof activeGender === 'object' ? activeGender.value : activeGender;
    const genderObj = GENDER_OPTIONS.find(g => g.value === genderValue);
    return genderObj ? genderObj.name : 'Custom Gender';
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >        {/* Category Type Filter */}
        {renderFilterButton(
          String(activeCategoryType || 'All'),
          activeCategoryType,
          () => openModal('categoryType'),
          !!activeCategoryType && activeCategoryType !== 'All'
        )}

        {/* Specific Categories Filter */}
        {renderFilterButton(
          getSpecificCategoriesText(),
          activeSpecificCategories,
          () => openModal('specificCategories'),
          activeSpecificCategories.length > 0
        )}

        {/* Color Filter */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            styles.colorFilterButton,
            activeColor && styles.activeFilterButton
          ]}
          onPress={() => openModal('colors')}
        >
          {renderColorIndicator(activeColor)}
          <Text style={[
            styles.filterButtonText,
            activeColor && styles.activeFilterText
          ]}>
            {getColorText()}
          </Text>
          <Ionicons name="chevron-down" size={14} color={activeColor ? "#fff" : "#666"} />
        </TouchableOpacity>

        {/* Gender Filter */}
        {renderFilterButton(
          getGenderText(),
          activeGender,
          () => openModal('genders'),
          !!activeGender
        )}

        {isLoadingMoreCategories && (
          <View style={styles.loadingIndicator}>
            <ActivityIndicator size="small" color="#FF6B6B" />
          </View>
        )}
      </ScrollView>

      {renderModal()}
    </View>
  );
};

const styles = {
  container: {
    paddingVertical: 8,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  scrollContent: {
    paddingHorizontal: 5,
    alignItems: 'center',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#f8f8f8',
    marginHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 36,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  colorFilterButton: {
    paddingHorizontal: 8,
  },
  activeFilterButton: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#555',
    fontWeight: '500',
    marginHorizontal: 4,
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 6,
  },
  whiteColorBorder: {
    borderWidth: 1,
    borderColor: '#ccc',
  },
  loadingIndicator: {
    paddingHorizontal: 10,
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  modalList: {
    maxHeight: 400,
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedModalItem: {
    backgroundColor: '#fff5f5',
  },
  modalItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedModalItemText: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
  colorItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 10,
  },
};

export default EnhancedCategoryFilter;
