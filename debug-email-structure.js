/**
 * Simple debug script to test transaction email with proper data structure
 */

// Example of how to call the transaction email function correctly
const testData = {
    transactionData: {
        transactionNumber: 'TXN123456789TEST',
        sellerId: 'test-seller-id',
        sellerName: 'Test Seller',
        sellerEmail: '<EMAIL>',
        buyerId: 'test-buyer-id',
        orderId: 'test-order-123',
        amount: 3000,
        itemsCount: 1,
        status: 'completed',
        transactionDetails: {
            method: 'Bank Transfer',
            referenceNumber: 'REF123456789',
            bankName: 'Test Bank'
        }
    },
    newStatus: 'completed',
    adminNotes: 'Payment has been successfully transferred to your bank account.'
};

console.log('Transaction email data structure:');
console.log(JSON.stringify(testData, null, 2));

// Firebase function call should be:
// await sendTransactionStatusUpdateToSeller(testData);
