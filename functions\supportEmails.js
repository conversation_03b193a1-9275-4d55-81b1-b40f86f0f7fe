const functions = require('firebase-functions');
const admin = require('firebase-admin');
const SibApiV3Sdk = require('sib-api-v3-sdk'); // Brevo (formerly Sendinblue) SDK

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Initialize Brevo API client
const defaultClient = SibApiV3Sdk.ApiClient.instance;
const apiKey = defaultClient.authentications['api-key'];
const brevoApiKey = functions.config().brevo?.apikey || process.env.BREVO_API_KEY;
apiKey.apiKey = brevoApiKey;

/**
 * Cloud Function that sends email notifications when a new support ticket is created
 */
exports.sendSupportTicketNotification = functions.firestore
  .document('supportTickets/{ticketId}')
  .onCreate(async (snap, context) => {
    try {
      const ticketId = context.params.ticketId;
      const ticketData = snap.data();

      console.log(`New support ticket created: ${ticketId}`);

      // Create a Brevo email API instance
      const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

      // Send notification email to admin
      await sendAdminSupportNotification(apiInstance, ticketId, ticketData);

      // Send confirmation email to user
      await sendUserTicketConfirmation(apiInstance, ticketData);

      console.log(`Support ticket notifications sent for ticket: ${ticketId}`);
      return null;
    } catch (error) {
      console.error('Error sending support ticket notifications:', error);
      return null;
    }
  });

/**
 * Cloud Function that sends email notifications when a support ticket is updated
 */
exports.sendSupportTicketUpdate = functions.firestore
  .document('supportTickets/{ticketId}')
  .onUpdate(async (change, context) => {
    try {
      const ticketId = context.params.ticketId;
      const beforeData = change.before.data();
      const afterData = change.after.data();

      // Check if responses were added
      const beforeResponses = beforeData.responses || [];
      const afterResponses = afterData.responses || [];

      if (afterResponses.length > beforeResponses.length) {
        // New response added
        const newResponse = afterResponses[afterResponses.length - 1];
        
        console.log(`New response added to ticket: ${ticketId}`);

        // Create a Brevo email API instance
        const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

        // Send notification to user about the response
        await sendUserResponseNotification(apiInstance, ticketId, afterData, newResponse);
      }

      // Check if status changed
      if (beforeData.status !== afterData.status) {
        console.log(`Ticket ${ticketId} status changed from ${beforeData.status} to ${afterData.status}`);

        // Create a Brevo email API instance
        const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

        // Send status update notification to user
        await sendUserStatusUpdateNotification(apiInstance, ticketId, afterData);
      }

      return null;
    } catch (error) {
      console.error('Error sending support ticket update notifications:', error);
      return null;
    }
  });

/**
 * Send admin notification email for new support ticket
 */
async function sendAdminSupportNotification(apiInstance, ticketId, ticketData) {
  try {
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: '<EMAIL>', name: 'SwipeSense Support' }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense Support System',
      email: '<EMAIL>'
    };

    const priorityLabel = ticketData.priority === 'high' ? ' [HIGH PRIORITY]' : '';
    sendSmtpEmail.subject = `New Support Ticket${priorityLabel}: ${ticketData.subject}`;

    // Generate HTML content
    sendSmtpEmail.htmlContent = generateAdminNotificationEmail(ticketId, ticketData);
    sendSmtpEmail.replyTo = { email: ticketData.userEmail, name: ticketData.userName };

    // Add headers for better organization
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'support-admin-notification',
      'X-Mailin-Tag': 'SupportTicketCreated',
      'X-Ticket-ID': ticketId,
      'X-Ticket-Category': ticketData.category,
      'X-Ticket-Priority': ticketData.priority
    };

    // Add tags for tracking
    sendSmtpEmail.tags = ['support-notification', 'admin-notification', `category-${ticketData.category}`];
    if (ticketData.priority === 'high') {
      sendSmtpEmail.tags.push('high-priority');
    }

    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`Admin notification sent for ticket: ${ticketId}`);
    return result;
  } catch (error) {
    console.error('Error sending admin support notification:', error);
    return null;
  }
}

/**
 * Send confirmation email to user for new support ticket
 */
async function sendUserTicketConfirmation(apiInstance, ticketData) {
  try {
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: ticketData.userEmail, name: ticketData.userName }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense Support',
      email: '<EMAIL>'
    };

    sendSmtpEmail.subject = `Support Ticket Confirmation: ${ticketData.subject}`;

    // Generate HTML content
    sendSmtpEmail.htmlContent = generateUserConfirmationEmail(ticketData);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense Support' };

    // Add headers
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'support-user-confirmation',
      'X-Mailin-Tag': 'SupportTicketConfirmation',
      'X-Ticket-Category': ticketData.category
    };

    // Add tags
    sendSmtpEmail.tags = ['support-confirmation', 'user-notification'];

    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`User confirmation sent for support ticket to: ${ticketData.userEmail}`);
    return result;
  } catch (error) {
    console.error('Error sending user ticket confirmation:', error);
    return null;
  }
}

/**
 * Send notification to user about new response
 */
async function sendUserResponseNotification(apiInstance, ticketId, ticketData, response) {
  try {
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: ticketData.userEmail, name: ticketData.userName }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense Support',
      email: '<EMAIL>'
    };

    sendSmtpEmail.subject = `Support Ticket Response: ${ticketData.subject}`;

    // Generate HTML content
    sendSmtpEmail.htmlContent = generateUserResponseEmail(ticketId, ticketData, response);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense Support' };

    // Add headers
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'support-user-response',
      'X-Mailin-Tag': 'SupportTicketResponse',
      'X-Ticket-ID': ticketId
    };

    // Add tags
    sendSmtpEmail.tags = ['support-response', 'user-notification'];

    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`User response notification sent for ticket: ${ticketId}`);
    return result;
  } catch (error) {
    console.error('Error sending user response notification:', error);
    return null;
  }
}

/**
 * Send status update notification to user
 */
async function sendUserStatusUpdateNotification(apiInstance, ticketId, ticketData) {
  try {
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: ticketData.userEmail, name: ticketData.userName }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense Support',
      email: '<EMAIL>'
    };

    const statusText = getStatusDisplayText(ticketData.status);
    sendSmtpEmail.subject = `Support Ticket Status Update: ${statusText}`;

    // Generate HTML content
    sendSmtpEmail.htmlContent = generateUserStatusUpdateEmail(ticketId, ticketData);
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense Support' };

    // Add headers
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'support-status-update',
      'X-Mailin-Tag': 'SupportStatusUpdate',
      'X-Ticket-ID': ticketId,
      'X-New-Status': ticketData.status
    };

    // Add tags
    sendSmtpEmail.tags = ['support-status-update', 'user-notification', `status-${ticketData.status}`];

    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    console.log(`User status update notification sent for ticket: ${ticketId}`);
    return result;
  } catch (error) {
    console.error('Error sending user status update notification:', error);
    return null;
  }
}

/**
 * Generate HTML content for admin notification email
 */
function generateAdminNotificationEmail(ticketId, ticketData) {
  const createdDate = ticketData.createdAt 
    ? new Date(ticketData.createdAt.toDate()).toLocaleString()
    : new Date().toLocaleString();

  const priorityColor = ticketData.priority === 'high' ? '#FF6B6B' : '#4ECDC4';
  const categoryColors = {
    'account': '#FF6B6B',
    'orders': '#4ECDC4',
    'payments': '#45B7D1',
    'technical': '#96CEB4',
    'seller': '#FECA57',
    'refund': '#FF9FF3',
    'other': '#74B9FF'
  };
  const categoryColor = categoryColors[ticketData.category] || '#74B9FF';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: ${priorityColor};
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .ticket-info {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .category-badge {
          display: inline-block;
          background-color: ${categoryColor};
          color: white;
          padding: 5px 10px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .priority-badge {
          display: inline-block;
          background-color: ${priorityColor};
          color: white;
          padding: 5px 10px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: bold;
          margin-left: 10px;
        }
        .message-box {
          background-color: #FFFACD;
          padding: 15px;
          border-left: 4px solid #4ECDC4;
          margin: 15px 0;
          border-radius: 0 5px 5px 0;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        .action-button {
          display: inline-block;
          background-color: #4ECDC4;
          color: white;
          padding: 12px 25px;
          text-decoration: none;
          border-radius: 5px;
          margin: 20px 0;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎫 New Support Ticket</h1>
          <p>Ticket ID: ${ticketId}</p>
        </div>
        <div class="content">
          <div class="category-badge">${ticketData.category.toUpperCase()}</div>
          <div class="priority-badge">${ticketData.priority.toUpperCase()}</div>
          
          <h2>${ticketData.subject}</h2>
          
          <div class="ticket-info">
            <p><strong>From:</strong> ${ticketData.userName} (${ticketData.userEmail})</p>
            <p><strong>Account Type:</strong> ${ticketData.isSeller ? 'Seller' : 'Buyer'}</p>
            <p><strong>Created:</strong> ${createdDate}</p>
            <p><strong>Category:</strong> ${ticketData.category}</p>
            <p><strong>Priority:</strong> ${ticketData.priority}</p>
            <p><strong>Status:</strong> ${ticketData.status}</p>
          </div>

          <h3>Message:</h3>
          <div class="message-box">
            ${ticketData.message.replace(/\n/g, '<br>')}
          </div>

          <p>Please respond to this ticket as soon as possible. You can reply directly to this email or use the admin dashboard.</p>
        </div>
        <div class="footer">
          <p>This is an automated message from the SwipeSense Support System.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate HTML content for user confirmation email
 */
function generateUserConfirmationEmail(ticketData) {
  const createdDate = ticketData.createdAt 
    ? new Date(ticketData.createdAt.toDate()).toLocaleString()
    : new Date().toLocaleString();

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: #4ECDC4;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .ticket-summary {
          margin: 20px 0;
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
        }
        .message-preview {
          background-color: #FFFACD;
          padding: 15px;
          border-left: 4px solid #4ECDC4;
          margin: 15px 0;
          border-radius: 0 5px 5px 0;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        .next-steps {
          background-color: #E8F5E8;
          padding: 15px;
          border-radius: 5px;
          margin: 15px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>✅ Support Ticket Received</h1>
          <p>We've received your support request</p>
        </div>
        <div class="content">
          <p>Hello ${ticketData.userName},</p>
          
          <p>Thank you for contacting SwipeSense Support. We have successfully received your support ticket and our team will review it shortly.</p>

          <div class="ticket-summary">
            <h3>Your Ticket Details:</h3>
            <p><strong>Subject:</strong> ${ticketData.subject}</p>
            <p><strong>Category:</strong> ${ticketData.category}</p>
            <p><strong>Priority:</strong> ${ticketData.priority}</p>
            <p><strong>Status:</strong> ${ticketData.status}</p>
            <p><strong>Submitted:</strong> ${createdDate}</p>
          </div>

          <h3>Your Message:</h3>
          <div class="message-preview">
            ${ticketData.message.replace(/\n/g, '<br>')}
          </div>

          <div class="next-steps">
            <h3>What happens next?</h3>
            <ul>
              <li>Our support team will review your ticket within 24 hours</li>
              <li>You'll receive an email notification when we respond</li>
              <li>You can check your ticket status in the app under "Support" → "My Tickets"</li>
              <li>For urgent matters, you can also try our Live Chat feature</li>
            </ul>
          </div>

          <p>If you have any additional information to add to this ticket, please reply to this email.</p>

          <p>Thank you for using SwipeSense!</p>
          <p>Best regards,<br>SwipeSense Support Team</p>
        </div>
        <div class="footer">
          <p>This is an automated confirmation from SwipeSense Support.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate HTML content for user response notification email
 */
function generateUserResponseEmail(ticketId, ticketData, response) {
  const responseDate = response.timestamp 
    ? new Date(response.timestamp.toDate()).toLocaleString()
    : new Date().toLocaleString();

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: #45B7D1;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .response-box {
          background-color: #E8F4FD;
          padding: 20px;
          border-left: 4px solid #45B7D1;
          margin: 20px 0;
          border-radius: 0 5px 5px 0;
        }
        .original-message {
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
          margin: 15px 0;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
        .ticket-info {
          background-color: #FFFACD;
          padding: 10px;
          border-radius: 5px;
          margin: 10px 0;
          font-size: 14px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💬 Support Team Response</h1>
          <p>We've responded to your ticket</p>
        </div>
        <div class="content">
          <p>Hello ${ticketData.userName},</p>
          
          <p>Great news! Our support team has responded to your ticket. Please see the response below:</p>

          <div class="ticket-info">
            <strong>Ticket:</strong> ${ticketData.subject} | 
            <strong>Status:</strong> ${ticketData.status} | 
            <strong>Response Time:</strong> ${responseDate}
          </div>

          <div class="response-box">
            <h3>Support Team Response:</h3>
            <p><strong>From:</strong> ${response.responderName || 'SwipeSense Support'}</p>
            <p><strong>Date:</strong> ${responseDate}</p>
            <hr style="border: none; border-top: 1px solid #ccc; margin: 15px 0;">
            ${response.message.replace(/\n/g, '<br>')}
          </div>

          <div class="original-message">
            <h4>Your Original Message:</h4>
            ${ticketData.message.replace(/\n/g, '<br>')}
          </div>

          <p>If you need further assistance or have additional questions, please reply to this email or submit a new support ticket through the app.</p>

          <p>Thank you for using SwipeSense!</p>
          <p>Best regards,<br>SwipeSense Support Team</p>
        </div>
        <div class="footer">
          <p>This is an automated notification from SwipeSense Support.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate HTML content for user status update email
 */
function generateUserStatusUpdateEmail(ticketId, ticketData) {
  const statusText = getStatusDisplayText(ticketData.status);
  const statusColor = getStatusColor(ticketData.status);
  const updateDate = ticketData.updatedAt 
    ? new Date(ticketData.updatedAt.toDate()).toLocaleString()
    : new Date().toLocaleString();

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
        }
        .container {
          padding: 20px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .header {
          background-color: ${statusColor};
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 5px 5px 0 0;
        }
        .content {
          padding: 20px;
          background-color: white;
          border-radius: 0 0 5px 5px;
        }
        .status-update {
          background-color: ${statusColor}20;
          padding: 20px;
          border-left: 4px solid ${statusColor};
          margin: 20px 0;
          border-radius: 0 5px 5px 0;
        }
        .ticket-summary {
          background-color: #f0f0f0;
          padding: 15px;
          border-radius: 5px;
          margin: 15px 0;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #777;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📊 Ticket Status Update</h1>
          <p>Your support ticket status has been updated</p>
        </div>
        <div class="content">
          <p>Hello ${ticketData.userName},</p>
          
          <p>We wanted to update you on the status of your support ticket.</p>

          <div class="status-update">
            <h3>Status Update</h3>
            <p><strong>New Status:</strong> ${statusText}</p>
            <p><strong>Updated:</strong> ${updateDate}</p>
            ${getStatusDescription(ticketData.status)}
          </div>

          <div class="ticket-summary">
            <h3>Ticket Summary:</h3>
            <p><strong>Subject:</strong> ${ticketData.subject}</p>
            <p><strong>Category:</strong> ${ticketData.category}</p>
            <p><strong>Priority:</strong> ${ticketData.priority}</p>
          </div>

          ${getStatusActionText(ticketData.status)}

          <p>You can view your complete ticket history in the app under "Support" → "My Tickets".</p>

          <p>Thank you for using SwipeSense!</p>
          <p>Best regards,<br>SwipeSense Support Team</p>
        </div>
        <div class="footer">
          <p>This is an automated notification from SwipeSense Support.</p>
          <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Get display text for status
 */
function getStatusDisplayText(status) {
  const statusMap = {
    'open': 'Open',
    'in-progress': 'In Progress',
    'resolved': 'Resolved',
    'closed': 'Closed'
  };
  return statusMap[status] || status;
}

/**
 * Get color for status
 */
function getStatusColor(status) {
  const colorMap = {
    'open': '#FF6B6B',
    'in-progress': '#4ECDC4',
    'resolved': '#45B7D1',
    'closed': '#96CEB4'
  };
  return colorMap[status] || '#74B9FF';
}

/**
 * Get status description
 */
function getStatusDescription(status) {
  const descriptions = {
    'open': '<p>Your ticket is open and waiting for our team to review it.</p>',
    'in-progress': '<p>Our support team is actively working on your request.</p>',
    'resolved': '<p>Your issue has been resolved. If you\'re satisfied with the resolution, no further action is needed.</p>',
    'closed': '<p>This ticket has been closed. If you need further assistance, please submit a new support ticket.</p>'
  };
  return descriptions[status] || '';
}

/**
 * Get action text for status
 */
function getStatusActionText(status) {
  const actionTexts = {
    'open': '<p>Our team will review your ticket and respond within 24 hours.</p>',
    'in-progress': '<p>We\'ll keep you updated as we work on resolving your issue.</p>',
    'resolved': '<p>If you\'re not satisfied with the resolution or need further help, please reply to this email.</p>',
    'closed': '<p>If you have any new questions or issues, please submit a new support ticket through the app.</p>'
  };
  return actionTexts[status] || '';
}
