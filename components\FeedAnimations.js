import React, { useEffect, useRef } from 'react';
import { View, Text, Platform, Image, Animated as RNAnimated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './FeedAnimations.styles';

const FeedAnimations = ({
  likeAnimationOpacity,
  dislikeAnimationOpacity,
  likeAnimationScale,
  dislikeAnimationScale,
  showCartAnimation
}) => {
  // Create cart animation values
  const cartAnimationOpacity = useRef(new RNAnimated.Value(0)).current;
  const cartAnimationScale = useRef(new RNAnimated.Value(1)).current; // Initialize to 1

  // Handle cart animation based on showCartAnimation state
  useEffect(() => {
    if (showCartAnimation) {
      console.log('[FeedAnimations] Triggering cart animation');

      // Reset animation values
      cartAnimationOpacity.setValue(0);
      cartAnimationScale.setValue(1); // Ensure scale is 1

      // Start animation
      const animDuration = Platform.OS === 'android' ? 100 : 50;

      RNAnimated.timing(cartAnimationOpacity, { // Only animate opacity
        toValue: 1,
        duration: animDuration,
        useNativeDriver: true
      }).start();
      // Removed RNAnimated.spring for cartAnimationScale

      // Hide animation after display duration
      const displayDuration = Platform.OS === 'android' ? 500 : 300;
      const hideTimer = setTimeout(() => {
        RNAnimated.timing(cartAnimationOpacity, {
          toValue: 0,
          duration: animDuration,
          useNativeDriver: true
        }).start(() => {
          console.log('[FeedAnimations] Cart animation completed');
        });
      }, displayDuration);

      return () => clearTimeout(hideTimer);
    }
  }, [showCartAnimation, cartAnimationOpacity, cartAnimationScale]); // cartAnimationScale is still a dependency
  return (
    <>
      {/* Like Animation */}
      <RNAnimated.View
        style={[
          styles.animationContainer,
          {
            opacity: likeAnimationOpacity,
            transform: [{ scale: likeAnimationScale }],
            elevation: Platform.OS === 'android' ? 1000 : undefined,
          }
        ]}
        pointerEvents="none"
      >
        <Image
          source={require('../assets/animations/heart.png')}
          style={[
            styles.animation,
            Platform.OS === 'android' ? { width: 200, height: 200 } : null
          ]}
          resizeMode="contain"
        />
      </RNAnimated.View>

      {/* Dislike Animation */}
      <RNAnimated.View
        style={[
          styles.animationContainer,
          {
            opacity: dislikeAnimationOpacity,
            transform: [{ scale: dislikeAnimationScale }],
            elevation: Platform.OS === 'android' ? 1000 : undefined,
          }
        ]}
        pointerEvents="none"
      >
        <Image
          source={require('../assets/animations/thumbs.png')}
          style={[
            styles.animation,
            Platform.OS === 'android' ? { width: 200, height: 200 } : null
          ]}
          resizeMode="contain"
        />
      </RNAnimated.View>

      {/* Cart Animation */}
      <RNAnimated.View
        style={[
          styles.animationContainer,
          {
            opacity: cartAnimationOpacity,
            transform: [{ scale: cartAnimationScale }],
            elevation: Platform.OS === 'android' ? 1000 : undefined,
          }
        ]}
        pointerEvents="none"
      >
        <View style={styles.cartAnimationContainer}>
          <Ionicons name="cart" size={Platform.OS === 'android' ? 100 : 80} color="#4CAF50" />
          <Text style={styles.cartAnimationText}>Added to Cart!</Text>
        </View>
      </RNAnimated.View>
    </>
  );
};

export default FeedAnimations;
