/**
 * Generates a random verification code for seller verification
 * @returns {string} A 6-character alphanumeric verification code
 */
export const generateVerificationCode = () => {
  // Define characters to use in the verification code
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Removed similar looking characters (0, O, 1, I)
  let code = '';
  
  // Generate a 6-character code
  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    code += characters.charAt(randomIndex);
  }
  
  return code;
};

/**
 * Formats a verification code with spaces for better readability
 * @param {string} code - The verification code to format
 * @returns {string} Formatted verification code with spaces
 */
export const formatVerificationCode = (code) => {
  if (!code) return '';
  
  // Insert a space after every 3 characters
  return code.replace(/(.{3})/g, '$1 ').trim();
};

/**
 * Validates a verification code against the expected format
 * @param {string} code - The verification code to validate
 * @returns {boolean} Whether the code is valid
 */
export const validateVerificationCode = (code) => {
  if (!code) return false;
  
  // Remove any spaces or non-alphanumeric characters
  const cleanCode = code.replace(/[^A-Z0-9]/g, '');
  
  // Check if the code is 6 characters long
  return cleanCode.length === 6;
};
