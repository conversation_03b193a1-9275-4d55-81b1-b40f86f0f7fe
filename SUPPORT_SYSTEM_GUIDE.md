# StyleApp Support System Documentation

## Overview

The StyleApp now includes a comprehensive support system that provides multiple channels for users to get help and for administrators to manage support tickets efficiently.

## Support System Components

### 1. User-Facing Support Features

#### A. Main Support Screen (`SupportScreen.js`)
- **Location**: Accessible via Settings → Help & Support → Contact Support
- **Features**:
  - Submit support tickets with categorization
  - Quick access to FAQ and Live Chat
  - View existing tickets and their status
  - Email support option

#### B. FAQ System (`FAQScreen.js`)
- **Location**: Accessible via Settings → Help & Support → FAQ & Help
- **Features**:
  - 20+ comprehensive FAQs across 6 categories:
    - Account Management
    - Orders & Payments
    - Selling on StyleApp
    - Returns & Refunds
    - Technical Issues
    - App Features
  - Search functionality
  - Expandable Q&A format

#### C. Live Chat Support (`LiveChatScreen.js`)
- **Location**: Accessible via Settings → Help & Support → Live Chat Support
- **Features**:
  - Real-time messaging with support agents
  - Auto-response system when agents are offline
  - Chat session management
  - Message history

### 2. Admin Support Features

#### A. Admin Support Dashboard (`AdminSupportDashboard.js`)
- **Access**: Admins only (implement your own access control)
- **Features**:
  - View all support tickets with filtering
  - Update ticket status (Open → In Progress → Resolved → Closed)
  - Send responses to user tickets
  - Support statistics and analytics
  - Priority management

#### B. Support Utilities (`supportUtils.js`)
- **Ticket Management**:
  - Create, read, update, delete tickets
  - Auto-categorization based on keywords
  - Status tracking and updates
- **Response System**:
  - Add responses to tickets
  - Template responses for common issues
  - Email notification integration
- **Analytics**:
  - Support statistics
  - Performance metrics
  - Ticket resolution tracking

## How to Use the Support System

### For Users

1. **Submitting a Support Ticket**:
   - Go to MyProfile → Settings (gear icon)
   - Tap "Contact Support"
   - Select appropriate category
   - Fill in subject and detailed message
   - Submit ticket

2. **Accessing FAQ**:
   - Go to Settings → FAQ & Help
   - Browse categories or use search
   - Tap questions to expand answers

3. **Live Chat**:
   - Go to Settings → Live Chat Support
   - Start chatting with support agents
   - Receive auto-responses when agents are offline

4. **Viewing Ticket Status**:
   - In Support Screen, tap "View My Tickets"
   - See all your tickets and their current status
   - Track progress from Open → Resolved

### For Administrators

1. **Accessing Admin Dashboard**:
   - Navigate to AdminSupport screen (implement your own access control)
   - View overview statistics
   - Filter tickets by status

2. **Managing Tickets**:
   - View ticket details
   - Update status (Open, In Progress, Resolved, Closed)
   - Send responses to users
   - Assign priority levels

3. **Response Management**:
   - Tap chat icon on any ticket
   - Write response in modal
   - Send response to user
   - Auto-updates ticket status

## Navigation Integration

The support system is integrated into the app navigation as follows:

```javascript
// Main navigation stack includes:
- Support (SupportScreen)
- FAQ (FAQScreen)  
- LiveChat (LiveChatScreen)
- AdminSupport (AdminSupportDashboard)
```

### Access Points

1. **MyProfileScreen**: "Help & Support" action button
2. **SettingsScreen**: Three support options:
   - Contact Support
   - FAQ & Help  
   - Live Chat Support

## Database Structure

### Support Tickets Collection (`supportTickets`)
```javascript
{
  id: string,
  userId: string,
  subject: string,
  message: string,
  category: string, // account, orders, payments, technical, seller, refund, other
  status: string,   // open, in_progress, resolved, closed
  priority: string, // low, medium, high
  createdAt: timestamp,
  updatedAt: timestamp,
  responses: [
    {
      message: string,
      responderId: string,
      responderName: string,
      createdAt: timestamp,
      isAdminResponse: boolean
    }
  ]
}
```

### Live Chat Sessions Collection (`liveChatSessions`)
```javascript
{
  id: string,
  userId: string,
  status: string, // active, ended
  createdAt: timestamp,
  messages: [
    {
      message: string,
      senderId: string,
      senderName: string,
      timestamp: timestamp,
      isSystemMessage: boolean
    }
  ]
}
```

## Email Integration

The support system includes email notification functionality:

1. **Ticket Creation**: User receives confirmation email
2. **Status Updates**: User notified when ticket status changes
3. **New Responses**: User alerted when admin responds
4. **Auto-Escalation**: High-priority tickets trigger notifications

To enable email notifications, configure your email service in `supportUtils.js`.

## Customization Options

### Adding New Support Categories
Edit the `supportCategories` array in `SupportScreen.js`:

```javascript
const supportCategories = [
  { id: 'new_category', title: 'New Category', icon: 'icon-name' },
  // ... existing categories
];
```

### Modifying FAQ Content
Update the `faqData` array in `FAQScreen.js`:

```javascript
const faqData = [
  {
    category: 'New Category',
    questions: [
      {
        question: 'New question?',
        answer: 'Detailed answer here.'
      }
    ]
  }
];
```

### Auto-Response Templates
Customize auto-responses in `supportUtils.js`:

```javascript
const autoResponses = {
  'account': 'Thank you for contacting us about your account...',
  'payments': 'We understand payment issues can be frustrating...',
  // Add more templates
};
```

## Security Considerations

1. **Admin Access**: Implement proper authentication for AdminSupportDashboard
2. **Data Privacy**: Ensure ticket data is only accessible to relevant users
3. **Input Validation**: Validate all user inputs before saving to database
4. **Rate Limiting**: Prevent spam by limiting ticket submission frequency

## Future Enhancements

1. **File Attachments**: Allow users to attach screenshots/files to tickets
2. **Push Notifications**: Real-time notifications for ticket updates
3. **Agent Assignment**: Automatic or manual assignment of tickets to specific agents
4. **Knowledge Base**: Searchable knowledge base with articles
5. **Escalation Rules**: Automatic escalation based on time or priority
6. **Satisfaction Surveys**: Post-resolution feedback collection
7. **Multi-language Support**: Support for multiple languages
8. **Voice/Video Chat**: Integration with voice/video calling
9. **AI Chatbot**: Automated initial response system
10. **Support Analytics**: Detailed reporting and analytics dashboard

## Troubleshooting

### Common Issues

1. **Tickets Not Loading**: Check Firebase permissions and authentication
2. **Chat Not Working**: Verify Firestore rules allow real-time updates
3. **Email Notifications**: Ensure email service is properly configured
4. **Admin Access**: Implement proper admin role checking

### Debug Mode

Enable debug logging in `supportUtils.js` by setting:
```javascript
const DEBUG_MODE = true;
```

This will log all support operations to the console for debugging.

## Support Contact

For technical issues with the support system itself, contact the development team or create a ticket in the admin dashboard.

---

*Last updated: May 25, 2025*
