const functions = require('firebase-functions');
const admin = require('firebase-admin');
const SibApiV3Sdk = require('sib-api-v3-sdk'); // Brevo (formerly Sendinblue) SDK

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Initialize Brevo API client
const defaultClient = SibApiV3Sdk.ApiClient.instance;
const apiKey = defaultClient.authentications['api-key'];
const brevoApiKey = functions.config().brevo?.apikey || process.env.BREVO_API_KEY; // Get from Firebase config or fallback to env var
console.log('Using Brevo API Key:', brevoApiKey ? 'API key is set' : 'API key is missing');
apiKey.apiKey = brevoApiKey;

/**
 * Cloud Function that was previously used to send an email notification to the admin when a seller submits verification details.
 * Now it just logs the event but doesn't send an email to avoid duplicate emails.
 * The email will be sent manually by the seller from the VerificationPendingScreen.
 */
exports.notifyAdminOfSellerVerification = functions.firestore
  .document('sellerVerifications/{verificationId}')
  .onCreate(async (snapshot, context) => {
    try {
      const verificationData = snapshot.data();
      const { email, name } = verificationData;

      // Log the event but don't send an email
      console.log(`New seller verification document created for ${email || name || 'unknown user'}`);
      console.log('Automatic email notification is disabled to prevent duplicate emails.');

      // Return early without sending an email
      return null;
    } catch (error) {
      console.error('Error sending seller verification notification:', error);
      return null;
    }
  });

/**
 * Function to send verification code to admin (<EMAIL>)
 * This function will be triggered when a seller enters the verification code screen
 */
exports.sendVerificationCodeToAdmin = functions.https.onCall(async (data, context) => {
  try {
    // Check if the request is from an authenticated user
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'You must be logged in to perform this action.'
      );
    }

    const userId = context.auth.uid;
    const { email } = data;

    if (!email) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email is required.'
      );
    }

    console.log(`Sending verification code to admin for user: ${userId}, email: ${email}`);

    // Get the user's verification document
    const verificationQuery = await admin.firestore()
      .collection('sellerVerifications')
      .where('userId', '==', userId)
      .limit(1)
      .get();

    if (verificationQuery.empty) {
      throw new functions.https.HttpsError(
        'not-found',
        'Verification document not found for this user.'
      );
    }

    const verificationDoc = verificationQuery.docs[0];
    const verificationData = verificationDoc.data();
    const { verificationCode, name, address, website, phoneNumber } = verificationData;

    // Get the user document to get the username if name is not provided
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    const userData = userDoc.exists ? userDoc.data() : {};
    const displayName = name || userData.username || userData.displayName || email.split('@')[0];

    // Create a Brevo email API instance
    const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

    // Prepare the HTML content with a modern design
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
          }
          .container {
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
          }
          .header {
            background-color: #ff6b6b;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
          }
          .content {
            padding: 20px;
            background-color: white;
            border-radius: 0 0 5px 5px;
          }
          .code {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            background-color: #f0f0f0;
            border-radius: 5px;
            letter-spacing: 5px;
          }
          .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #777;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>SwipeSense Seller Verification Code</h1>
          </div>
          <div class="content">
            <p>Hello Admin,</p>
            <p>A seller has requested verification. Here are the details:</p>

            <ul>
              <li><strong>Name:</strong> ${displayName}</li>
              <li><strong>Email:</strong> ${email}</li>
              <li><strong>Phone:</strong> ${phoneNumber || 'Not provided'}</li>
              <li><strong>Website:</strong> ${website || 'Not provided'}</li>
              <li><strong>Address:</strong> ${address ? `${address.street || ''}, ${address.city || ''}, ${address.state || ''}, ${address.country || ''}, ${address.zipCode || address.pincode || ''}` : 'Not provided'}</li>
            </ul>

            <p>The verification code for this seller is:</p>

            <div class="code">${verificationCode}</div>

            <p>The seller is waiting for this code to complete their verification process.</p>

            <p>Best regards,<br>SwipeSense System</p>
          </div>
          <div class="footer">
            <p>This is an automated message from the SwipeSense system.</p>
            <p>&copy; ${new Date().getFullYear()} SwipeSense. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Prepare email content using Brevo format
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

    // Configure email details
    sendSmtpEmail.to = [{ email: '<EMAIL>', name: 'SwipeSense Admin' }];
    sendSmtpEmail.sender = {
      name: 'SwipeSense',
      email: '<EMAIL>'
    };
    sendSmtpEmail.subject = `Seller Verification Code for: ${displayName}`;
    sendSmtpEmail.htmlContent = htmlContent;
    sendSmtpEmail.replyTo = { email: '<EMAIL>', name: 'SwipeSense' };

    // Add headers for better deliverability
    sendSmtpEmail.headers = {
      'X-Mailin-custom': 'verification-code',
      'X-Mailin-Tag': 'SellerVerificationCode'
    };

    // Add tags for better tracking
    sendSmtpEmail.tags = ['verification-code', 'admin-notification'];

    // Send email using Brevo API
    console.log('Attempting to send verification code to admin at:', '<EMAIL>');

    try {
      const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
      console.log(`Verification code sent to admin for seller ${email}`);
      console.log('Email send result:', result);

      // Update the verification document to indicate that the code has been sent
      await verificationDoc.ref.update({
        codeSentToAdmin: true,
        codeSentAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      return { success: true, message: 'Verification code sent to admin' };
    } catch (emailError) {
      console.error('Error sending verification code to admin with Brevo:', emailError);
      console.error('Error details:', JSON.stringify(emailError, null, 2));
      throw new functions.https.HttpsError(
        'internal',
        `Failed to send verification code: ${emailError.message}`
      );
    }
  } catch (error) {
    console.error('Error in sendVerificationCodeToAdmin function:', error);
    throw new functions.https.HttpsError(
      'internal',
      `Failed to process request: ${error.message}`
    );
  }
});

/**
 * Cloud Function that updates the user document when a seller verification is completed
 */
exports.updateUserOnVerificationStatusChange = functions.firestore
  .document('sellerVerifications/{verificationId}')
  .onUpdate(async (change, context) => {
    try {
      const beforeData = change.before.data();
      const afterData = change.after.data();

      // If status changed from needsCode to verified
      if (beforeData.status === 'needsCode' && afterData.status === 'verified') {
        const userId = afterData.userId;

        // Update the user document
        await admin.firestore().collection('users').doc(userId).update({
          sellerVerificationStatus: 'verified',
          verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`User ${userId} updated with verified seller status`);
      }

      return null;
    } catch (error) {
      console.error('Error updating user on verification status change:', error);
      return null;
    }
  });
