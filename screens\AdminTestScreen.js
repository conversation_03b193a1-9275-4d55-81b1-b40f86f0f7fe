import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth } from '../firebase.config';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import {
  testAndSetAdminAccess,
  testAdminPermissions,
  makeCurrentUserAdmin
} from '../utils/testAdminAccess';

const AdminTestScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState(null);

  const handleMakeAdmin = async () => {
    try {
      setLoading(true);
      Alert.alert(
        'Make Admin',
        `Are you sure you want to make ${auth.currentUser?.email} an administrator?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Yes, Make Admin',
            onPress: async () => {
              try {
                await makeCurrentUserAdmin();
                Alert.alert(
                  'Success!',
                  'You are now an administrator. You can access the Admin Dashboard from your profile.',
                  [
                    {
                      text: 'Go to Profile',
                      onPress: () => navigation.navigate('MyProfile')
                    }
                  ]
                );
              } catch (error) {
                Alert.alert('Error', error.message);
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTestPermissions = async () => {
    try {
      setLoading(true);
      const results = await testAdminPermissions();
      setTestResults(results);
    } catch (error) {
      Alert.alert('Error', 'Failed to test permissions');
    } finally {
      setLoading(false);
    }
  };

  const handleTestAndSetAdmin = async () => {
    try {
      setLoading(true);
      const success = await testAndSetAdminAccess();
      if (success) {
        Alert.alert(
          'Success!',
          'Admin access verified/granted. You can now access the Admin Dashboard.',
          [
            {
              text: 'Go to Profile',
              onPress: () => navigation.navigate('MyProfile')
            }
          ]
        );
      } else {
        Alert.alert('Failed', 'Could not verify or grant admin access');
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderTestResult = (collectionName, result) => (
    <View key={collectionName} style={styles.testResultItem}>
      <View style={styles.testResultHeader}>
        <Text style={styles.collectionName}>{collectionName}</Text>
        <Ionicons
          name={result.accessible ? "checkmark-circle" : "close-circle"}
          size={20}
          color={result.accessible ? "#4CAF50" : "#FF6B6B"}
        />
      </View>
      {result.accessible ? (
        <Text style={styles.testResultSuccess}>
          ✅ Accessible ({result.count} documents)
        </Text>
      ) : (
        <Text style={styles.testResultError}>
          ❌ {result.error}
        </Text>
      )}
    </View>
  );

  return (
    <SafeAreaWrapper>
      {/* Header */}
      <SafeAreaHeader>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Admin Setup & Testing</Text>
        <View style={styles.headerSpacer} />
      </SafeAreaHeader>

      <View style={styles.container}>

        <ScrollView style={styles.content}>
          {/* User Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current User</Text>
            <View style={styles.userInfo}>
              <Text style={styles.userEmail}>{auth.currentUser?.email}</Text>
              <Text style={styles.userUid}>UID: {auth.currentUser?.uid}</Text>
            </View>
          </View>

          {/* Admin Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Admin Setup</Text>

            <TouchableOpacity
              style={[styles.actionButton, styles.primaryButton]}
              onPress={handleMakeAdmin}
              disabled={loading}
            >
              <Ionicons name="shield-checkmark" size={20} color="white" />
              <Text style={styles.actionButtonText}>Make Me Admin</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={handleTestAndSetAdmin}
              disabled={loading}
            >
              <Ionicons name="checkmark-done" size={20} color="white" />
              <Text style={styles.actionButtonText}>Test & Set Admin Access</Text>
            </TouchableOpacity>
          </View>

          {/* Testing Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Permission Testing</Text>

            <TouchableOpacity
              style={[styles.actionButton, styles.testButton]}
              onPress={handleTestPermissions}
              disabled={loading}
            >
              <Ionicons name="analytics" size={20} color="white" />
              <Text style={styles.actionButtonText}>Test Database Permissions</Text>
            </TouchableOpacity>
          </View>

          {/* Test Results */}
          {testResults && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Permission Test Results</Text>
              <View style={styles.testResults}>
                {Object.entries(testResults).map(([collection, result]) =>
                  renderTestResult(collection, result)
                )}
              </View>
            </View>
          )}

          {/* Instructions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Instructions</Text>
            <View style={styles.instructions}>
              <Text style={styles.instructionText}>
                1. <Text style={styles.bold}>Make Me Admin</Text> - Sets your account as an administrator
              </Text>
              <Text style={styles.instructionText}>
                2. <Text style={styles.bold}>Test Permissions</Text> - Checks if you can access admin collections
              </Text>
              <Text style={styles.instructionText}>
                3. After becoming admin, go to your <Text style={styles.bold}>Profile</Text> to access the Admin Dashboard
              </Text>
              <Text style={styles.instructionText}>
                4. If you get permission errors, make sure Firestore rules are deployed
              </Text>
            </View>
          </View>

          {loading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color="#FF6B6B" />
              <Text style={styles.loadingText}>Processing...</Text>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSpacer: {
    width: 40, // Same width as back button for centering
  },
  content: {
    flex: 1,
  },
  section: {
    margin: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  userInfo: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
  },
  userEmail: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  userUid: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#FF6B6B',
  },
  secondaryButton: {
    backgroundColor: '#4ECDC4',
  },
  testButton: {
    backgroundColor: '#45B7D1',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  testResults: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
  },
  testResultItem: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  testResultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  collectionName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  testResultSuccess: {
    fontSize: 12,
    color: '#4CAF50',
  },
  testResultError: {
    fontSize: 12,
    color: '#FF6B6B',
  },
  instructions: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  bold: {
    fontWeight: 'bold',
    color: '#333',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
});

export default AdminTestScreen;
