# SwipeSense

SwipeSense is a mobile application built with React Native and Expo.

## Installation

1. Clone the repository.
2. Install dependencies:
   ```bash
   npm install
   ```

## Running the App

- To start the development server:
  ```bash
  npx expo start
  ```
- To run on Android:
  ```bash
  npx expo start --android
  ```
- To run on iOS:
  ```bash
  npx expo start --ios
  ```
- To run on Web:
  ```bash
  npx expo start --web
  ```

## Key Features

- User Authentication (Email/Password, Google Sign-In)
- Firebase Integration (Firestore, Storage, Functions)
- Payment Integration (Razorpay)
- Image Upload and Manipulation
- Real-time Chat Functionality
- Push Notifications
- Profile Management
- Product Listings and Details
- Shopping Cart and Checkout
- Order Management
- Seller Verification and Metrics

## Tech Stack

- React Native
- Expo
- Firebase (Authentication, Firestore, Storage, Functions)
- React Navigation
- Razorpay SDK

## Project Structure

The project is organized into the following main directories:

- `assets/`: Contains static assets like images, icons, and fonts.
- `components/`: Reusable UI components.
- `functions/`: Firebase Cloud Functions.
- `screens/`: Application screens/views.
- `styles/`: Global styles and theme configurations.
- `utils/`: Utility functions and helpers.

## Important Notes

- **Razorpay Setup**: Razorpay requires a development build and will not work in Expo Go. Please refer to `RAZORPAY_SETUP.md` for detailed instructions on creating a development build and configuring Razorpay.
- **Firebase Setup**: Ensure you have `google-services.json` (for Android) and `GoogleService-Info.plist` (for iOS) configured correctly for Firebase integration. Refer to `FIREBASE_FIXES.md`, `FIREBASE_FUNCTIONS_DEPLOYMENT.md`, `FIREBASE_PERMISSION_FIX.md`, and `FIREBASE_PERMISSIONS_FIX.md` for common Firebase setup and troubleshooting.

