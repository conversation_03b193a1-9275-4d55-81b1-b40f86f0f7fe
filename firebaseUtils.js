import { db } from './firebase.config'; // Assuming db is exported from firebase.config.js
import { collection, getDocs, query, where, orderBy, limit, doc, updateDoc, increment, serverTimestamp } from 'firebase/firestore';

// Fetch clothing items, optionally filtered by category
export const fetchClothingItems = async (category = 'All', count = 10) => {
  try {
    const itemsCollection = collection(db, 'clothingItems'); // Assuming your collection is named 'clothingItems'
    let itemsQuery;

    if (category === 'All') {
      // Fetch any 10 items initially, maybe order by timestamp later?
      itemsQuery = query(itemsCollection, limit(count));
    } else {
      // Fetch items matching the category
      itemsQuery = query(itemsCollection, where('category', '==', category), limit(count));
    }

    const querySnapshot = await getDocs(itemsQuery);
    const items = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    console.log(`Fetched ${items.length} items for category: ${category}`);
    return items;
  } catch (error) {
    console.error("Error fetching clothing items:", error);
    throw error; // Re-throw the error to be caught in the component
  }
};

// Increment like count for an item
export const likeItem = async (itemId) => {
  try {
    const itemRef = doc(db, 'clothingItems', itemId);
    await updateDoc(itemRef, {
      likeCount: increment(1)
      // Optionally add a timestamp for the like action if needed
    });
    console.log(`Liked item: ${itemId}`);
  } catch (error) {
    console.error("Error liking item:", error);
  }
};

// Placeholder for dislike action if needed (e.g., tracking dislikes)
export const dislikeItem = async (itemId) => {
  // Currently, dislike just moves to the next item in the frontend.
  // Add logic here if you need to track dislikes in Firestore.
  console.log(`Disliked item: ${itemId}`);
};

// Add more functions as needed (e.g., fetching item details, user data, etc.)
