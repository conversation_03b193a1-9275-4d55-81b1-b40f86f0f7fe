import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import HierarchicalCategorySelector from '../components/HierarchicalCategorySelector';
import RecommendationSettings from '../components/RecommendationSettings';
import { validateCategorySelection } from '../utils/categoryHierarchy';
import { RecommendationEngine } from '../utils/recommendationEngine';

const CategoryTestScreen = ({ navigation }) => {
  const [broadCategory, setBroadCategory] = useState('');
  const [detailedCategory, setDetailedCategory] = useState('');
  const [useRecommendations, setUseRecommendations] = useState(true);
  const [recommendationThreshold, setRecommendationThreshold] = useState(0.3);
  const [testResults, setTestResults] = useState([]);

  const recommendationEngine = new RecommendationEngine();

  const runCategoryTest = () => {
    const validation = validateCategorySelection(broadCategory, detailedCategory);
    
    if (validation.valid) {
      Alert.alert(
        'Category Test Passed',
        `✅ Valid selection:\nBroad: ${broadCategory}\nDetailed: ${detailedCategory}`
      );
    } else {
      Alert.alert(
        'Category Test Failed',
        `❌ ${validation.error}`
      );
    }
  };

  const runRecommendationTest = async () => {
    try {
      // Create sample items for testing
      const sampleItems = [
        {
          id: '1',
          broadCategory: 'Shirts',
          detailedCategory: 'Casual Shirts',
          tags: ['blue', 'cotton'],
          brand: 'nike',
          price: 2000,
          size: 'm'
        },
        {
          id: '2',
          broadCategory: 'Jeans',
          detailedCategory: 'Skinny Jeans',
          tags: ['black', 'denim'],
          brand: 'levis',
          price: 3500,
          size: 'm'
        },
        {
          id: '3',
          broadCategory: 'Shirts',
          detailedCategory: 'Formal Shirts',
          tags: ['white', 'cotton'],
          brand: 'nike',
          price: 2500,
          size: 'm'
        }
      ];

      // Test feature extraction
      const features1 = recommendationEngine.extractFeatures(sampleItems[0]);
      const features2 = recommendationEngine.extractFeatures(sampleItems[1]);
      
      // Test similarity calculation
      const similarity = recommendationEngine.calculateItemSimilarity(sampleItems[0], sampleItems[2]);
      
      const results = [
        `Feature extraction test: ✅`,
        `Item 1 features: ${JSON.stringify(features1, null, 2)}`,
        `Item 2 features: ${JSON.stringify(features2, null, 2)}`,
        `Similarity between shirts: ${similarity.toFixed(3)}`,
        `Recommendation engine initialized: ✅`
      ];
      
      setTestResults(results);
      
      Alert.alert(
        'Recommendation Test Results',
        `✅ All tests passed!\nSimilarity score: ${similarity.toFixed(3)}`
      );
    } catch (error) {
      Alert.alert('Test Failed', `❌ ${error.message}`);
    }
  };

  const updateRecommendationSettings = (newUseRecommendations, newThreshold) => {
    if (newUseRecommendations !== undefined) {
      setUseRecommendations(newUseRecommendations);
    }
    if (newThreshold !== undefined) {
      setRecommendationThreshold(newThreshold);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.headerButton}
        >
          <Ionicons name="chevron-back" size={28} color="#FF6B6B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Category System Test</Text>
        <View style={styles.headerButton} />
      </View>

      <View style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. Hierarchical Category Test</Text>
          <Text style={styles.sectionDescription}>
            Test the hierarchical category selector component
          </Text>
          
          <HierarchicalCategorySelector
            selectedBroadCategory={broadCategory}
            selectedDetailedCategory={detailedCategory}
            onBroadCategorySelect={setBroadCategory}
            onDetailedCategorySelect={setDetailedCategory}
            style={styles.categorySelector}
          />

          <TouchableOpacity
            style={styles.testButton}
            onPress={runCategoryTest}
          >
            <Ionicons name="play-circle" size={20} color="#fff" />
            <Text style={styles.testButtonText}>Test Category Selection</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. Recommendation Settings Test</Text>
          <Text style={styles.sectionDescription}>
            Test the recommendation settings component
          </Text>
          
          <RecommendationSettings
            useRecommendations={useRecommendations}
            recommendationThreshold={recommendationThreshold}
            onUpdateSettings={updateRecommendationSettings}
            style={styles.recommendationSettings}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>3. Recommendation Engine Test</Text>
          <Text style={styles.sectionDescription}>
            Test the cosine similarity recommendation engine
          </Text>
          
          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: '#4CAF50' }]}
            onPress={runRecommendationTest}
          >
            <Ionicons name="analytics" size={20} color="#fff" />
            <Text style={styles.testButtonText}>Test Recommendation Engine</Text>
          </TouchableOpacity>
        </View>

        {testResults.length > 0 && (
          <View style={styles.resultsSection}>
            <Text style={styles.sectionTitle}>Test Results</Text>
            {testResults.map((result, index) => (
              <Text key={index} style={styles.resultText}>
                {result}
              </Text>
            ))}
          </View>
        )}

        <View style={styles.statusSection}>
          <Text style={styles.sectionTitle}>Implementation Status</Text>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={styles.statusText}>Hierarchical Categories ✅</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={styles.statusText}>Recommendation Engine ✅</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={styles.statusText}>Settings Integration ✅</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={styles.statusText}>Upload Screen Updated ✅</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            <Text style={styles.statusText}>Migration Tools ✅</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 10,
    paddingHorizontal: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  headerButton: {
    padding: 8,
    width: 40,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  categorySelector: {
    marginBottom: 20,
  },
  recommendationSettings: {
    marginBottom: 10,
  },
  testButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 8,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  resultsSection: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
  },
  resultText: {
    fontSize: 12,
    color: '#2E7D32',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
  statusSection: {
    padding: 20,
    backgroundColor: '#FFF3E0',
    borderRadius: 12,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
});

export default CategoryTestScreen;
