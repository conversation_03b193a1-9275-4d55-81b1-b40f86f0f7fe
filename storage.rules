rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow public read access to all files
    match /{allPaths=**} {
      allow read: if true;
    }
    
    // Allow authenticated users to upload to their own folders
    match /profilePictures/{userId}/{fileName} {
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to upload to their own clothing items folder
    match /clothingItems/{userId}/{fileName} {
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow write: if false;
    }
  }
}
