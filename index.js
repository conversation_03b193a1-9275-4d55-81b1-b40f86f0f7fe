import { AppRegistry, Platform } from 'react-native';
import App from './App';
import { enableLayoutAnimations } from 'react-native-reanimated';
import 'react-native-gesture-handler';

// Enable layout animations for both platforms
enableLayoutAnimations(true);

// Register the app with the correct name
// Note: The app name should match what's expected by the native code
AppRegistry.registerComponent('StyleApp', () => App);