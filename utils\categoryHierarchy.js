/**
 * Hierarchical Category System for Clothing Items
 * This defines the broad categories and their detailed subcategories
 */

export const categoryHierarchy = {
  // Tops
  'Shirts': [
    'Casual Shirts',
    'Formal Shirts',
    'Polo Shirts',
    'Striped Shirts',
    'Solid Color Shirts',
    'Printed Shirts',
    'Denim Shirts',
    'Flannel Shirts'
  ],
  'T-Shirts': [
    'Basic Tees',
    'Graphic Tees',
    'V-Neck',
    'Crew Neck',
    'Long Sleeve',
    'Tank Tops',
    'Oversized Tees',
    'Fitted Tees'
  ],
  'Blouses': [
    'Casual Blouses',
    'Formal Blouses',
    'Silk Blouses',
    'Chiffon Blouses',
    'Printed Blouses',
    'Solid Blouses'
  ],
  'Sweaters': [
    'Pullover Sweaters',
    'Cardigan Sweaters',
    'Turtleneck Sweaters',
    'V-Neck Sweaters',
    'Crew Neck Sweaters',
    'Oversized Sweaters'
  ],  'Hoodies': [
    'Pullover Hoodies',
    'Zip-Up Hoodies',
    'Oversized Hoodies',
    'Cropped Hoodies',
    'Graphic Hoodies'
  ],  'Tops': [
    'Tube Tops',
    'Halter Tops',
    'Crop Tops',
    'Peplum Tops',
    'Sleeveless Tops',
    'Off-Shoulder Tops',
    'Cold Shoulder Tops',
    'Wrap Tops',
    'Asymmetric Tops',
    'Mesh Tops',
    'Camisoles',
    'Bustiers',
    'Corset Tops',
    'Bandeau Tops'
  ],
  'Co-ord': [
    'Two Piece Sets',
    'Matching Top and Bottom',
    'Crop Top and Skirt Set',
    'Blazer and Trouser Set',
    'Tank and Shorts Set',
    'Bralette and Pants Set',
    'Cardigan and Skirt Set',
    'Sweater and Leggings Set',
    'Shirt and Shorts Set',
    'Bodysuit and Pants Set',
    'Hoodie and Joggers Set',
    'Knit Co-ord Set',
    'Formal Co-ord Set',
    'Casual Co-ord Set',
    'Sports Co-ord Set',
    'Lounge Co-ord Set'
  ],

  // Bottoms
  'Jeans': [
    'Skinny Jeans',
    'Straight Jeans',
    'Bootcut Jeans',
    'Wide Leg Jeans',
    'High Waisted Jeans',
    'Low Rise Jeans',
    'Distressed Jeans',
    'Dark Wash Jeans',
    'Light Wash Jeans'
  ],  'Pants': [
    'Chinos',
    'Dress Pants',
    'Cargo Pants',
    'Joggers',
    'Wide Leg Pants',
    'Straight Leg Pants',
    'Cropped Pants',
    'Tailored Pants'
  ],
  'Shorts': [
    'Denim Shorts',
    'Chino Shorts',
    'Athletic Shorts',
    'Cargo Shorts',
    'High Waisted Shorts',
    'Bermuda Shorts'
  ],
  'Skirts': [
    'Mini Skirts',
    'Midi Skirts',
    'Maxi Skirts',
    'A-Line Skirts',
    'Pencil Skirts',
    'Pleated Skirts',
    'Denim Skirts'
  ],
  'Leggings': [
    'Athletic Leggings',
    'Casual Leggings',
    'High Waisted Leggings',
    'Cropped Leggings',
    'Printed Leggings'
  ],

  // Dresses
  'Casual Dresses': [
    'Sundresses',
    'Shirt Dresses',
    'T-Shirt Dresses',
    'Wrap Dresses',
    'A-Line Dresses',
    'Maxi Dresses',
    'Mini Dresses'
  ],
  'Formal Dresses': [
    'Cocktail Dresses',
    'Evening Gowns',
    'Business Dresses',
    'Little Black Dresses',
    'Midi Formal Dresses'
  ],
  'Party Dresses': [
    'Sequin Dresses',
    'Bodycon Dresses',
    'Off-Shoulder Dresses',
    'Backless Dresses',
    'Cut-Out Dresses'
  ],

  // Outerwear
  'Jackets': [
    'Denim Jackets',
    'Leather Jackets',
    'Bomber Jackets',
    'Blazers',
    'Windbreakers',
    'Puffer Jackets'
  ],
  'Coats': [
    'Trench Coats',
    'Wool Coats',
    'Pea Coats',
    'Long Coats',
    'Winter Coats'
  ],
  'Cardigans': [
    'Open Front Cardigans',
    'Button-Up Cardigans',
    'Long Cardigans',
    'Cropped Cardigans',
    'Oversized Cardigans'
  ],

  // Shoes
  'Sneakers': [
    'Running Shoes',
    'Casual Sneakers',
    'High-Top Sneakers',
    'Low-Top Sneakers',
    'Platform Sneakers',
    'Slip-On Sneakers'
  ],
  'Boots': [
    'Ankle Boots',
    'Knee-High Boots',
    'Combat Boots',
    'Chelsea Boots',
    'Hiking Boots',
    'Rain Boots'
  ],
  'Heels': [
    'Stiletto Heels',
    'Block Heels',
    'Wedge Heels',
    'Platform Heels',
    'Kitten Heels'
  ],
  'Flats': [
    'Ballet Flats',
    'Loafers',
    'Oxford Shoes',
    'Slip-On Flats',
    'Pointed Toe Flats'
  ],
  'Sandals': [
    'Flip Flops',
    'Strappy Sandals',
    'Platform Sandals',
    'Slide Sandals',
    'Gladiator Sandals'
  ],

  // Accessories
  'Bags': [
    'Handbags',
    'Backpacks',
    'Crossbody Bags',
    'Tote Bags',
    'Clutches',
    'Shoulder Bags',
    'Messenger Bags'
  ],
  'Jewelry': [
    'Necklaces',
    'Earrings',
    'Bracelets',
    'Rings',
    'Watches',
    'Anklets'
  ],
  'Hats': [
    'Baseball Caps',
    'Beanies',
    'Sun Hats',
    'Fedoras',
    'Bucket Hats'
  ],
  'Scarves': [
    'Silk Scarves',
    'Wool Scarves',
    'Cotton Scarves',
    'Infinity Scarves',
    'Bandanas'
  ],
  'Belts': [
    'Leather Belts',
    'Chain Belts',
    'Fabric Belts',
    'Wide Belts',
    'Skinny Belts'
  ],  'Sunglasses': [
    'Aviator Sunglasses',
    'Cat Eye Sunglasses',
    'Round Sunglasses',
    'Square Sunglasses',
    'Oversized Sunglasses'
  ],

  // Indian Traditional Wear - Women
  'Sarees': [
    'Silk Sarees',
    'Cotton Sarees',
    'Chiffon Sarees',
    'Georgette Sarees',
    'Banarasi Sarees',
    'Kanjivaram Sarees',
    'Tant Sarees',
    'Handloom Sarees',
    'Designer Sarees',
    'Printed Sarees',
    'Embroidered Sarees',
    'Net Sarees',
    'Organza Sarees',
    'Crepe Sarees',
    'Linen Sarees'
  ],
  'Lehenga': [
    'Bridal Lehenga',
    'Party Lehenga',
    'A-Line Lehenga',
    'Flared Lehenga',
    'Mermaid Lehenga',
    'Sharara Lehenga',
    'Gharara Lehenga',
    'Crop Top Lehenga',
    'Long Lehenga',
    'Short Lehenga',
    'Heavy Work Lehenga',
    'Simple Lehenga',
    'Designer Lehenga'
  ],
  'Salwar Suit': [
    'Anarkali Suit',
    'Straight Cut Suit',
    'A-Line Suit',
    'Palazzo Suit',
    'Sharara Suit',
    'Gharara Suit',
    'Patiala Suit',
    'Churidar Suit',
    'Pakistani Suit',
    'Designer Suit',
    'Cotton Suit',
    'Silk Suit',
    'Georgette Suit',
    'Chiffon Suit'
  ],
  'Kurti': [
    'Straight Kurti',
    'A-Line Kurti',
    'Anarkali Kurti',
    'High-Low Kurti',
    'Asymmetric Kurti',
    'Long Kurti',
    'Short Kurti',
    'Sleeveless Kurti',
    'Cotton Kurti',
    'Silk Kurti',
    'Printed Kurti',
    'Embroidered Kurti',
    'Designer Kurti'
  ],
  'Sharara': [
    'Traditional Sharara',
    'Designer Sharara',
    'Heavy Work Sharara',
    'Simple Sharara',
    'Bridal Sharara',
    'Party Sharara'
  ],
  'Gharara': [
    'Traditional Gharara',
    'Designer Gharara',
    'Bridal Gharara',
    'Party Gharara',
    'Heavy Work Gharara'
  ],
  'Dupatta': [
    'Silk Dupatta',
    'Chiffon Dupatta',
    'Georgette Dupatta',
    'Net Dupatta',
    'Cotton Dupatta',
    'Embroidered Dupatta',
    'Printed Dupatta',
    'Plain Dupatta',
    'Heavy Dupatta',
    'Light Dupatta'
  ],

  // Indian Traditional Wear - Men
  'Kurta': [
    'Cotton Kurta',
    'Silk Kurta',
    'Linen Kurta',
    'Short Kurta',
    'Long Kurta',
    'Collar Kurta',
    'Band Collar Kurta',
    'Nehru Collar Kurta',
    'Printed Kurta',
    'Plain Kurta',
    'Embroidered Kurta',
    'Designer Kurta'
  ],
  'Dhoti': [
    'Cotton Dhoti',
    'Silk Dhoti',
    'Ready-made Dhoti',
    'Traditional Dhoti',
    'Designer Dhoti',
    'Printed Dhoti',
    'Plain Dhoti'
  ],
  'Lungi': [
    'Cotton Lungi',
    'Silk Lungi',
    'Printed Lungi',
    'Plain Lungi',
    'Traditional Lungi',
    'Designer Lungi'
  ],
  'Sherwani': [
    'Wedding Sherwani',
    'Party Sherwani',
    'Designer Sherwani',
    'Silk Sherwani',
    'Velvet Sherwani',
    'Embroidered Sherwani',
    'Simple Sherwani',
    'Indo-Western Sherwani'
  ],
  'Achkan': [
    'Traditional Achkan',
    'Designer Achkan',
    'Wedding Achkan',
    'Party Achkan',
    'Silk Achkan',
    'Velvet Achkan'
  ],
  'Nehru Jacket': [
    'Cotton Nehru Jacket',
    'Silk Nehru Jacket',
    'Velvet Nehru Jacket',
    'Printed Nehru Jacket',
    'Plain Nehru Jacket',
    'Designer Nehru Jacket'
  ],
  'Pajama': [
    'Churidar Pajama',
    'Straight Pajama',
    'Dhoti Pajama',
    'Cotton Pajama',
    'Silk Pajama',
    'Designer Pajama'
  ],

  // Indian Footwear
  'Juttis': [
    'Punjabi Jutti',
    'Rajasthani Jutti',
    'Embroidered Jutti',
    'Plain Jutti',
    'Leather Jutti',
    'Velvet Jutti',
    'Silk Jutti',
    'Beaded Jutti'
  ],
  'Kolhapuri': [
    'Traditional Kolhapuri',
    'Designer Kolhapuri',
    'Leather Kolhapuri',
    'Colored Kolhapuri',
    'Plain Kolhapuri'
  ],

  // Regional Specialties
  'Pattu Sarees': [
    'Kanjivaram Pattu',
    'Mysore Silk',
    'Pochampally Silk',
    'Gadwal Silk',
    'Uppada Silk'
  ],
  'Bandhani': [
    'Bandhani Saree',
    'Bandhani Dupatta',
    'Bandhani Kurti',
    'Bandhani Lehenga',
    'Bandhani Suit'
  ],
  'Phulkari': [
    'Phulkari Dupatta',
    'Phulkari Suit',
    'Phulkari Kurti',
    'Phulkari Saree'
  ],
  'Chikankari': [
    'Chikankari Kurti',
    'Chikankari Suit',
    'Chikankari Saree',
    'Chikankari Dupatta',
    'Chikankari Lehenga'
  ],
  'Block Print': [
    'Rajasthani Block Print',
    'Bagru Print',
    'Sanganeri Print',
    'Ajrakh Print',
    'Kalamkari Print'
  ],

  // Fusion Indian Wear
  'Indo-Western': [
    'Indo-Western Gown',
    'Indo-Western Dress',
    'Indo-Western Top',
    'Indo-Western Kurti',
    'Indo-Western Jacket',
    'Indo-Western Skirt',
    'Indo-Western Palazzo',
    'Indo-Western Jumpsuit'
  ]
};

// Get all broad categories
export const getBroadCategories = () => {
  return Object.keys(categoryHierarchy);
};

// Get detailed categories for a specific broad category
export const getDetailedCategories = (broadCategory) => {
  return categoryHierarchy[broadCategory] || [];
};

// Check if a broad category exists
export const isBroadCategory = (category) => {
  return categoryHierarchy.hasOwnProperty(category);
};

// Check if a detailed category exists under a broad category
export const isDetailedCategory = (broadCategory, detailedCategory) => {
  const detailedCategories = categoryHierarchy[broadCategory];
  return detailedCategories && detailedCategories.includes(detailedCategory);
};

// Get broad category for a detailed category (reverse lookup)
export const getBroadCategoryForDetailed = (detailedCategory) => {
  for (const [broadCat, detailedCats] of Object.entries(categoryHierarchy)) {
    if (detailedCats.includes(detailedCategory)) {
      return broadCat;
    }
  }
  return null;
};

// Search categories by query
export const searchCategories = (query) => {
  const results = {
    broad: [],
    detailed: []
  };

  const lowerQuery = query.toLowerCase();

  // Search broad categories
  Object.keys(categoryHierarchy).forEach(broadCat => {
    if (broadCat.toLowerCase().includes(lowerQuery)) {
      results.broad.push(broadCat);
    }
  });

  // Search detailed categories
  Object.entries(categoryHierarchy).forEach(([broadCat, detailedCats]) => {
    detailedCats.forEach(detailedCat => {
      if (detailedCat.toLowerCase().includes(lowerQuery)) {
        results.detailed.push({
          broadCategory: broadCat,
          detailedCategory: detailedCat
        });
      }
    });
  });

  return results;
};

// Validate category selection
export const validateCategorySelection = (broadCategory, detailedCategory) => {
  if (!broadCategory) {
    return { valid: false, error: 'Broad category is required' };
  }

  if (!isBroadCategory(broadCategory)) {
    return { valid: false, error: 'Invalid broad category' };
  }

  if (!detailedCategory) {
    return { valid: false, error: 'Detailed category is required' };
  }

  if (!isDetailedCategory(broadCategory, detailedCategory)) {
    return { valid: false, error: 'Invalid detailed category for the selected broad category' };
  }

  return { valid: true };
};

// Get all categories in a flat structure for migration
export const getAllCategoriesFlat = () => {
  const flatCategories = [];
  Object.entries(categoryHierarchy).forEach(([broadCat, detailedCats]) => {
    flatCategories.push(broadCat);
    flatCategories.push(...detailedCats);
  });
  return [...new Set(flatCategories)]; // Remove duplicates
};
