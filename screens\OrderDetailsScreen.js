import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  Image,
  TextInput,
  Alert,
  Modal,
  KeyboardAvoidingView,
  Platform,
  StatusBar,  // Added for Android status bar height
  Keyboard
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { doc, getDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { updateOrderStatus as updateOrderStatusUtil, markOrderAsShipped, buyerCancelOrder } from '../utils/orderUtils';

const OrderDetailsScreen = ({ navigation, route }) => {
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState(null);
  const [buyerInfo, setBuyerInfo] = useState(null);
  const [isEditingShipping, setIsEditingShipping] = useState(false);
  const [deliveryService, setDeliveryService] = useState('');
  const [trackingNumber, setTrackingNumber] = useState('');
  const [updatingShipping, setUpdatingShipping] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [cancelReason, setCancelReason] = useState('');
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancellingOrder, setCancellingOrder] = useState(false);

  const { orderId } = route.params || {};
  const currentUserId = auth.currentUser?.uid;

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
    } else {
      setLoading(false);
    }
  }, [orderId]);

  const fetchBuyerInfo = async (userId) => {
    try {
      console.log('Fetching buyer info for user ID:', userId);
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        const buyerData = {
          id: userDoc.id,
          name: userData.name || userData.displayName || 'Customer',
          email: userData.email || '',
          phone: userData.phone || userData.address?.phone || '',
          ...userData
        };
        console.log('Buyer info retrieved:', { name: buyerData.name, email: buyerData.email, phone: buyerData.phone });
        setBuyerInfo(buyerData);
      } else {
        console.log('Buyer user document does not exist');
        setBuyerInfo({
          id: userId,
          name: 'Customer',
          email: '',
          phone: ''
        });
      }
    } catch (error) {
      console.error('Error fetching buyer info:', error);
      setBuyerInfo({
        id: userId,
        name: 'Customer',
        email: '',
        phone: ''
      });
    }
  };

  const fetchOrderDetails = async () => {
    try {
      console.log('Fetching order details for order ID:', orderId);
      console.log('Current route name:', route.name);

      const orderRef = doc(db, 'orders', orderId);
      const orderDoc = await getDoc(orderRef);

      if (orderDoc.exists()) {
        const orderData = orderDoc.data();
        console.log('Order data retrieved:', orderData);

        // Check if we're coming from the SellerOrderDetails route
        const isSellerView = route.name === 'SellerOrderDetails';
        console.log('Is seller view:', isSellerView);

        // If not seller view, verify that the current user is the owner of this order
        if (!isSellerView && orderData.userId !== currentUserId) {
          console.log('User is not authorized to view this order');
          console.log('Order user ID:', orderData.userId);
          console.log('Current user ID:', currentUserId);
          navigation.goBack();
          return;
        }

        const orderWithDate = {
          id: orderDoc.id,
          ...orderData,
          createdAt: orderData.createdAt?.toDate() || new Date()
        };

        setOrder(orderWithDate);

        // Fetch buyer information if we have a userId
        if (orderData.userId) {
          await fetchBuyerInfo(orderData.userId);
        }
      } else {
        console.log('Order does not exist');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'processing':
        return '#FF9800';
      case 'shipped':
        return '#2196F3';
      case 'delivered':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const handleEditShipping = () => {
    // Initialize the form with current values
    setDeliveryService(order.deliveryService || '');
    setTrackingNumber(order.trackingNumber || '');
    setValidationError('');
    setIsEditingShipping(true);
  };

  const handleCancelEdit = () => {
    setIsEditingShipping(false);
    setValidationError('');
  };

  const updateShippingDetails = async () => {
    // Validate inputs
    if (!deliveryService.trim()) {
      setValidationError('Delivery service is required');
      return;
    }

    if (!trackingNumber.trim()) {
      setValidationError('Tracking number is required');
      return;
    }

    setUpdatingShipping(true);

    try {
      console.log(`Updating shipping details for order ${orderId}`);
      console.log(`Delivery service: ${deliveryService.trim()}, Tracking number: ${trackingNumber.trim()}`);

      // If the order is already shipped, just update the tracking info
      if (order.orderStatus === 'shipped') {
        console.log(`Order is already shipped, updating tracking info only`);

        // Update just the tracking info without changing the status
        await updateOrderStatusUtil(orderId, 'shipped', {
          deliveryService: deliveryService.trim(),
          trackingNumber: trackingNumber.trim(),
          trackingId: trackingNumber.trim() // Add both field names for compatibility
        });
      } else {
        console.log(`Order is not yet shipped, marking as shipped`);

        // If the order is not yet shipped, mark it as shipped
        await markOrderAsShipped(orderId, deliveryService.trim(), trackingNumber.trim());
      }

      console.log(`Successfully updated shipping details for order ${orderId}`);

      // Update local state
      setOrder(prevOrder => ({
        ...prevOrder,
        orderStatus: 'shipped',
        deliveryService: deliveryService.trim(),
        trackingNumber: trackingNumber.trim(),
        shippedAt: new Date()
      }));

      setIsEditingShipping(false);
      Alert.alert('Success', 'Shipping details updated successfully');
    } catch (error) {
      console.error(`Error updating shipping details for order ${orderId}:`, error);

      // Show the actual error message for better debugging
      Alert.alert('Error', `Failed to update shipping details: ${error.message}`);
    } finally {
      setUpdatingShipping(false);
    }
  };

  const handleCancelOrder = async () => {
    if (!cancelReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for cancellation');
      return;
    }

    setCancellingOrder(true);

    try {
      console.log(`Buyer attempting to cancel order ${orderId} with reason: ${cancelReason.trim()}`);

      // The buyerCancelOrder function now throws errors instead of returning false
      await buyerCancelOrder(orderId, cancelReason.trim());

      // If we get here, the cancellation was successful
      console.log(`Successfully cancelled order ${orderId}`);

      // Update local state
      setOrder(prevOrder => ({
        ...prevOrder,
        orderStatus: 'cancelled',
        cancellationReason: cancelReason.trim(),
        cancelledAt: new Date(),
        cancelledBy: 'buyer'
      }));

      setShowCancelModal(false);
      Alert.alert('Success', 'Order has been cancelled successfully');
    } catch (error) {
      console.error(`Error cancelling order ${orderId}:`, error);

      // Show a more specific error message if available
      if (error.message.includes('Cannot cancel order in')) {
        Alert.alert('Error', 'This order cannot be cancelled because it has already been shipped or delivered');
      } else {
        // Show the actual error message for better debugging
        Alert.alert('Error', `Failed to cancel order: ${error.message}`);
      }
    } finally {
      setCancellingOrder(false);
    }
  };

  const renderOrderItems = () => {
    if (!order || !order.items || order.items.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No items in this order</Text>
        </View>
      );
    }

    return order.items.map((item, index) => {
      const quantity = item.quantity || 1;
      const price = item.price || 0;
      const subtotal = price * quantity;

      return (
        <TouchableOpacity
          key={item.id || index}
          style={styles.orderItem}
          onPress={() => navigation.navigate('ItemDetails', { itemId: item.itemId })}
        >
          <View style={styles.itemImageContainer}>
            <Image
              source={{ uri: item.imageUrl }}
              style={styles.itemImage}
              resizeMode="cover"
            />
          </View>
          <View style={styles.itemDetails}>
            <View style={styles.itemMainInfo}>
              <View style={styles.itemTitleContainer}>
                <Text style={styles.itemTitle}>{item.title || 'Untitled Item'}</Text>
                {item.brand && <Text style={styles.itemBrand}>{item.brand}</Text>}
                {item.gender && <Text style={styles.itemGender}>{item.gender}</Text>}
              </View>
              <View style={styles.itemPriceInfo}>
                <Text style={styles.itemPrice}>₹{Math.floor(price)}</Text>
                <Text style={styles.itemQuantity}>Qty: {quantity}</Text>
              </View>
            </View>
            <View style={styles.subtotalRow}>
              <Text style={styles.itemSubtotal}>Subtotal: ₹{Math.floor(subtotal)}</Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    });
  };

  const renderBuyerInfo = () => {
    const isSellerView = route.name === 'SellerOrderDetails';
    const isBuyerView = !isSellerView;

    if (!buyerInfo) {
      return (
        <View style={styles.buyerInfoContainer}>
          <View style={styles.buyerInfoRow}>
            <Text style={styles.buyerInfoLabel}>Loading buyer information...</Text>
            <ActivityIndicator size="small" color="#666" />
          </View>
        </View>
      );
    }

    return (
      <View style={styles.buyerInfoContainer}>
        <View style={styles.buyerInfoRow}>
          <Text style={styles.buyerInfoLabel}>
            {isBuyerView ? 'Your Name:' : 'Buyer Name:'}
          </Text>
          <Text style={styles.buyerInfoValue}>{buyerInfo.name || 'Not provided'}</Text>
        </View>

        {buyerInfo.phone && (
          <View style={styles.buyerInfoRow}>
            <Text style={styles.buyerInfoLabel}>
              {isBuyerView ? 'Your Phone:' : 'Buyer Phone:'}
            </Text>
            <Text style={styles.buyerInfoValue}>{buyerInfo.phone}</Text>
          </View>
        )}

        {buyerInfo.email && (
          <View style={styles.buyerInfoRow}>
            <Text style={styles.buyerInfoLabel}>
              {isBuyerView ? 'Your Email:' : 'Buyer Email:'}
            </Text>
            <Text style={styles.buyerInfoValue}>{buyerInfo.email}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderShippingAddress = () => {
    if (!order || !order.shippingAddress) return null;

    const address = order.shippingAddress;

    return (
      <View style={styles.addressContainer}>
        <Text style={styles.addressText}>{address.street}</Text>
        <Text style={styles.addressText}>{address.city}, {address.state} {address.zipCode}</Text>
        <Text style={styles.addressText}>{address.country}</Text>
        <Text style={styles.addressText}>Phone: {address.phone}</Text>
      </View>
    );
  };

  const renderOrderStatus = () => {
    if (!order) return null;

    const statusColor = getStatusColor(order.orderStatus);
    const isSellerView = route.name === 'SellerOrderDetails';
    const isBuyerView = !isSellerView;
    const canBeCancelled = isBuyerView && order.orderStatus === 'processing';

    return (
      <View style={styles.statusContainer}>
        <View style={styles.statusHeader}>
          <Text style={styles.statusTitle}>Order Status</Text>
          <Text style={[styles.statusValue, { color: statusColor }]}>
            {order.orderStatus || 'Processing'}
          </Text>
        </View>

        {order.orderStatus === 'cancelled' ? (
          <View style={styles.cancellationInfo}>
            <Text style={styles.cancellationTitle}>Order Cancelled</Text>
            {order.cancellationReason && (
              <Text style={styles.cancellationReason}>
                Reason: {order.cancellationReason}
              </Text>
            )}
            {order.cancelledAt && (
              <Text style={styles.cancellationDate}>
                Cancelled on: {formatDate(order.cancelledAt instanceof Date ? order.cancelledAt : order.cancelledAt.toDate())}
              </Text>
            )}
            {order.cancelledBy && (
              <Text style={styles.cancelledBy}>
                Cancelled by: {order.cancelledBy === 'buyer' ? 'You' : 'Seller'}
              </Text>
            )}
          </View>
        ) : (
          <>
            <View style={styles.statusTimeline}>
              <View style={[
                styles.statusStep,
                { backgroundColor: order.orderStatus ? statusColor : '#ccc' }
              ]}>
                <Ionicons name="checkmark" size={16} color="#fff" />
              </View>
              <View style={[
                styles.statusLine,
                {
                  backgroundColor: order.orderStatus === 'shipped' ||
                    order.orderStatus === 'delivered' ? statusColor : '#ccc'
                }
              ]} />
              <View style={[
                styles.statusStep,
                {
                  backgroundColor: order.orderStatus === 'shipped' ||
                    order.orderStatus === 'delivered' ? statusColor : '#ccc'
                }
              ]}>
                {(order.orderStatus === 'shipped' || order.orderStatus === 'delivered') ? (
                  <Ionicons name="checkmark" size={16} color="#fff" />
                ) : (
                  <Text style={styles.statusStepText}>2</Text>
                )}
              </View>
              <View style={[
                styles.statusLine,
                { backgroundColor: order.orderStatus === 'delivered' ? statusColor : '#ccc' }
              ]} />
              <View style={[
                styles.statusStep,
                { backgroundColor: order.orderStatus === 'delivered' ? statusColor : '#ccc' }
              ]}>
                {order.orderStatus === 'delivered' ? (
                  <Ionicons name="checkmark" size={16} color="#fff" />
                ) : (
                  <Text style={styles.statusStepText}>3</Text>
                )}
              </View>
            </View>

            <View style={styles.statusLabels}>
              <Text style={styles.statusLabel}>Ordered</Text>
              <Text style={styles.statusLabel}>Shipped</Text>
              <Text style={styles.statusLabel}>Delivered</Text>
            </View>

            {canBeCancelled && (
              <TouchableOpacity
                style={styles.cancelOrderButton}
                onPress={() => setShowCancelModal(true)}
              >
                <Text style={styles.cancelOrderButtonText}>Cancel Order</Text>
              </TouchableOpacity>
            )}
          </>
        )}

        {/* Shipping information section */}
        {(order.orderStatus === 'shipped' || order.orderStatus === 'delivered') && (
          <View style={styles.shippingInfoContainer}>
            <Text style={styles.shippingInfoTitle}>Shipping Information</Text>

            {isEditingShipping ? (
              // Edit mode
              <View style={styles.editShippingForm}>
                <Text style={styles.inputLabel}>Delivery Service <Text style={styles.requiredStar}>*</Text></Text>
                <TextInput
                  style={[styles.input, !deliveryService.trim() && validationError.includes('Delivery service') ? styles.inputError : null]}
                  value={deliveryService}
                  onChangeText={setDeliveryService}
                  placeholder="Enter delivery service (e.g., FedEx, DHL)"
                  placeholderTextColor="#000"
                />

                <Text style={styles.inputLabel}>Tracking Number <Text style={styles.requiredStar}>*</Text></Text>
                <TextInput
                  style={[styles.input, !trackingNumber.trim() && validationError.includes('Tracking number') ? styles.inputError : null]}
                  value={trackingNumber}
                  onChangeText={setTrackingNumber}
                  placeholder="Enter tracking number"
                  placeholderTextColor="#000"
                />

                {validationError ? (
                  <Text style={styles.errorText}>{validationError}</Text>
                ) : null}

                <View style={styles.editButtonsRow}>
                  {updatingShipping ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="small" color="#2196F3" />
                      <Text style={styles.loadingText}>Updating...</Text>
                    </View>
                  ) : (
                    <>
                      <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={handleCancelEdit}
                      >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.saveButton}
                        onPress={updateShippingDetails}
                      >
                        <Text style={styles.saveButtonText}>Save Changes</Text>
                      </TouchableOpacity>
                    </>
                  )}
                </View>
              </View>
            ) : (
              // View mode
              <>
                <View style={styles.shippingInfoRow}>
                  <Text style={styles.shippingInfoLabel}>Delivery Service:</Text>
                  <Text style={styles.shippingInfoValue}>{order.deliveryService || 'Not specified'}</Text>
                </View>

                <View style={styles.shippingInfoRow}>
                  <Text style={styles.shippingInfoLabel}>Tracking Number:</Text>
                  <Text style={styles.shippingInfoValue}>{order.trackingNumber || 'Not specified'}</Text>
                </View>

                {order.shippedAt && (
                  <View style={styles.shippingInfoRow}>
                    <Text style={styles.shippingInfoLabel}>Shipped Date:</Text>
                    <Text style={styles.shippingInfoValue}>
                      {formatDate(order.shippedAt instanceof Date ? order.shippedAt : order.shippedAt.toDate())}
                    </Text>
                  </View>
                )}

                {isSellerView && (
                  <TouchableOpacity
                    style={styles.editShippingButton}
                    onPress={handleEditShipping}
                  >
                    <Text style={styles.editShippingButtonText}>Edit Shipping Details</Text>
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
        )}
      </View>
    );
  };

  const renderCancelOrderModal = () => {
    return (
      <Modal
        visible={showCancelModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCancelModal(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1 }}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={Keyboard.dismiss}
          >
            <View style={styles.modalContainer}>
              <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                keyboardShouldPersistTaps="handled"
              >
                <View style={styles.modalContent}>
                  <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>Cancel Order</Text>
                    <TouchableOpacity
                      onPress={() => setShowCancelModal(false)}
                      style={styles.closeButton}
                    >
                      <Ionicons name="close" size={24} color="#333" />
                    </TouchableOpacity>
                  </View>

                  <Text style={styles.modalLabel}>Reason for Cancellation <Text style={styles.requiredStar}>*</Text></Text>
                  <TextInput
                    style={styles.input}
                    value={cancelReason}
                    onChangeText={setCancelReason}
                    placeholder="Please provide a reason for cancellation"
                    multiline={true}
                    numberOfLines={3}
                    textAlignVertical="top"
                    placeholderTextColor="#000"
                  />

                  <Text style={styles.helperText}>
                    Note: Orders can only be cancelled if they haven't been shipped yet.
                  </Text>

                  {cancellingOrder ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="large" color="#F44336" />
                      <Text style={styles.loadingText}>Cancelling order...</Text>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.cancelOrderConfirmButton}
                      onPress={handleCancelOrder}
                    >
                      <Text style={styles.cancelOrderConfirmButtonText}>Confirm Cancellation</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </ScrollView>
            </View>
          </TouchableOpacity>
        </KeyboardAvoidingView>
      </Modal>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Order Details</Text>
          <View style={styles.rightPlaceholder} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B6B" />
            <Text style={styles.loadingText}>Loading order details...</Text>
          </View>
        ) : (
          <ScrollView style={styles.content}>
            <View style={styles.orderHeader}>
              <View style={styles.orderInfoColumn}>
                <Text style={styles.orderIdLabel}>Order ID</Text>
                <Text style={styles.orderId}>{orderId}</Text>
              </View>
              <View style={styles.orderInfoColumn}>
                <Text style={styles.orderDateLabel}>Order Date</Text>
                <Text style={styles.orderDate}>{formatDate(order?.createdAt)}</Text>
              </View>
            </View>

            {renderOrderStatus()}

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Buyer Information</Text>
              {renderBuyerInfo()}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Items</Text>
              {renderOrderItems()}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Shipping Address</Text>
              {renderShippingAddress()}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Payment Details</Text>
              <View style={styles.paymentContainer}>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Payment Method</Text>
                  <Text style={styles.paymentValue} numberOfLines={1}>Razorpay</Text>
                </View>
                <View style={styles.paymentIdRow}>
                  <Text style={styles.paymentLabel}>Payment ID</Text>
                  <View style={styles.paymentIdContainer}>
                    <Text style={styles.paymentIdValue} numberOfLines={3} ellipsizeMode="tail">
                      {order?.paymentId || 'N/A'}
                    </Text>
                  </View>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Payment Status</Text>
                  <Text
                    style={[
                      styles.paymentValue,
                      { color: order?.paymentStatus === 'completed' ? '#4CAF50' : '#FF9800' }
                    ]}
                    numberOfLines={1}
                  >
                    {order?.paymentStatus || 'Pending'}
                  </Text>
                </View>
              </View>

              <View style={styles.totalContainer}>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Subtotal</Text>
                  <Text style={styles.paymentValue} numberOfLines={1}>
                    ₹{order && (order.totalAmount || order.amount) ? ((order.totalAmount || order.amount) / 1.05).toFixed(2) : '0'}
                  </Text>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Shipping</Text>
                  <Text style={styles.paymentValue} numberOfLines={1}>₹0</Text>
                </View>
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Tax (5%)</Text>
                  <Text style={styles.paymentValue} numberOfLines={1}>
                    ₹{order && (order.totalAmount || order.amount) ? ((order.totalAmount || order.amount) - ((order.totalAmount || order.amount) / 1.05)).toFixed(2) : '0'}
                  </Text>
                </View>
                <View style={[styles.paymentRow, styles.totalRow]}>
                  <Text style={styles.totalLabel}>Total</Text>
                  <Text style={styles.totalValue} numberOfLines={1}>
                    ₹{order && (order.totalAmount || order.amount) ? Math.floor(order.totalAmount || order.amount) : '0'}
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        )}

        {renderCancelOrderModal()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0, // Ensure header is visible below status bar on Android
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.06)',
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  rightPlaceholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  orderInfoColumn: {
    flex: 1,
    paddingHorizontal: 8,
    maxWidth: '48%',
  },
  orderIdLabel: {
    fontSize: 13,
    color: '#666',
    marginBottom: 8,
    letterSpacing: 0.2,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  orderId: {
    fontSize: 15,
    color: '#333',
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  orderDateLabel: {
    fontSize: 13,
    color: '#666',
    marginBottom: 8,
    letterSpacing: 0.2,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  orderDate: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  statusContainer: {
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.3,
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '700',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  statusTimeline: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  statusStep: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  statusStepText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: 12,
  },
  statusLine: {
    flex: 1,
    height: 3,
    backgroundColor: '#ccc',
  },
  statusLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    width: '33%',
    fontWeight: '500',
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  orderItem: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
    height: 100,
  },
  itemImageContainer: {
    width: 100,
    height: 100,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  itemImage: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  itemDetails: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  itemMainInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  itemTitleContainer: {
    flex: 1,
    paddingRight: 8,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  itemBrand: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    fontWeight: '500',
  },
  itemGender: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  itemPriceInfo: {
    alignItems: 'flex-end',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4CAF50',
    marginBottom: 4,
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    backgroundColor: 'rgba(0,0,0,0.03)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  subtotalRow: {
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  itemSubtotal: {
    fontSize: 15,
    fontWeight: '600',
    color: '#4CAF50',
  },
  buyerInfoContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  buyerInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
    paddingVertical: 4,
  },
  buyerInfoLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    width: '35%',
    lineHeight: 20,
  },
  buyerInfoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
    lineHeight: 20,
  },
  addressContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  addressText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 6,
    lineHeight: 20,
  },
  paymentContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  totalContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  paymentIdRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  paymentLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    width: '35%',
  },
  paymentValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    textAlign: 'right',
    flex: 1,
  },
  paymentIdContainer: {
    flex: 1,
    alignItems: 'flex-end',
    maxWidth: '65%',
  },
  paymentIdValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    textAlign: 'right',
    flexShrink: 1,
  },
  totalRow: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#4CAF50',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
    borderStyle: 'dashed',
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    fontWeight: '500',
  },
  shippingInfoContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  shippingInfoTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
    letterSpacing: 0.2,
  },
  shippingInfoRow: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  shippingInfoLabel: {
    fontSize: 14,
    color: '#666',
    width: 140,
    fontWeight: '500',
  },
  shippingInfoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    flex: 1,
  },
  editShippingButton: {
    marginTop: 12,
    backgroundColor: '#2196F3',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  editShippingButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  editShippingForm: {
    marginTop: 12,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  inputLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 6,
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  inputError: {
    borderColor: '#FF6B6B',
    borderWidth: 2,
  },
  requiredStar: {
    color: '#FF6B6B',
    fontWeight: 'bold',
  },
  errorText: {
    color: '#FF6B6B',
    marginBottom: 16,
    fontSize: 14,
    fontWeight: '500',
  },
  editButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.08)',
    flex: 1,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.2,
  },

  // Cancel order button styles
  cancelOrderButton: {
    backgroundColor: '#F44336',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 16,
    alignSelf: 'center'
  },
  cancelOrderButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16
  },

  // Cancel order modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden'
  },
  scrollViewContent: {
    flexGrow: 1
  },
  modalContent: {
    padding: 20
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333'
  },
  modalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8
  },
  cancelOrderConfirmButton: {
    backgroundColor: '#F44336',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16
  },
  cancelOrderConfirmButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16
  },

  // Cancellation info styles
  cancellationInfo: {
    backgroundColor: '#FFEBEE',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#F44336'
  },
  cancellationTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#D32F2F',
    marginBottom: 8
  },
  cancellationReason: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8
  },
  cancellationDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4
  },
  cancelledBy: {
    fontSize: 14,
    color: '#666'
  },
});

export default OrderDetailsScreen;
