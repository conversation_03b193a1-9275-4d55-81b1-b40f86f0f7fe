import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { jest } from '@jest/globals';

// Mock Firebase
jest.mock('../firebase.config', () => ({
  db: {},
  auth: {
    currentUser: { uid: 'test-user-id' }
  }
}));

// Mock hooks
jest.mock('../hooks/useOptimizedSwipeActions', () => ({
  useOptimizedSwipeActions: jest.fn(() => ({
    processSwipeCompletion: jest.fn()
  }))
}));

jest.mock('../hooks/useSwipeAnimations', () => ({
  useSwipeAnimations: jest.fn(() => ({
    likeAnimationOpacity: { current: { setValue: jest.fn() } },
    dislikeAnimationOpacity: { current: { setValue: jest.fn() } },
    cartAnimationOpacity: { current: { setValue: jest.fn() } },
    likeAnimationScale: { current: { setValue: jest.fn() } },
    dislikeAnimationScale: { current: { setValue: jest.fn() } },
    cartAnimationScale: { current: { setValue: jest.fn() } },
    triggerLikeAnimation: jest.fn(),
    triggerDislikeAnimation: jest.fn(),
    triggerCartAnimation: jest.fn()
  }))
}));

jest.mock('../hooks/useClothingFeed', () => ({
  useClothingFeed: jest.fn(() => ({
    items: [
      {
        id: 'test-item-1',
        title: 'Test Item 1',
        imageUrl: 'https://example.com/image1.jpg',
        price: 29.99,
        brand: 'Test Brand'
      }
    ],
    loading: false,
    currentIndex: 0,
    currentUserId: 'test-user-id',
    cartItems: [],
    addToCart: jest.fn(),
    setInteractedItemIds: jest.fn(),
    fetchClothingItems: jest.fn()
  }))
}));

import ClothingFeedScreen from '../screens/ClothingFeedScreen';

describe('Swipe Performance Optimizations', () => {
  let mockProcessSwipeCompletion;
  let mockTriggerCartAnimation;
  let mockAddToCart;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get mocked functions
    const { useOptimizedSwipeActions } = require('../hooks/useOptimizedSwipeActions');
    const { useSwipeAnimations } = require('../hooks/useSwipeAnimations');
    const { useClothingFeed } = require('../hooks/useClothingFeed');

    mockProcessSwipeCompletion = jest.fn();
    mockTriggerCartAnimation = jest.fn();
    mockAddToCart = jest.fn();

    useOptimizedSwipeActions.mockReturnValue({
      processSwipeCompletion: mockProcessSwipeCompletion
    });

    useSwipeAnimations.mockReturnValue({
      likeAnimationOpacity: { current: { setValue: jest.fn() } },
      dislikeAnimationOpacity: { current: { setValue: jest.fn() } },
      cartAnimationOpacity: { current: { setValue: jest.fn() } },
      likeAnimationScale: { current: { setValue: jest.fn() } },
      dislikeAnimationScale: { current: { setValue: jest.fn() } },
      cartAnimationScale: { current: { setValue: jest.fn() } },
      triggerLikeAnimation: jest.fn(),
      triggerDislikeAnimation: jest.fn(),
      triggerCartAnimation: mockTriggerCartAnimation
    });

    useClothingFeed.mockReturnValue({
      items: [
        {
          id: 'test-item-1',
          title: 'Test Item 1',
          imageUrl: 'https://example.com/image1.jpg',
          price: 29.99,
          brand: 'Test Brand'
        }
      ],
      loading: false,
      currentIndex: 0,
      currentUserId: 'test-user-id',
      cartItems: [],
      addToCart: mockAddToCart,
      setInteractedItemIds: jest.fn(),
      fetchClothingItems: jest.fn(),
      activeCategory: 'all',
      noMoreItems: false,
      filteredCategories: [],
      searchQuery: '',
      searchQueryHeader: '',
      searchModalVisible: false,
      showCollectionModal: false,
      isLoadingMoreCategories: false,
      currentUserPhotoURL: null,
      isSearchMode: false,
      suggestedCategory: null,
      setActiveCategory: jest.fn(),
      setCurrentIndex: jest.fn(),
      setSearchQuery: jest.fn(),
      setSearchQueryHeader: jest.fn(),
      setSearchModalVisible: jest.fn(),
      setShowCollectionModal: jest.fn(),
      searchItems: jest.fn(),
      clearSearch: jest.fn(),
      currentUserId: 'test-user-id'
    });
  });

  test('should initialize optimized swipe actions with correct parameters', () => {
    const mockNavigation = { navigate: jest.fn() };
    
    render(<ClothingFeedScreen navigation={mockNavigation} />);

    const { useOptimizedSwipeActions } = require('../hooks/useOptimizedSwipeActions');
    
    expect(useOptimizedSwipeActions).toHaveBeenCalledWith({
      currentUserId: 'test-user-id',
      setInteractedItemIds: expect.any(Function),
      addToCart: expect.any(Function),
      triggerCartAnimation: expect.any(Function),
      triggerLikeAnimation: expect.any(Function),
      triggerDislikeAnimation: expect.any(Function)
    });
  });

  test('should pass optimized swipe completion handler to CardStack', () => {
    const mockNavigation = { navigate: jest.fn() };
    
    const { getByTestId } = render(<ClothingFeedScreen navigation={mockNavigation} />);
    
    // The CardStack should receive the optimized processSwipeCompletion function
    expect(mockProcessSwipeCompletion).toBeDefined();
  });

  test('performance: cart animation should be triggered immediately on upward swipe', async () => {
    // This test verifies that the cart animation is triggered immediately
    // when an upward swipe is detected, not after database operations complete
    
    const testItem = {
      id: 'test-item-1',
      title: 'Test Item 1',
      price: 29.99
    };

    // Simulate upward swipe completion
    mockProcessSwipeCompletion('up', testItem);

    // Verify that the animation trigger and cart addition happen immediately
    expect(mockProcessSwipeCompletion).toHaveBeenCalledWith('up', testItem);
  });

  test('performance: local state should be updated immediately for all swipe types', () => {
    const testItem = {
      id: 'test-item-1',
      title: 'Test Item 1',
      price: 29.99
    };

    // Test all swipe directions
    ['up', 'right', 'left'].forEach(direction => {
      mockProcessSwipeCompletion(direction, testItem);
      expect(mockProcessSwipeCompletion).toHaveBeenCalledWith(direction, testItem);
    });
  });
});

describe('Performance Benchmarks', () => {
  test('should complete swipe processing within performance threshold', async () => {
    const { useOptimizedSwipeActions } = require('../hooks/useOptimizedSwipeActions');
    
    const mockSetInteractedItemIds = jest.fn();
    const mockAddToCart = jest.fn();
    const mockTriggerCartAnimation = jest.fn();

    const { processSwipeCompletion } = useOptimizedSwipeActions({
      currentUserId: 'test-user-id',
      setInteractedItemIds: mockSetInteractedItemIds,
      addToCart: mockAddToCart,
      triggerCartAnimation: mockTriggerCartAnimation,
      triggerLikeAnimation: jest.fn(),
      triggerDislikeAnimation: jest.fn()
    });

    const testItem = {
      id: 'test-item-1',
      title: 'Test Item 1',
      price: 29.99
    };

    const startTime = performance.now();
    
    // Process upward swipe (cart action)
    await processSwipeCompletion('up', testItem);
    
    const endTime = performance.now();
    const processingTime = endTime - startTime;

    // The immediate UI feedback should complete within 50ms
    // (Database operations run in background and don't block UI)
    expect(processingTime).toBeLessThan(50);
    
    // Verify immediate actions were called
    expect(mockSetInteractedItemIds).toHaveBeenCalled();
    expect(mockAddToCart).toHaveBeenCalledWith(testItem);
  });
});
