import React, { useState, useEffect } from 'react';
import { SafeAreaView, FlatList, View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, Image } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { db, auth } from '../firebase.config';
import { collection, query, where, orderBy, onSnapshot, doc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import { Ionicons } from '@expo/vector-icons';

// Indicator dot showing online (green) or offline (grey)
const OnlineIndicator = ({ userId }) => {
  const [isOnline, setIsOnline] = useState(false);
  useEffect(() => {
    const unsub = onSnapshot(doc(db, 'users', userId), snapshot => {
      const data = snapshot.data() || {};
      setIsOnline(!!data.online);
    });
    return unsub;
  }, [userId]);
  return (
    <Ionicons
      name="ellipse"
      size={12}
      color={isOnline ? 'green' : 'grey'}
      style={styles.onlineDot}
    />
  );
};

// Main Messages screen listing chat threads
export const MessagesScreen = ({ navigation }) => {
  const [userId, setUserId] = useState(null);
  const [threads, setThreads] = useState([]);
  const [loading, setLoading] = useState(true);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    // Set userId from auth
    if (auth.currentUser) {
      setUserId(auth.currentUser.uid);
    } else {
      setLoading(false);
      return;
    }

    // Only proceed once we have userId
    const fetchThreads = async () => {
      const threadsRef = collection(db, 'threads');
      const q = query(
        threadsRef,
        where('participants', 'array-contains', auth.currentUser.uid),
        orderBy('updatedAt', 'desc')
      );
      const unsubscribe = onSnapshot(q, async snapshot => {
        const detailed = await Promise.all(snapshot.docs.map(async docSnap => {
          const data = docSnap.data();
          const otherId = data.participants.find(id => id !== auth.currentUser.uid);
          // Fetch other user's profile
          let otherName = 'User';
          let otherImage = 'https://via.placeholder.com/50';
          try {
            const userDoc = await getDoc(doc(db, 'users', otherId));
            const udata = userDoc.data() || {};
            otherName = udata.name || otherName;
            otherImage = udata.profilePictureUrl || otherImage;
          } catch (e) {
            console.error('Error fetching other user profile:', e);
          }
          return {
            id: docSnap.id,
            ...data,
            otherUserName: otherName,
            otherUserImage: otherImage
          };
        }));
        setThreads(detailed);
        setLoading(false);
      });
      return unsubscribe;
    };
    fetchThreads();
  }, []); // Empty array: auth.currentUser.uid is stable once mounted

  const renderItem = ({ item }) => {
    const otherId = item.participants.find(id => id !== userId);
    const otherUserName = item.otherUserName;
    const otherUserImage = item.otherUserImage;
    const lastMessage = item.lastMessage || '';
    const isUnread = !(item.readBy || []).includes(userId);

    return (
      <TouchableOpacity
        style={styles.threadRow}
        onPress={async () => {
          navigation.navigate('ChatThread', { threadId: item.id, otherUserId: otherId });
          // Mark as read in Firestore if unread
          if (isUnread) {
            const threadRef = doc(db, 'threads', item.id);
            await updateDoc(threadRef, {
              readBy: arrayUnion(userId)
            });
          }
        }}
      >
        <TouchableOpacity onPress={() => navigation.navigate('UserProfile', { userId: otherId })}>
          <Image source={{ uri: otherUserImage }} style={styles.avatar} />
        </TouchableOpacity>
        <View style={styles.textContainer}>
          <Text style={styles.threadText}>{otherUserName}</Text>
          <Text
            style={[
              styles.lastMessage,
              isUnread ? styles.lastMessageUnread : styles.lastMessageRead
            ]}
            numberOfLines={1}
          >
            {lastMessage}
          </Text>
        </View>
        <OnlineIndicator userId={otherId} />
      </TouchableOpacity>
    );
  };

  if (loading) {
    return <ActivityIndicator style={styles.loader} size="large" color="#FF6B6B" />;
  }

  return (
    <SafeAreaView style={[styles.safeArea, { paddingTop: insets.top }]}>
      <View style={styles.container}>
        <FlatList
          data={threads}
          keyExtractor={item => item.id}
          renderItem={renderItem}
          ListEmptyComponent={<Text style={styles.empty}>No conversations yet.</Text>}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  loader: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  empty: { textAlign: 'center', marginTop: 20, color: '#666' },
  threadRow: { flexDirection: 'row', alignItems: 'center', padding: 15, borderBottomWidth: 1, borderColor: '#eee' },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
    backgroundColor: '#eee', // Placeholder background
  },
  textContainer: {
    flex: 1, // Take remaining space
    justifyContent: 'center',
  },
  threadText: { 
    fontSize: 16, 
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2, // Add space if showing last message
  },
  lastMessage: {
    fontSize: 14,
    marginTop: 2,
  },
  lastMessageUnread: {
    color: '#111',
    fontWeight: 'bold',
  },
  lastMessageRead: {
    color: '#888',
    fontWeight: 'normal',
  },
  onlineDot: { marginLeft: 'auto' } // Push dot to the right
});
