import { StyleSheet, PixelRatio } from 'react-native';
import { SCREEN_WIDTH, CARD_WIDTH_PERCENTAGE, CARD_HEIGHT } from '../utils/feedConstants';

export const styles = StyleSheet.create({
  cardAreaContainer: {
    width: SCREEN_WIDTH * CARD_WIDTH_PERCENTAGE,
    height: CARD_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    backgroundColor: '#fff',
    borderWidth: 0,
  },
  card: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  statusContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 15,
  },
  loadingText: {
    fontSize: PixelRatio.getFontScale() * 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
  },
  quoteText: {
    fontSize: PixelRatio.getFontScale() * 14,
    color: '#888',
    textAlign: 'center',
    marginTop: 15,
    marginHorizontal: 20,
    lineHeight: 20,
    fontStyle: 'italic',
    minHeight: 40,
  },
  errorText: {
    fontSize: PixelRatio.getFontScale() * 16,
    color: 'red',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 15,
  },
  emptyText: {
    fontSize: PixelRatio.getFontScale() * 18,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
  emptySubText: {
    fontSize: PixelRatio.getFontScale() * 14,
    color: '#aaa',
    textAlign: 'center',
    marginTop: 5,
    marginBottom: 20,
  },
  refreshButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  refreshButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: PixelRatio.getFontScale() * 16,
  },
  emptyButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  animationsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  animationContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  animationText: {
    fontSize: 80,
    textAlign: 'center',
  },
});
