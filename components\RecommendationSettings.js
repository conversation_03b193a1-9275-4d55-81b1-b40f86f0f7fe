import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const RecommendationSettings = ({
  useRecommendations,
  recommendationThreshold,
  onUpdateSettings,
  style
}) => {
  const [showThresholdModal, setShowThresholdModal] = useState(false);
  const [tempThreshold, setTempThreshold] = useState(recommendationThreshold);

  const handleToggleRecommendations = (value) => {
    onUpdateSettings(value, undefined);

    if (value) {
      Alert.alert(
        'Recommendations Enabled',
        'The app will now show you clothing items based on your preferences and past likes.',
        [{ text: 'Got it', style: 'default' }]
      );
    }
  };

  const handleThresholdChange = () => {
    onUpdateSettings(undefined, tempThreshold);
    setShowThresholdModal(false);

    const percentage = Math.round((tempThreshold || 0.3) * 100);
    const description = tempThreshold < 0.3
      ? 'You\'ll see more varied recommendations.'
      : tempThreshold > 0.5
      ? 'You\'ll see more focused recommendations.'
      : 'Balanced recommendation filtering.';

    Alert.alert(
      'Threshold Updated',
      `Recommendation sensitivity set to ${percentage}%. ${description}`,
      [{ text: 'Got it', style: 'default' }]
    );
  };

  const getThresholdDescription = (threshold) => {
    if (threshold < 0.2) return 'Very Loose - Show almost all items';
    if (threshold < 0.3) return 'Loose - Show varied recommendations';
    if (threshold < 0.5) return 'Balanced - Good mix of preferences';
    if (threshold < 0.7) return 'Focused - Strong preference matching';
    return 'Very Focused - Only very similar items';
  };

  const getThresholdColor = (threshold) => {
    if (threshold < 0.3) return '#4CAF50'; // Green for loose
    if (threshold < 0.5) return '#FF9800'; // Orange for balanced
    return '#FF6B6B'; // Red for focused
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Ionicons name="sparkles" size={24} color="#FF6B6B" />
        <Text style={styles.title}>Smart Recommendations</Text>
      </View>

      <Text style={styles.description}>
        Get personalized clothing recommendations based on your likes and preferences
      </Text>

      {/* Toggle Switch */}
      <View style={styles.settingRow}>
        <View style={styles.settingInfo}>
          <Text style={styles.settingLabel}>Enable Recommendations</Text>
          <Text style={styles.settingSubtext}>
            {useRecommendations
              ? 'Showing personalized items'
              : 'Showing random items'
            }
          </Text>
        </View>
        <Switch
          value={useRecommendations}
          onValueChange={handleToggleRecommendations}
          trackColor={{ false: '#E0E0E0', true: '#FFB3B3' }}
          thumbColor={useRecommendations ? '#FF6B6B' : '#BDBDBD'}
          ios_backgroundColor="#E0E0E0"
        />
      </View>

      {/* Threshold Setting */}
      {useRecommendations && (
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Recommendation Sensitivity</Text>
            <Text style={[
              styles.settingSubtext,
              { color: getThresholdColor(recommendationThreshold) }
            ]}>
              {getThresholdDescription(recommendationThreshold)}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.thresholdButton}
            onPress={() => {
              setTempThreshold(recommendationThreshold);
              setShowThresholdModal(true);
            }}
          >
            <Text style={styles.thresholdButtonText}>
              {Math.round((recommendationThreshold || 0.3) * 100)}%
            </Text>
            <Ionicons name="chevron-forward" size={16} color="#666" />
          </TouchableOpacity>
        </View>
      )}

      {/* Info Section */}
      <View style={styles.infoSection}>
        <Ionicons name="information-circle-outline" size={20} color="#666" />
        <Text style={styles.infoText}>
          Recommendations improve as you interact with more items. Like items you enjoy to get better suggestions!
        </Text>
      </View>

      {/* Threshold Modal */}
      <Modal
        visible={showThresholdModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowThresholdModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Recommendation Sensitivity</Text>
              <TouchableOpacity
                onPress={() => setShowThresholdModal(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.sliderContainer}>
              <Text style={styles.sliderLabel}>
                Current: {Math.round((tempThreshold || 0.3) * 100)}%
              </Text>
              <Text style={[
                styles.sliderDescription,
                { color: getThresholdColor(tempThreshold) }
              ]}>
                {getThresholdDescription(tempThreshold)}
              </Text>

              {/* Simple threshold selector buttons */}
              <View style={styles.thresholdOptions}>
                {[
                  { value: 0.2, label: 'Loose' },
                  { value: 0.3, label: 'Balanced' },
                  { value: 0.5, label: 'Focused' },
                  { value: 0.7, label: 'Strict' }
                ].map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.thresholdOption,
                      Math.abs(tempThreshold - option.value) < 0.1 && styles.selectedThresholdOption
                    ]}
                    onPress={() => setTempThreshold(option.value)}
                  >
                    <Text style={[
                      styles.thresholdOptionText,
                      Math.abs(tempThreshold - option.value) < 0.1 && styles.selectedThresholdOptionText
                    ]}>
                      {option.label}
                    </Text>
                    <Text style={[
                      styles.thresholdOptionValue,
                      Math.abs(tempThreshold - option.value) < 0.1 && styles.selectedThresholdOptionValue
                    ]}>
                      {Math.round((option.value || 0.3) * 100)}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.sliderLabels}>
                <Text style={styles.sliderLabelText}>More Variety</Text>
                <Text style={styles.sliderLabelText}>More Focused</Text>
              </View>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowThresholdModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleThresholdChange}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 15,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  settingSubtext: {
    fontSize: 14,
    color: '#666',
  },
  thresholdButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  thresholdButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginRight: 5,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 15,
    padding: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  infoText: {
    fontSize: 13,
    color: '#666',
    marginLeft: 10,
    flex: 1,
    lineHeight: 18,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  sliderContainer: {
    marginBottom: 30,
  },
  sliderLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  sliderDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '500',
  },
  thresholdOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 20,
  },
  thresholdOption: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#F8F9FA',
  },
  selectedThresholdOption: {
    borderColor: '#FF6B6B',
    backgroundColor: '#FFF5F5',
  },
  thresholdOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 2,
  },
  selectedThresholdOptionText: {
    color: '#FF6B6B',
  },
  thresholdOptionValue: {
    fontSize: 12,
    color: '#999',
  },
  selectedThresholdOptionValue: {
    color: '#FF6B6B',
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  sliderLabelText: {
    fontSize: 12,
    color: '#666',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    marginRight: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    marginLeft: 10,
    borderRadius: 8,
    backgroundColor: '#FF6B6B',
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
});

export default RecommendationSettings;
