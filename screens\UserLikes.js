import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, FlatList, Image, StyleSheet, ActivityIndicator, SafeAreaView, Platform, StatusBar, TouchableOpacity, Alert, Dimensions } from 'react-native';
import { collection, getDocs, doc, getDoc, deleteDoc, query, where, updateDoc } from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { getLowQualityImageUrl } from '../utils/imageUtils';

// Get screen dimensions for grid layout
const { width } = Dimensions.get('window');
const CATEGORY_ITEM_SIZE = width / 2 - 24; // 2 items per row with some padding

// Available categories
const categories = ['All', 'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories'];

const UserLikes = ({ route, navigation }) => {
  // Get userId from route params or fallback to current user
  const routeUserId = route.params?.userId;
  const [userId, setUserId] = useState(routeUserId || auth.currentUser?.uid);
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [categoryItems, setCategoryItems] = useState({});
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [viewMode, setViewMode] = useState('categories'); // 'categories' or 'items'
  const [isTabScreen, setIsTabScreen] = useState(false);

  // Check if this screen is being shown as a tab or as a stack screen
  useEffect(() => {
    // If we're in the tab navigator, there won't be a navigation.canGoBack()
    // or the route.name will be 'Likes'
    const checkIfTabScreen = () => {
      const state = navigation.getState();
      const routes = state.routes;
      const currentRoute = routes[state.index];
      setIsTabScreen(currentRoute.name === 'Likes');
    };

    checkIfTabScreen();
  }, [navigation]);

  // Make sure we have a valid userId
  useEffect(() => {
    if (!userId && auth.currentUser) {
      console.log("Setting userId from auth.currentUser");
      setUserId(auth.currentUser.uid);
    }
  }, [userId]);

  const fetchLikes = useCallback(async () => {
    if (!userId) {
      console.log("No userId available, cannot fetch likes");
      setError("User ID not available");
      setLoading(false);
      return;
    }

    console.log(`Fetching likes for user: ${userId}`);
    setLoading(true);
    setError(null);

    try {
      const likesSnap = await getDocs(collection(db, 'users', userId, 'likes'));
      console.log(`Found ${likesSnap.docs.length} liked items`);

      const likesDocs = likesSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      if (likesDocs.length === 0) {
        console.log("No liked items found");
        setItems([]);
        setCategoryItems({});
        setLoading(false);
        return;
      }

      const itemIds = likesDocs.map(doc => doc.itemId);
      console.log(`Fetching details for ${itemIds.length} items`);

      const itemPromises = itemIds.map(id => getDoc(doc(db, 'clothingItems', id)));
      const itemDocs = await Promise.all(itemPromises);

      // Combine the likes document IDs with the item data
      const itemsWithLikeIds = [];
      const seenIds = new Set();

      for (const d of itemDocs.filter(d => d.exists())) {
        const itemId = d.id;
        if (!seenIds.has(itemId)) {
          const likeDoc = likesDocs.find(ld => ld.itemId === itemId);
          const likedAt = likeDoc?.likedAt || likeDoc?.timestamp || new Date();

          itemsWithLikeIds.push({
            id: itemId,
            likeDocId: likeDoc?.id, // Store the like document ID
            likedAt: likedAt,
            ...d.data()
          });
          seenIds.add(itemId);
        }
      }

      // Add a uniqueKey property to each item
      itemsWithLikeIds.forEach(item => {
        item.uniqueKey = `${item.id}_${item.likeDocId}`;
      });

      // Sort items by likedAt timestamp (newest first)
      const sortedItems = itemsWithLikeIds.sort((a, b) => {
        const dateA = a.likedAt instanceof Date ? a.likedAt : new Date(a.likedAt?.seconds * 1000 || 0);
        const dateB = b.likedAt instanceof Date ? b.likedAt : new Date(b.likedAt?.seconds * 1000 || 0);
        return dateB - dateA;
      });

      // Group items by category
      const groupedByCategory = {};

      // Initialize all categories with empty arrays
      categories.forEach(category => {
        if (category !== 'All') {
          groupedByCategory[category] = [];
        }
      });

      // Add items to their respective categories
      sortedItems.forEach(item => {
        const category = item.category || 'Other';
        if (groupedByCategory[category]) {
          groupedByCategory[category].push(item);
        } else {
          // If category doesn't exist in our predefined list, create it
          groupedByCategory[category] = [item];
        }
      });

      // Filter out empty categories
      const filteredCategories = Object.fromEntries(
        Object.entries(groupedByCategory).filter(([_, items]) => items.length > 0)
      );

      console.log(`Successfully grouped items into ${Object.keys(filteredCategories).length} categories`);
      setItems(sortedItems);
      setCategoryItems(filteredCategories);
    } catch (e) {
      console.error("Error fetching likes:", e);
      setError("Failed to load liked items");
      setItems([]);
      setCategoryItems({});
    }
    setLoading(false);
  }, [userId]);

  // Use regular useEffect for initial load
  useEffect(() => {
    if (userId) {
      fetchLikes();
    }
  }, [userId, fetchLikes]);

  // Use useFocusEffect to refresh data when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log("Likes screen focused, refreshing data");
      if (userId) {
        fetchLikes();
      }
      return () => {
        // Cleanup function when screen is unfocused
        console.log("Likes screen unfocused");
      };
    }, [userId, fetchLikes])
  );

  const removeFromLikes = async (itemId, likeDocId) => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item from your likes?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Remove",
          onPress: async () => {
            try {
              // Delete the like document
              await deleteDoc(doc(db, 'users', userId, 'likes', likeDocId));

              // Update the item's like count and likedBy array in Firestore
              try {
                const itemRef = doc(db, 'clothingItems', itemId);
                const itemDoc = await getDoc(itemRef);
                if (itemDoc.exists()) {
                  // Update both the like count and remove the user from likedBy array
                  const currentLikeCount = itemDoc.data().likeCount || 0;
                  const updates = {
                    likeCount: currentLikeCount > 0 ? currentLikeCount - 1 : 0
                  };

                  // If likedBy array exists, remove the current user from it
                  if (itemDoc.data().likedBy) {
                    updates.likedBy = itemDoc.data().likedBy.filter(id => id !== userId);
                  }

                  await updateDoc(itemRef, updates);
                  console.log(`Updated item ${itemId}: removed user ${userId} from likedBy and decremented like count`);
                }
              } catch (error) {
                console.error("Error updating item like count and likedBy:", error);
              }

              // Update the UI by removing the item from all states
              setItems(currentItems => currentItems.filter(item => item.id !== itemId));

              // Also update the categoryItems state to remove the item from all categories
              setCategoryItems(prevCategoryItems => {
                const updatedCategoryItems = {};

                // Go through each category and filter out the removed item
                Object.entries(prevCategoryItems).forEach(([category, items]) => {
                  const filteredItems = items.filter(item => item.id !== itemId);

                  // Only keep categories that still have items
                  if (filteredItems.length > 0) {
                    updatedCategoryItems[category] = filteredItems;
                  }
                });

                return updatedCategoryItems;
              });

              // If we're in the items view and this was the last item in the category,
              // go back to the categories view
              if (viewMode === 'items' && selectedCategory) {
                const categoryItemsCount = categoryItems[selectedCategory]?.filter(item => item.id !== itemId).length || 0;
                if (categoryItemsCount === 0) {
                  setViewMode('categories');
                  setSelectedCategory(null);
                }
              }
            } catch (error) {
              console.error("Error removing from likes:", error);
              Alert.alert("Error", "Failed to remove item from likes");
            }
          },
          style: "destructive"
        }
      ]
    );
  };

  // Function to handle category selection
  const handleCategoryPress = (category) => {
    setSelectedCategory(category);
    setViewMode('items');
  };

  // Function to go back to categories view
  const handleBackToCategories = () => {
    setSelectedCategory(null);
    setViewMode('categories');
  };

  // Render a category block
  const renderCategoryItem = ({ item: [category, items] }) => {
    // Get the most recent item in this category
    const featuredItem = items[0];

    return (
      <TouchableOpacity
        style={styles.categoryItem}
        onPress={() => handleCategoryPress(category)}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: getLowQualityImageUrl(featuredItem.imageUrl) }}
          style={styles.categoryImage}
        />
        <View style={styles.categoryOverlay}>
          <Text style={styles.categoryName}>{category}</Text>
          <Text style={styles.categoryCount}>{items.length} items</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Render an individual item in category detail view
  const renderItem = ({ item }) => (
    <View style={styles.itemRow}>
      <TouchableOpacity
        style={styles.itemContent}
        onPress={() => navigation.navigate('ItemDetails', { itemId: item.id })}
        activeOpacity={0.7}
      >
        <Image source={{ uri: getLowQualityImageUrl(item.imageUrl) }} style={styles.image} />        <View style={{ flex: 1 }}>
          <Text style={styles.title}>{item.title}</Text>
          <Text style={styles.category}>{item.category}</Text>
          {item.gender && <Text style={styles.gender}>{item.gender}</Text>}
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeFromLikes(item.id, item.likeDocId)}
      >
        <Ionicons name="close-circle" size={24} color="#FF6B6B" />
      </TouchableOpacity>
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={60} color="#ccc" style={styles.emptyIcon} />
      <Text style={styles.empty}>No liked items found.</Text>
      <Text style={styles.emptySubText}>Like items by swiping right on the feed!</Text>
    </View>
  );

  if (loading) return <ActivityIndicator style={{ flex: 1 }} size="large" color="#FF6B6B" />;

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.headerContainer}>
          {viewMode === 'items' ? (
            // Back button to return to categories view
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBackToCategories}
            >
              <Ionicons name="arrow-back-outline" size={24} color="#FF6B6B" />
            </TouchableOpacity>
          ) : (
            // Back button to navigate back (to ClothingFeed if in tab mode, or previous screen if not)
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => isTabScreen ? navigation.navigate('ClothingFeed') : navigation.goBack()}
            >
              <Ionicons name="arrow-back-outline" size={24} color="#FF6B6B" />
            </TouchableOpacity>
          )}
          <Text style={styles.header}>
            {viewMode === 'categories' ? 'My Likes' : selectedCategory}
          </Text>
          <View style={styles.placeholderButton} />
        </View>

        {error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={60} color="#FF6B6B" />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchLikes}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : viewMode === 'categories' ? (
          // Categories Grid View
          Object.entries(categoryItems).length > 0 ? (
            <FlatList
              key="categoriesGrid"
              data={Object.entries(categoryItems)}
              keyExtractor={([category]) => category}
              renderItem={renderCategoryItem}
              numColumns={2}
              contentContainerStyle={styles.gridContent}
              columnWrapperStyle={styles.gridRow}
              refreshing={loading}
              onRefresh={fetchLikes}
            />
          ) : (
            renderEmptyState()
          )
        ) : (
          // Category Detail View (Items List)
          <FlatList
            key="categoryItems"
            data={categoryItems[selectedCategory] || []}
            keyExtractor={item => item.uniqueKey}
            renderItem={renderItem}
            numColumns={1}
            ListEmptyComponent={renderEmptyState()}
            contentContainerStyle={styles.listContent}
            refreshing={loading}
            onRefresh={fetchLikes}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    paddingTop: 20,
    paddingHorizontal: 16,
    backgroundColor: '#f8f8f8',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
    width: '100%',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B6B',
    letterSpacing: 1,
    flex: 1,
    textAlign: 'center',
  },
  backButton: {
    padding: 10,
    borderRadius: 20,
    width: 40,
  },
  placeholderButton: {
    width: 40,
  },
  // Grid layout styles
  gridContent: {
    paddingBottom: 30,
    paddingTop: 10,
  },
  gridRow: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  categoryItem: {
    width: CATEGORY_ITEM_SIZE,
    height: CATEGORY_ITEM_SIZE,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  categoryOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 10,
  },
  categoryName: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 2,
  },
  categoryCount: {
    color: '#eee',
    fontSize: 12,
  },
  // List view styles
  listContent: {
    paddingBottom: 30,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 1,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  removeButton: {
    padding: 8,
    marginLeft: 8,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 14,
    backgroundColor: '#eee',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 4,
  },  category: {
    fontSize: 14,
    color: '#888',
  },
  gender: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    textTransform: 'capitalize',
  },
  // Empty and error states
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
  },
  emptyIcon: {
    marginBottom: 15,
  },
  empty: {
    textAlign: 'center',
    color: '#888',
    marginTop: 10,
    fontSize: 16,
  },
  emptySubText: {
    textAlign: 'center',
    color: '#aaa',
    marginTop: 5,
    fontSize: 14,
    paddingHorizontal: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF6B6B',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default UserLikes;
