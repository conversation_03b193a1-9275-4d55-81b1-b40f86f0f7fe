import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    ActivityIndicator,
    RefreshControl,
    Alert,
    SafeAreaView,
    Platform,
    StatusBar,
    Modal,
    ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
    collection,
    query,
    where,
    getDocs,
    orderBy,
    doc,
    getDoc
} from 'firebase/firestore';
import TransactionAuditTrail from '../components/TransactionAuditTrail';

const SellerTransactionsScreen = ({ navigation }) => {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [selectedTransaction, setSelectedTransaction] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [totalEarnings, setTotalEarnings] = useState(0);
    const [pendingAmount, setPendingAmount] = useState(0);
    const [completedAmount, setCompletedAmount] = useState(0);
    const currentUserId = auth.currentUser?.uid;

    useEffect(() => {
        if (currentUserId) {
            fetchSellerTransactions();
        }
    }, [currentUserId]); const fetchSellerTransactions = async () => {
        setLoading(true);
        try {
            console.log('[SellerTransactions] Fetching transactions for seller:', currentUserId);

            // Get all transactions for this seller
            const transactionsRef = collection(db, 'sellerTransactions');
            const q = query(
                transactionsRef,
                where('sellerId', '==', currentUserId),
                orderBy('createdAt', 'desc')
            );

            const snapshot = await getDocs(q);
            console.log('[SellerTransactions] Found transactions:', snapshot.size);

            const transactionsData = [];

            for (const docSnapshot of snapshot.docs) {
                const transactionData = docSnapshot.data(); console.log('[SellerTransactions] Processing transaction:', {
                    id: docSnapshot.id,
                    sellerId: transactionData.sellerId,
                    amount: transactionData.amount,
                    amountType: typeof transactionData.amount,
                    grossAmount: transactionData.grossAmount,
                    itemsCount: transactionData.itemsCount,
                    status: transactionData.status
                });

                // Get order details if orderId exists
                let orderDetails = null;
                if (transactionData.orderId) {
                    try {
                        const orderDoc = await getDoc(doc(db, 'orders', transactionData.orderId));
                        if (orderDoc.exists()) {
                            orderDetails = orderDoc.data();
                        }
                    } catch (error) {
                        console.error('[SellerTransactions] Error fetching order details:', error);
                    }
                }

                // Get buyer details if buyerId exists
                let buyerDetails = null;
                if (transactionData.buyerId) {
                    try {
                        const userDoc = await getDoc(doc(db, 'users', transactionData.buyerId));
                        if (userDoc.exists()) {
                            buyerDetails = userDoc.data();
                        }
                    } catch (error) {
                        console.error('[SellerTransactions] Error fetching buyer details:', error);
                    }
                }

                transactionsData.push({
                    id: docSnapshot.id,
                    ...transactionData,
                    createdAt: transactionData.createdAt?.toDate() || new Date(),
                    updatedAt: transactionData.updatedAt?.toDate() || new Date(),
                    transferredAt: transactionData.transferredAt?.toDate() || null,
                    orderDetails,
                    buyerDetails
                });
            }

            setTransactions(transactionsData);
            calculateEarnings(transactionsData);

        } catch (error) {
            console.error('[SellerTransactions] Error fetching transactions:', error);
            Alert.alert('Error', 'Failed to load transactions. Please try again.');
        } finally {
            setLoading(false);
        }
    }; const calculateEarnings = (transactionsData) => {
        let total = 0;
        let pending = 0;
        let completed = 0;

        transactionsData.forEach(transaction => {
            const amount = transaction.amount || 0;
            total += amount;

            if (transaction.status === 'transferred' || transaction.status === 'completed') {
                completed += amount;
            } else {
                pending += amount;
            }
        });

        setTotalEarnings(total);
        setPendingAmount(pending);
        setCompletedAmount(completed);
    };

    const onRefresh = async () => {
        setRefreshing(true);
        await fetchSellerTransactions();
        setRefreshing(false);
    }; const formatDate = (date) => {
        if (!date || !(date instanceof Date)) return 'N/A';
        try {
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.error('[SellerTransactions] Error formatting date:', error);
            return 'N/A';
        }
    }; const formatAmount = (amount) => {
        console.log('[SellerTransactions] Formatting amount:', amount, 'Type:', typeof amount);
        if (typeof amount !== 'number') {
            console.log('[SellerTransactions] Amount is not a number, returning ₹0.00');
            return '₹0.00';
        }
        return `₹${amount.toFixed(2)}`;
    }; const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'transferred':
                return '#4CAF50';
            case 'transfer_in_progress':
                return '#FF9800';
            case 'pending_transfer':
                return '#FFC107';
            case 'failed':
                return '#f44336';
            default:
                return '#666';
        }
    };

    const getStatusIcon = (status) => {
        switch (status?.toLowerCase()) {
            case 'transferred':
                return 'checkmark-circle';
            case 'transfer_in_progress':
                return 'time';
            case 'pending_transfer':
                return 'hourglass';
            case 'failed':
                return 'close-circle';
            default:
                return 'help-circle';
        }
    };

    const openTransactionDetails = (transaction) => {
        setSelectedTransaction(transaction);
        setModalVisible(true);
    }; const renderEarningsCard = () => (
        <View style={styles.earningsCard}>
            <View style={styles.earningsHeader}>
                <Ionicons name="wallet-outline" size={24} color="#FF6B6B" />
                <Text style={styles.earningsTitle}>Your Earnings</Text>
            </View>
            <View style={styles.earningsRow}>
                <View style={styles.earningItem}>
                    <View style={[styles.earningIconContainer, { backgroundColor: '#FF6B6B' }]}>
                        <Ionicons name="trending-up" size={20} color="#fff" />
                    </View>
                    <Text style={styles.earningAmount}>{formatAmount(totalEarnings)}</Text>
                    <Text style={styles.earningLabel}>Total Earnings</Text>
                </View>
                <View style={styles.earningItem}>
                    <View style={[styles.earningIconContainer, { backgroundColor: '#4CAF50' }]}>
                        <Ionicons name="checkmark-circle" size={20} color="#fff" />
                    </View>
                    <Text style={[styles.earningAmount, { color: '#2E7D32' }]}>{formatAmount(completedAmount)}</Text>
                    <Text style={styles.earningLabel}>Received</Text>
                </View>
                <View style={styles.earningItem}>
                    <View style={[styles.earningIconContainer, { backgroundColor: '#FF9800' }]}>
                        <Ionicons name="hourglass" size={20} color="#fff" />
                    </View>
                    <Text style={[styles.earningAmount, { color: '#F57C00' }]}>{formatAmount(pendingAmount)}</Text>
                    <Text style={styles.earningLabel}>Pending</Text>
                </View>
            </View>
        </View>
    ); const renderTransactionItem = ({ item }) => {
        const statusColor = getStatusColor(item.status);
        const statusIcon = getStatusIcon(item.status);

        return (
            <TouchableOpacity
                style={styles.transactionCard}
                onPress={() => openTransactionDetails(item)}
                activeOpacity={0.7}
            >
                {/* Card Header with gradient background */}
                <View style={styles.cardHeaderGradient}>
                    <View style={styles.transactionHeader}>
                        <View style={styles.transactionInfo}>
                            <View style={styles.transactionIdContainer}>
                                <Ionicons name="receipt-outline" size={16} color="#333" />
                                <Text style={styles.transactionId}>
                                    #{item.transactionNumber || item.id.slice(-8).toUpperCase()}
                                </Text>
                            </View>
                            <Text style={styles.orderInfo}>
                                Order: {item.orderId ? `#${item.orderId.slice(-8).toUpperCase()}` : 'N/A'}
                            </Text>
                            {item.buyerDetails && (
                                <View style={styles.buyerContainer}>
                                    <Ionicons name="person-outline" size={14} color="#666" />
                                    <Text style={styles.buyerInfo}>
                                        {item.buyerDetails.name || 'Unknown'}
                                    </Text>
                                </View>
                            )}
                        </View>
                        <View style={styles.amountContainer}>
                            <Text style={styles.amount}>
                                {formatAmount(item.amount)}
                            </Text>
                            <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
                                <Ionicons name={statusIcon} size={12} color="#fff" />
                                <Text style={styles.statusText}>
                                    {(item.status || 'Unknown').replace('_', ' ').toUpperCase()}
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Card Body */}
                <View style={styles.cardBody}>
                    <View style={styles.dateContainer}>
                        <Ionicons name="time-outline" size={14} color="#888" />
                        <Text style={styles.dateText}>
                            {formatDate(item.createdAt)}
                        </Text>
                    </View>

                    {item.description && (
                        <View style={styles.descriptionContainer}>
                            <Text style={styles.descriptionText} numberOfLines={2}>
                                {item.description}
                            </Text>
                        </View>
                    )}

                    {/* Item count and details */}
                    {item.itemsCount && (
                        <View style={styles.itemsContainer}>
                            <Ionicons name="bag-outline" size={14} color="#666" />
                            <Text style={styles.itemsText}>
                                {item.itemsCount} item{item.itemsCount > 1 ? 's' : ''}
                            </Text>
                        </View>
                    )}

                    {item.status === 'transferred' && item.transferredAt && (
                        <View style={styles.completedContainer}>
                            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                            <Text style={styles.completedText}>
                                Transferred on {formatDate(item.transferredAt)}
                            </Text>
                        </View>
                    )}
                </View>

                {/* Card Footer */}
                <View style={styles.cardFooter}>
                    <Text style={styles.tapToView}>Tap to view details</Text>
                    <Ionicons name="chevron-forward" size={16} color="#FF6B6B" />
                </View>
            </TouchableOpacity>
        );
    };

    const renderTransactionDetailsModal = () => {
        if (!selectedTransaction) return null;

        return (
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <ScrollView style={styles.modalScrollView}>
                            {/* Header */}
                            <View style={styles.modalHeader}>
                                <Text style={styles.modalTitle}>Transaction Details</Text>
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={() => setModalVisible(false)}
                                >
                                    <Ionicons name="close" size={24} color="#333" />
                                </TouchableOpacity>
                            </View>

                            {/* Transaction Info */}
                            <View style={styles.modalSection}>
                                <Text style={styles.sectionTitle}>Transaction Information</Text>
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Transaction ID:</Text>
                                    <Text style={styles.infoValue}>
                                        #{selectedTransaction.transactionNumber || selectedTransaction.id.slice(-8).toUpperCase()}
                                    </Text>
                                </View>
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Amount:</Text>
                                    <Text style={[styles.infoValue, styles.amountValue]}>
                                        {formatAmount(selectedTransaction.amount)}
                                    </Text>
                                </View>
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Status:</Text>
                                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedTransaction.status) }]}>
                                        <Ionicons name={getStatusIcon(selectedTransaction.status)} size={14} color="#fff" />
                                        <Text style={styles.statusText}>
                                            {(selectedTransaction.status || 'Unknown').toUpperCase()}
                                        </Text>
                                    </View>
                                </View>
                                <View style={styles.infoRow}>
                                    <Text style={styles.infoLabel}>Created:</Text>
                                    <Text style={styles.infoValue}>
                                        {formatDate(selectedTransaction.createdAt)}
                                    </Text>
                                </View>
                                {selectedTransaction.transferredAt && (
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Transferred:</Text>
                                        <Text style={styles.infoValue}>
                                            {formatDate(selectedTransaction.transferredAt)}
                                        </Text>
                                    </View>
                                )}
                            </View>

                            {/* Order Details */}
                            {selectedTransaction.orderDetails && (
                                <View style={styles.modalSection}>
                                    <Text style={styles.sectionTitle}>Order Information</Text>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Order ID:</Text>
                                        <Text style={styles.infoValue}>
                                            #{selectedTransaction.orderId.slice(-8).toUpperCase()}
                                        </Text>
                                    </View>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Order Total:</Text>
                                        <Text style={styles.infoValue}>
                                            {formatAmount(selectedTransaction.orderDetails.totalAmount)}
                                        </Text>
                                    </View>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Order Status:</Text>
                                        <Text style={styles.infoValue}>
                                            {selectedTransaction.orderDetails.status || 'N/A'}
                                        </Text>
                                    </View>
                                </View>
                            )}

                            {/* Buyer Details */}
                            {selectedTransaction.buyerDetails && (
                                <View style={styles.modalSection}>
                                    <Text style={styles.sectionTitle}>Buyer Information</Text>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Name:</Text>
                                        <Text style={styles.infoValue}>
                                            {selectedTransaction.buyerDetails.name || 'N/A'}
                                        </Text>
                                    </View>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Email:</Text>
                                        <Text style={styles.infoValue}>
                                            {selectedTransaction.buyerDetails.email || 'N/A'}
                                        </Text>
                                    </View>
                                </View>
                            )}

                            {/* Platform Fee Details */}
                            {selectedTransaction.platformFee && (
                                <View style={styles.modalSection}>
                                    <Text style={styles.sectionTitle}>Fee Breakdown</Text>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Gross Amount:</Text>
                                        <Text style={styles.infoValue}>
                                            {formatAmount((selectedTransaction.amount || 0) + (selectedTransaction.platformFee || 0))}
                                        </Text>
                                    </View>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Platform Fee:</Text>
                                        <Text style={styles.infoValue}>
                                            -{formatAmount(selectedTransaction.platformFee)}
                                        </Text>
                                    </View>
                                    <View style={styles.infoRow}>
                                        <Text style={styles.infoLabel}>Net Amount:</Text>
                                        <Text style={[styles.infoValue, styles.amountValue]}>
                                            {formatAmount(selectedTransaction.amount)}
                                        </Text>
                                    </View>
                                </View>
                            )}

                            {/* Transaction Details */}
                            {selectedTransaction.transactionDetails && (
                                <View style={styles.modalSection}>
                                    <Text style={styles.sectionTitle}>Transfer Details</Text>
                                    {selectedTransaction.transactionDetails.method && (
                                        <View style={styles.infoRow}>
                                            <Text style={styles.infoLabel}>Method:</Text>
                                            <Text style={styles.infoValue}>
                                                {selectedTransaction.transactionDetails.method}
                                            </Text>
                                        </View>
                                    )}
                                    {selectedTransaction.transactionDetails.referenceNumber && (
                                        <View style={styles.infoRow}>
                                            <Text style={styles.infoLabel}>Reference:</Text>
                                            <Text style={styles.infoValue}>
                                                {selectedTransaction.transactionDetails.referenceNumber}
                                            </Text>
                                        </View>
                                    )}
                                    {selectedTransaction.transactionDetails.bankName && (
                                        <View style={styles.infoRow}>
                                            <Text style={styles.infoLabel}>Bank:</Text>
                                            <Text style={styles.infoValue}>
                                                {selectedTransaction.transactionDetails.bankName}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            )}

                            {/* Admin Notes */}
                            {selectedTransaction.adminNotes && (
                                <View style={styles.modalSection}>
                                    <Text style={styles.sectionTitle}>Admin Notes</Text>
                                    <Text style={styles.notesText}>
                                        {selectedTransaction.adminNotes}
                                    </Text>
                                </View>
                            )}                            {/* Description */}
                            {selectedTransaction.description && (
                                <View style={styles.modalSection}>
                                    <Text style={styles.sectionTitle}>Description</Text>
                                    <Text style={styles.notesText}>
                                        {selectedTransaction.description}
                                    </Text>
                                </View>
                            )}

                            {/* Transaction History */}
                            <TransactionAuditTrail
                                transactionId={selectedTransaction.id}
                                style={styles.auditTrailSection}
                            />
                        </ScrollView>
                    </View>
                </View>
            </Modal>
        );
    };

    if (loading) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#FF6B6B" />
                    <Text style={styles.loadingText}>Loading transactions...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <Ionicons name="arrow-back" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>My Transactions</Text>
                <TouchableOpacity
                    style={styles.refreshButton}
                    onPress={onRefresh}
                >
                    <Ionicons name="refresh" size={24} color="#333" />
                </TouchableOpacity>
            </View>

            {/* Earnings Summary */}
            {renderEarningsCard()}

            <FlatList
                data={transactions}
                renderItem={renderTransactionItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Ionicons name="card-outline" size={80} color="#ccc" />
                        <Text style={styles.emptyText}>No transactions yet</Text>
                        <Text style={styles.emptySubtext}>
                            Transactions from completed orders will appear here
                        </Text>
                    </View>
                }
            />

            {/* Transaction Details Modal */}
            {renderTransactionDetailsModal()}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    backButton: {
        padding: 8,
    },
    refreshButton: {
        padding: 8,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        textAlign: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#666',
    }, earningsCard: {
        backgroundColor: '#fff',
        margin: 16,
        padding: 24,
        borderRadius: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 6,
        borderWidth: 0.5,
        borderColor: '#f0f0f0',
    },
    earningsHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
    },
    earningsTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 8,
    },
    earningsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    earningItem: {
        alignItems: 'center',
        flex: 1,
    },
    earningIconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    earningAmount: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#FF6B6B',
        marginBottom: 4,
    }, earningLabel: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
        fontWeight: '500',
    },
    listContainer: {
        padding: 16,
    }, transactionCard: {
        backgroundColor: '#fff',
        borderRadius: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 6,
        overflow: 'hidden',
        borderWidth: 0.5,
        borderColor: '#f0f0f0',
    },
    cardHeaderGradient: {
        backgroundColor: '#fafafa',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f5f5f5',
    },
    cardBody: {
        paddingHorizontal: 16,
        paddingVertical: 12,
    },
    transactionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    transactionInfo: {
        flex: 1,
        marginRight: 12,
    },
    transactionIdContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 6,
    },
    transactionId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 6,
    },
    orderInfo: {
        fontSize: 14,
        color: '#666',
        marginBottom: 4,
        marginLeft: 22,
    },
    buyerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 22,
    },
    buyerInfo: {
        fontSize: 14,
        color: '#666',
        marginLeft: 4,
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    dateText: {
        fontSize: 12,
        color: '#888',
        marginLeft: 6,
    },
    itemsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
    },
    itemsText: {
        fontSize: 13,
        color: '#666',
        marginLeft: 6,
        fontWeight: '500',
    },
    amountContainer: {
        alignItems: 'flex-end',
    },
    amount: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2E7D32',
        marginBottom: 8,
    },
    amountValue: {
        color: '#2E7D32',
        fontWeight: 'bold',
        fontSize: 16,
    },
    statusBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        minWidth: 80,
        justifyContent: 'center',
    },
    statusText: {
        color: '#fff',
        fontSize: 11,
        fontWeight: 'bold',
        marginLeft: 4,
    },
    descriptionContainer: {
        marginTop: 8,
        padding: 12,
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        borderLeftWidth: 3,
        borderLeftColor: '#FF6B6B',
    },
    descriptionText: {
        fontSize: 14,
        color: '#555',
        lineHeight: 20,
        fontStyle: 'italic',
    },
    completedContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 12,
        padding: 8,
        backgroundColor: '#e8f5e8',
        borderRadius: 8,
        borderLeftWidth: 3,
        borderLeftColor: '#4CAF50',
    },
    completedText: {
        fontSize: 12,
        color: '#2E7D32',
        marginLeft: 6,
        fontWeight: '600',
    },
    cardFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#fafafa',
        borderTopWidth: 1,
        borderTopColor: '#f5f5f5',
    },
    tapToView: {
        fontSize: 13,
        color: '#FF6B6B',
        fontWeight: '500',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modalContent: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        maxHeight: '90%',
        minHeight: '50%',
    },
    modalScrollView: {
        flex: 1,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    closeButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalSection: {
        backgroundColor: '#fff',
        marginHorizontal: 16,
        marginVertical: 8,
        padding: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 12,
    },
    infoRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
    notesText: {
        fontSize: 14,
        color: '#666',
        lineHeight: 20,
    },
    auditTrailSection: {
        marginTop: 8,
        backgroundColor: '#fff',
    },
});

export default SellerTransactionsScreen;
