import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator, StatusBar, Platform } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { auth, db } from './firebase.config';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { Ionicons } from '@expo/vector-icons';

// Import screens
import WelcomeScreen from './screens/WelcomeScreen';
import LoginScreen from './screens/LoginScreen';
import SignUpScreen from './screens/SignUpScreen';
import VerifyEmailScreen from './screens/VerifyEmailScreen'; // Import the new VerifyEmailScreen
import ForgotPasswordScreen from './screens/ForgotPasswordScreen'; // Import the new ForgotPasswordScreen
import ClothingFeedScreen from './screens/ClothingFeedScreen';
import UploadScreen from './screens/UploadScreen';
import ItemDetailsScreen from './screens/ItemDetailsScreen';
import LeaderboardScreen from './screens/LeaderboardScreen';
import MyProfileScreen from './screens/MyProfileScreen'; // Renamed from ProfileScreen
import EditProfileScreen from './screens/EditProfileScreen';
import EditItemScreen from './screens/EditItemScreen';
import UserUploads from './screens/UserUploads';
import UserWishlist from './screens/UserWishlist';
import UserLikes from './screens/UserLikes';
import SettingsScreen from './screens/SettingsScreen';
import { MessagesScreen } from './screens/MessagesScreen.js';
console.log('MessagesScreen imported:', MessagesScreen);
import ChatThreadScreen from './screens/ChatThreadScreen';
import UserProfileScreen from './screens/UserProfileScreen'; // Import the new UserProfileScreen
import CartScreen from './screens/CartScreen'; // Import the new CartScreen
import CollectionDetailScreen from './screens/CollectionDetailScreen'; // Import the new CollectionDetailScreen
import CategoryItemsScreen from './screens/CategoryItemsScreen'; // Import the new CategoryItemsScreen
import SellerListingsScreen from './screens/SellerListingsScreen'; // Import the new SellerListingsScreen
import SellerMetricsScreen from './screens/SellerMetricsScreen'; // Import the new SellerMetricsScreen
import SellerVerificationScreen from './screens/SellerVerificationScreen'; // Import the seller verification screen
import VerificationPendingScreen from './screens/VerificationPendingScreen'; // Import the verification pending screen
import SellerCodeVerificationScreen from './screens/SellerCodeVerificationScreen'; // Import the seller code verification screen
import FirebaseTestScreen from './screens/FirebaseTestScreen'; // Import our improved Firebase test screen
import InteractionTestScreen from './screens/InteractionTestScreen'; // Import the interaction test screen
import AddressScreen from './screens/AddressScreen'; // Import the address screen
import CheckoutScreen from './screens/CheckoutScreen'; // Import the checkout screen
import OrderConfirmationScreen from './screens/OrderConfirmationScreen'; // Import the order confirmation screen
import OrderHistoryScreen from './screens/OrderHistoryScreen'; // Import the order history screen
import OrderDetailsScreen from './screens/OrderDetailsScreen'; // Import the order details screen
import SellerOrdersScreen from './screens/SellerOrdersScreen'; // Import the seller orders screen
import SupportScreen from './screens/SupportScreen'; // Import the support screen
import FAQScreen from './screens/FAQScreen'; // Import the FAQ screen
import LiveChatScreen from './screens/LiveChatScreen'; // Import the live chat screen
import AdminSupportDashboard from './screens/AdminSupportDashboard';
import AdminLiveChatScreen from './screens/AdminLiveChatScreen';
import AdminDashboard from './screens/AdminDashboard';
import AdminUserManagement from './screens/AdminUserManagement';
import AdminContentModeration from './screens/AdminContentModeration';
import AdminOrderManagement from './screens/AdminOrderManagement';
import AdminAnalytics from './screens/AdminAnalytics';
import AdminSettings from './screens/AdminSettings';
import AdminTestScreen from './screens/AdminTestScreen';
import AdminVerificationManagement from './screens/AdminVerificationManagement';
import AdminTransactionManagementScreen from './screens/AdminTransactionManagementScreen';
import AdminSellerDetailsScreen from './screens/AdminSellerDetailsScreen';
import SellerAccountDetailsScreen from './screens/SellerAccountDetailsScreen';
import SellerTransactionsScreen from './screens/SellerTransactionsScreen';
import MaintenanceScreen from './screens/MaintenanceScreen';
import CategoryMigrationScreen from './screens/CategoryMigrationScreen';
import CategoryTestScreen from './screens/CategoryTestScreen';
import { isMaintenanceModeEnabled } from './utils/settingsUtils';
import { checkAdminStatus } from './utils/adminUtils'; // Import the admin support dashboard
import authStateManager from './utils/authState'; // Import the auth state manager
import userStateManager from './utils/userStateManager'; // Import the user state manager

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Tinder-like main tab navigator
function MainTabNavigator({ user }) {
  // Use the user data passed from the main App component instead of doing a separate check
  const isSeller = user?.isSeller || false;

  console.log('MainTabNavigator - User seller status:', isSeller, 'User data:', user);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'ClothingFeed') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Explore') {
            iconName = focused ? 'compass' : 'compass-outline';
          } else if (route.name === 'Upload') {
            iconName = focused ? 'add-circle' : 'add-circle-outline';
          } else if (route.name === 'MyProfile') {
            iconName = focused ? 'person' : 'person-outline';
          } else if (route.name === 'Saved') {
            iconName = focused ? 'bookmark' : 'bookmark-outline';
          } else if (route.name === 'Likes') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Listings') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Metrics') {
            iconName = focused ? 'stats-chart' : 'stats-chart-outline';
          } else if (route.name === 'Orders') {
            iconName = focused ? 'receipt' : 'receipt-outline';
          }

          if (!iconName) {
            iconName = 'help-circle-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B6B',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 0,
          height: 80, // Increased from 70 to 80
          paddingBottom: 20, // Increased from 10 to 20
          paddingTop: 5, // Decreased from 10 to 5 to move icons higher
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -3 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          elevation: 5,
        },
        tabBarShowLabel: false,
      })}>
      {isSeller ? (
        // Seller tabs
        <>
          <Tab.Screen name="Listings" component={SellerListingsScreen} />
          <Tab.Screen name="Orders" component={SellerOrdersScreen} />
          <Tab.Screen name="Metrics" component={SellerMetricsScreen} />
          <Tab.Screen name="Upload" component={UploadScreen} />
          <Tab.Screen name="MyProfile" component={MyProfileScreen} />
        </>
      ) : (
        // Buyer tabs
        <>
          <Tab.Screen name="ClothingFeed" component={ClothingFeedScreen} />
          <Tab.Screen name="Explore" component={LeaderboardScreen} />
          <Tab.Screen name="Saved" component={UserWishlist} initialParams={{ userId: auth.currentUser?.uid }} />
          <Tab.Screen name="Likes" component={UserLikes} initialParams={{ userId: auth.currentUser?.uid }} />
          <Tab.Screen name="MyProfile" component={MyProfileScreen} />
        </>
      )}
    </Tab.Navigator>
  );
}

// Auth stack navigator
function AuthStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="SignUp" component={SignUpScreen} />
      <Stack.Screen name="VerifyEmail" component={VerifyEmailScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="SellerVerification" component={SellerVerificationScreen} />
      <Stack.Screen name="VerificationPending" component={VerificationPendingScreen} />
      <Stack.Screen name="SellerCodeVerification" component={SellerCodeVerificationScreen} />
      <Stack.Screen
        name="ItemDetails"
        component={ItemDetailsScreen}
        options={{
          headerShown: true,
          headerTitle: '',
          headerBackTitleVisible: false,
          headerTransparent: true,
          headerTintColor: '#000',
        }}
      />
    </Stack.Navigator>
  );
}

export default function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      console.log('Auth state changed:', currentUser ? `User logged in: ${currentUser.email}` : 'User logged out');

      // If user logs out, clear everything
      if (!currentUser) {
        setUser(null);
        setLoading(false);
        setIsAdmin(false);
        // Don't clear mismatch flag on logout - let it expire naturally to prevent race conditions
        return;
      }

      // Check if we should ignore this auth event due to user type mismatch
      if (authStateManager.shouldIgnoreAuthEvent(currentUser.uid)) {
        console.log('Ignoring auth event due to user type mismatch in progress');
        setUser(null);
        setLoading(false);
        setIsAdmin(false);
        return;
      }

      // CRITICAL: Add a longer delay and multiple checks to ensure LoginScreen operations complete
      // This delay helps to ensure that if LoginScreen initiated a signOut, it completes.
      console.log('Waiting for potential LoginScreen operations to complete...');
      await new Promise(resolve => setTimeout(resolve, 700)); // Increased delay slightly

      // Re-check authentication status after delay
      const stillAuthenticatedUser = auth.currentUser;
      if (!stillAuthenticatedUser || stillAuthenticatedUser.uid !== currentUser.uid) {
        console.log('User was signed out or changed during processing, aborting auth flow.');
        // Don't clear mismatch flag here either - let it expire naturally
        setUser(null);
        setLoading(false);
        setIsAdmin(false); // Reset admin status
        return;
      }

      // Check again if we should ignore this auth event after the delay
      if (authStateManager.shouldIgnoreAuthEvent(stillAuthenticatedUser.uid)) {
        console.log('Ignoring auth event after delay due to user type mismatch');
        setUser(null);
        setLoading(false);
        setIsAdmin(false);
        return;
      }

      // Use the most current authenticated user
      const freshCurrentUser = stillAuthenticatedUser;

      // Check maintenance mode and admin status only if user is authenticated
      try {
        const maintenanceEnabled = await isMaintenanceModeEnabled();
        setMaintenanceMode(maintenanceEnabled);

        // Check admin status using the freshCurrentUser
        const adminStatus = await checkAdminStatus(freshCurrentUser.uid);
        setIsAdmin(adminStatus);

      } catch (error) {
        // Handle permission errors gracefully
        if (error.code === 'permission-denied') {
          console.log('Permission denied for app settings, using defaults');
        } else {
          console.error('Error checking app settings:', error);
        }
        // Don't fail the authentication flow due to settings errors
        setMaintenanceMode(false); // Default to not in maintenance mode
        setIsAdmin(false); // Default to not admin on error
      }

      // If there's a user logged in (use freshCurrentUser)
      try {
        const userDocRef = doc(db, 'users', freshCurrentUser.uid);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();

          // Add null check for userData
          if (!userData) {
            console.log('User data is null, signing out user');
            await auth.signOut(); // This will trigger onAuthStateChanged again
            // setUser(null); // Let onAuthStateChanged handle state update
            // setLoading(false);
            return;
          }          // If the user (buyer or seller) is not email verified, redirect to verification screen
          if (!userData.emailVerified && !freshCurrentUser.emailVerified) {
            console.log("User needs email verification, redirecting to verification screen");
            setUser({
              ...freshCurrentUser,
              needsVerification: true,
              isSeller: userData.isSeller || false // Pass seller status to verification screen
            });
            setLoading(false);
            return;
          }
          // If the user is a seller with email verified but needs to complete the verification form
          else if (userData.isSeller && userData.sellerVerificationStatus === 'needsSellerInfo') {
            console.log("Seller needs to complete verification form");
            setUser({
              ...freshCurrentUser,
              needsSellerOnboarding: true
            });
            setLoading(false);
            return;
          }          // If the user is a seller and needs to complete the verification form (fallback for older accounts)
          // Only check field validation if the seller is not already verified
          else if (userData.isSeller &&
            userData.sellerVerificationStatus !== 'verified' && // First check if not already verified
            userData.sellerVerificationStatus !== 'needsCode' && // And not waiting for code
            (!userData.address || !userData.address.city || !userData.phoneNumber || !userData.website)) { // Then check missing fields
            console.log("Seller needs to complete verification form (fallback)");
            setUser({
              ...freshCurrentUser,
              needsSellerOnboarding: true
            });
            setLoading(false);
            return;
          }
          // If the user is a seller and needs to enter verification code
          else if (userData.isSeller && userData.sellerVerificationStatus === 'needsCode') {
            console.log("Seller needs to enter verification code");
            setUser({
              ...freshCurrentUser,
              needsSellerCodeVerification: true
            });
            setLoading(false);
            return;
          }
          else {
            // If the user is verified in Firebase but not in Firestore, update Firestore
            if (freshCurrentUser.emailVerified && !userData.emailVerified) {
              console.log("User is verified in Firebase but not in Firestore, updating Firestore");
              try {
                await updateDoc(userDocRef, { emailVerified: true });
                // Update local userData copy as well
                userData.emailVerified = true;
              } catch (error) {
                console.error("Error updating verification status in Firestore:", error);
              }
            }
            // Final check before proceeding - ensure no user type mismatch is in progress
            if (authStateManager.shouldIgnoreAuthEvent(freshCurrentUser.uid)) {
              console.log('Final check: Ignoring auth event due to user type mismatch');
              setUser(null);
              setLoading(false);
              setIsAdmin(false);
              return;
            }

            console.log("User is verified or is a seller, proceeding to main app. User data:", userData);
            // Set user state with combined data from auth and Firestore
            setUser({
              ...freshCurrentUser,
              ...userData, // Spread userData to include isSeller, etc.
              // Explicitly ensure flags are false if conditions above are not met
              needsVerification: false,
              needsSellerOnboarding: false,
              needsSellerCodeVerification: false
            });
          }
        } else {
          // User document doesn't exist, sign them out to prevent issues
          console.log("User document doesn't exist in Firestore, signing out");
          await auth.signOut(); // This will trigger onAuthStateChanged again
          // setUser(null); // Let onAuthStateChanged handle state update
          // setLoading(false);
          return;
        }
      } catch (error) {
        console.error("Error checking user verification status or Firestore data:", error);
        // On error, sign out the user to prevent inconsistent state
        try {
          await auth.signOut(); // This will trigger onAuthStateChanged again
        } catch (signOutError) {
          console.error("Error signing out user after verification error:", signOutError);
        }
        // setUser(null); // Let onAuthStateChanged handle state update
        // setLoading(false);
        return;
      }
      // Normal flow for verified users or sellers
      // setUser(freshCurrentUser); // This was potentially missing userData like isSeller
      setLoading(false);
    }); return () => unsubscribe();
  }, []);

  // Set up user state manager listener for forced refreshes
  useEffect(() => {
    const handleUserStateUpdate = (updatedUser) => {
      console.log('UserStateManager: Received user state update:', updatedUser);
      setUser(updatedUser);
      setLoading(false);
    };

    userStateManager.addListener(handleUserStateUpdate);

    return () => {
      userStateManager.removeListener(handleUserStateUpdate);
    };
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#FF6B6B" />
      </View>
    );
  }

  // Show maintenance screen if maintenance mode is enabled and user is not admin
  if (maintenanceMode && !isAdmin) {
    return (
      <GestureHandlerRootView style={{ flex: 1 }}>
        <NavigationContainer>
          <StatusBar
            backgroundColor="#fff"
            barStyle="dark-content"
            translucent={true}
          />
          <MaintenanceScreen />
        </NavigationContainer>
      </GestureHandlerRootView>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <NavigationContainer
        theme={{
          colors: {
            background: '#fff',
          },
        }}
      >
        <StatusBar
          backgroundColor="#fff"
          barStyle="dark-content"
          translucent={true}
        />
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            cardStyle: { backgroundColor: '#fff' },
            headerStatusBarHeight: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
          }}
        >
          {user ? (user.needsVerification ? (
            // If user needs email verification, show VerifyEmail screen
            <>
              <Stack.Screen
                name="VerifyEmail"
                component={VerifyEmailScreen}
                initialParams={{ isSeller: user.isSeller || false }}
              />
              <Stack.Screen name="Welcome" component={WelcomeScreen} />
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="SignUp" component={SignUpScreen} />
              <Stack.Screen name="SellerVerification" component={SellerVerificationScreen} />
              <Stack.Screen name="VerificationPending" component={VerificationPendingScreen} />
              <Stack.Screen name="SellerCodeVerification" component={SellerCodeVerificationScreen} />
              {/* Include MainApp screen in the stack so we can navigate to it from VerifyEmail */}
              <Stack.Screen name="MainApp" options={{ headerShown: false }}>
                {() => <MainTabNavigator user={user} />}
              </Stack.Screen>
              {/* Add ItemDetails to this stack */}
              <Stack.Screen
                name="ItemDetails"
                component={ItemDetailsScreen}
                options={{
                  headerShown: true,
                  headerTitle: '',
                  headerBackTitleVisible: false,
                  headerTransparent: true,
                  headerTintColor: '#000',
                }}
              />
              {/* Add EditProfile to this stack as well */}
              <Stack.Screen
                name="EditProfile"
                component={EditProfileScreen}
                options={{
                  headerShown: false,
                }}
              />
            </>
          ) : user.needsSellerOnboarding ? (
            // If seller needs to complete the verification form
            <>
              <Stack.Screen name="SellerVerification" component={SellerVerificationScreen} />
              <Stack.Screen name="VerificationPending" component={VerificationPendingScreen} />
              <Stack.Screen name="SellerCodeVerification" component={SellerCodeVerificationScreen} />
              <Stack.Screen name="Welcome" component={WelcomeScreen} />
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="SignUp" component={SignUpScreen} />
              <Stack.Screen name="MainApp" options={{ headerShown: false }}>
                {() => <MainTabNavigator user={user} />}
              </Stack.Screen>
              {/* Add ItemDetails to this stack */}
              <Stack.Screen
                name="ItemDetails"
                component={ItemDetailsScreen}
                options={{
                  headerShown: true,
                  headerTitle: '',
                  headerBackTitleVisible: false,
                  headerTransparent: true,
                  headerTintColor: '#000',
                }}
              />
              {/* Add EditProfile to this stack as well */}
              <Stack.Screen
                name="EditProfile"
                component={EditProfileScreen}
                options={{
                  headerShown: false,
                }}
              />
            </>
          ) : user.needsSellerCodeVerification ? (
            // If seller needs to enter verification code, show SellerCodeVerification screen
            <>
              <Stack.Screen name="SellerCodeVerification" component={SellerCodeVerificationScreen} />
              <Stack.Screen name="VerificationPending" component={VerificationPendingScreen} />
              <Stack.Screen name="Welcome" component={WelcomeScreen} />
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="SignUp" component={SignUpScreen} />
              <Stack.Screen name="MainApp" options={{ headerShown: false }}>
                {() => <MainTabNavigator user={user} />}
              </Stack.Screen>
              {/* Add ItemDetails to this stack */}
              <Stack.Screen
                name="ItemDetails"
                component={ItemDetailsScreen}
                options={{
                  headerShown: true,
                  headerTitle: '',
                  headerBackTitleVisible: false,
                  headerTransparent: true,
                  headerTintColor: '#000',
                }}
              />
              {/* Add EditProfile to this stack as well */}
              <Stack.Screen
                name="EditProfile"
                component={EditProfileScreen}
                options={{
                  headerShown: false,
                }}
              />
            </>
          ) : (
            // If user is logged in and verified (or is a seller), show MainApp and allow navigating to other screens
            <>
              <Stack.Screen name="MainApp" options={{ headerShown: false }}>
                {() => <MainTabNavigator user={user} />}
              </Stack.Screen>
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="SignUp" component={SignUpScreen} />
              <Stack.Screen name="Welcome" component={WelcomeScreen} />
              <Stack.Screen
                name="ItemDetails"
                component={ItemDetailsScreen}
                options={{
                  headerShown: true,
                  headerTitle: '',
                  headerBackTitleVisible: false,
                  headerTransparent: true,
                  headerTintColor: '#000',
                }}
              />
              {/* EditProfileScreen for both buyers and sellers */}
              <Stack.Screen
                name="EditProfile"
                component={EditProfileScreen}
                options={{
                  headerShown: false, // Hide default header, custom header is in the screen
                }}
              />
              <Stack.Screen name="EditItem" component={EditItemScreen} options={{ headerShown: false }} />
              <Stack.Screen name="UserUploads" component={UserUploads} />
              <Stack.Screen name="UserWishlist" component={UserWishlist} />
              <Stack.Screen name="UserLikes" component={UserLikes} />
              <Stack.Screen
                name="UserProfile" // Add UserProfileScreen to the stack
                component={UserProfileScreen}
                options={{
                  headerShown: false, // Hide default header, custom header is in the screen
                }}
              />
              <Stack.Screen
                name="ChatThread"
                component={ChatThreadScreen}
                options={{ headerShown: true, headerTitle: '' }}
              />
              {/* Add SettingsScreen to the main stack */}
              <Stack.Screen
                name="Settings"
                component={SettingsScreen}
                options={{
                  headerShown: false, // Or customize as needed
                }}
              />
              <Stack.Screen
                name="Cart"
                component={CartScreen}
                options={{
                  headerShown: false, // Custom header is in the screen
                }}
              />
              <Stack.Screen
                name="CollectionDetail"
                component={CollectionDetailScreen}
                options={{
                  headerShown: false, // Custom header is in the screen
                }}
              />
              <Stack.Screen
                name="CategoryItems"
                component={CategoryItemsScreen}
                options={{
                  headerShown: false, // Custom header is in the screen
                }}
              />
              <Stack.Screen
                name="VerifyEmail"
                component={VerifyEmailScreen}
                options={{
                  headerShown: false, // Custom header is in the screen
                }}
              />
              <Stack.Screen
                name="ForgotPassword"
                component={ForgotPasswordScreen}
                options={{
                  headerShown: false, // Custom header is in the screen
                }}
              />
              <Stack.Screen
                name="SellerCodeVerification"
                component={SellerCodeVerificationScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="VerificationPending"
                component={VerificationPendingScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="FirebaseTest"
                component={FirebaseTestScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="InteractionTest"
                component={InteractionTestScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AddressScreen"
                component={AddressScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="Checkout"
                component={CheckoutScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="OrderConfirmation"
                component={OrderConfirmationScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="OrderHistory"
                component={OrderHistoryScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="OrderDetails"
                component={OrderDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="SellerOrderDetails"
                component={OrderDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="Support"
                component={SupportScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="FAQ"
                component={FAQScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="LiveChat"
                component={LiveChatScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminSupport"
                component={AdminSupportDashboard}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminLiveChatScreen"
                component={AdminLiveChatScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminDashboard"
                component={AdminDashboard}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminUserManagement"
                component={AdminUserManagement}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminContentModeration"
                component={AdminContentModeration}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminOrderManagement"
                component={AdminOrderManagement}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminAnalytics"
                component={AdminAnalytics}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="AdminSettings"
                component={AdminSettings}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminTestScreen"
                component={AdminTestScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminVerificationManagement"
                component={AdminVerificationManagement}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminTransactionManagement"
                component={AdminTransactionManagementScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="AdminSellerDetails"
                component={AdminSellerDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="SellerAccountDetails"
                component={SellerAccountDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="SellerTransactions"
                component={SellerTransactionsScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="MaintenanceScreen"
                component={MaintenanceScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="CategoryMigration"
                component={CategoryMigrationScreen}
                options={{
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="CategoryTest"
                component={CategoryTestScreen}
                options={{
                  headerShown: false,
                }}
              />

            </>
          )
          ) : (
            // If user is not logged in, show AuthFlow
            <Stack.Screen name="AuthFlow" component={AuthStack} />
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}