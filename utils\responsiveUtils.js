import { Dimensions, Platform, StatusBar, PixelRatio } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Get screen dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Base dimensions (based on iPhone 16 Pro design)
const baseWidth = 390;
const baseHeight = 844;

// Pixel ratio for the device
const pixelRatio = PixelRatio.get();

// Check if the device is a tablet
const isTablet = SCREEN_WIDTH >= 768 || SCREEN_HEIGHT >= 768;

// Check if the device has a notch (approximation)
const hasNotch = Platform.OS === 'ios' && !Platform.isPad && !Platform.isTV && (SCREEN_HEIGHT >= 812 || SCREEN_WIDTH >= 812);

/**
 * Scale a value horizontally based on screen width
 * @param {number} size - Size to scale
 * @returns {number} - Scaled size
 */
export const scaleWidth = (size) => {
  const scale = SCREEN_WIDTH / baseWidth;
  const newSize = size * scale;
  
  // Ensure the size is not too small on larger screens
  if (isTablet && newSize < size) {
    return size;
  }
  
  return Math.round(newSize);
};

/**
 * Scale a value vertically based on screen height
 * @param {number} size - Size to scale
 * @returns {number} - Scaled size
 */
export const scaleHeight = (size) => {
  const scale = SCREEN_HEIGHT / baseHeight;
  const newSize = size * scale;
  
  // Ensure the size is not too small on larger screens
  if (isTablet && newSize < size) {
    return size;
  }
  
  return Math.round(newSize);
};

/**
 * Scale a value moderately for font sizes
 * @param {number} size - Size to scale
 * @returns {number} - Scaled size
 */
export const scaleFontSize = (size) => {
  const scale = Math.min(SCREEN_WIDTH / baseWidth, SCREEN_HEIGHT / baseHeight);
  
  // Use a more moderate scaling factor for fonts
  const moderateScale = 0.5;
  const factor = moderateScale * (scale - 1) + 1;
  
  return Math.round(size * factor);
};

/**
 * Get platform-specific styles
 * @param {Object} iosStyles - iOS specific styles
 * @param {Object} androidStyles - Android specific styles
 * @returns {Object} - Platform-specific styles
 */
export const platformStyles = (iosStyles = {}, androidStyles = {}) => {
  return Platform.select({
    ios: iosStyles,
    android: androidStyles,
  });
};

/**
 * Get status bar height
 * @returns {number} - Status bar height
 */
export const getStatusBarHeight = () => {
  if (Platform.OS === 'android') {
    return StatusBar.currentHeight || 0;
  }
  
  // For iOS, use the notch detection
  return hasNotch ? 44 : 20;
};

/**
 * Get navigation bar height (bottom bar on iOS, on-screen buttons on Android)
 * @returns {number} - Navigation bar height
 */
export const getNavigationBarHeight = () => {
  // This is an approximation, as it's difficult to get the exact height
  if (Platform.OS === 'android') {
    return 48; // Approximate height for Android navigation bar
  }
  
  return hasNotch ? 34 : 0; // Home indicator height on iOS
};

/**
 * Get device info for debugging
 * @returns {Object} - Device information
 */
export const getDeviceInfo = () => {
  return {
    screenWidth: SCREEN_WIDTH,
    screenHeight: SCREEN_HEIGHT,
    pixelRatio,
    isTablet,
    hasNotch,
    platform: Platform.OS,
    platformVersion: Platform.Version,
  };
};

/**
 * Hook to get responsive values with safe area insets
 * @returns {Object} - Responsive values and safe area insets
 */
export const useResponsiveValues = () => {
  const insets = useSafeAreaInsets();
  
  return {
    screenWidth: SCREEN_WIDTH,
    screenHeight: SCREEN_HEIGHT,
    scaleWidth,
    scaleHeight,
    scaleFontSize,
    insets,
    isTablet,
    hasNotch,
  };
};

export default {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  scaleWidth,
  scaleHeight,
  scaleFontSize,
  platformStyles,
  getStatusBarHeight,
  getNavigationBarHeight,
  getDeviceInfo,
  useResponsiveValues,
  isTablet,
  hasNotch,
};
