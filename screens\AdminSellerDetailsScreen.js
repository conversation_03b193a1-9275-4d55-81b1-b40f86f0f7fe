import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Alert,
    TextInput,
    Modal,
    ActivityIndicator,
    RefreshControl,
    ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase.config';
import {
    collection,
    query,
    where,
    getDocs,
    orderBy,
    limit,
    doc,
    onSnapshot,
    getDoc
} from 'firebase/firestore';
import SafeAreaWrapper, { SafeAreaHeader } from '../components/SafeAreaWrapper';
import { checkAdminStatus } from '../utils/adminUtils';

const AdminSellerDetailsScreen = ({ navigation }) => {
    const [isAdmin, setIsAdmin] = useState(false);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [sellers, setSellers] = useState([]);
    const [filteredSellers, setFilteredSellers] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedSeller, setSelectedSeller] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);

    useEffect(() => {
        checkAdminAccess();
    }, []);

    useEffect(() => {
        if (isAdmin) {
            const unsubscribe = setupRealtimeListener();
            return () => unsubscribe && unsubscribe();
        }
    }, [isAdmin]);

    useEffect(() => {
        filterSellers();
    }, [sellers, searchQuery]);

    const checkAdminAccess = async () => {
        try {
            console.log('[AdminSellerDetails] Checking admin status...');
            // Pass the current user's UID to checkAdminStatus
            const adminStatus = await checkAdminStatus(auth.currentUser?.uid);
            console.log('[AdminSellerDetails] Admin status result:', adminStatus);

            if (!adminStatus) {
                console.log('[AdminSellerDetails] User is not an admin');
                Alert.alert('Access Denied', 'You do not have admin privileges.');
                navigation.goBack();
                return;
            }

            console.log('[AdminSellerDetails] Admin access granted');
            setIsAdmin(true);
        } catch (error) {
            console.error('[AdminSellerDetails] Error checking admin status:', error);
            Alert.alert('Error', 'Failed to verify admin status: ' + error.message);
            navigation.goBack();
        } finally {
            setLoading(false);
        }
    };

    const setupRealtimeListener = () => {
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('isSeller', '==', true), orderBy('name', 'asc'));

        const unsubscribe = onSnapshot(q, async (snapshot) => {
            try {
                const sellersData = snapshot.docs.map(docSnapshot => ({
                    id: docSnapshot.id,
                    ...docSnapshot.data()
                }));
                setSellers(sellersData);
            } catch (error) {
                console.error('Error processing seller updates:', error);
            }
        }, (error) => {
            console.error('Error listening to sellers:', error);
        });

        return unsubscribe;
    };

    const filterSellers = () => {
        let filtered = sellers;

        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase().trim();
            filtered = filtered.filter(seller =>
                (seller.name && seller.name.toLowerCase().includes(query)) ||
                (seller.email && seller.email.toLowerCase().includes(query)) ||
                (seller.phoneNumber && seller.phoneNumber.toLowerCase().includes(query))
            );
        }

        setFilteredSellers(filtered);
    };

    const onRefresh = async () => {
        setRefreshing(true);
        // The real-time listener will automatically update the data
        setTimeout(() => setRefreshing(false), 1000);
    };

    const formatDate = (timestamp) => {
        if (!timestamp) return 'N/A';
        try {
            const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        } catch (error) {
            return 'Invalid Date';
        }
    };

    const formatAddress = (address) => {
        if (!address) return 'N/A';
        const parts = [];
        if (address.street) parts.push(address.street);
        if (address.city) parts.push(address.city);
        if (address.state) parts.push(address.state);
        if (address.zipCode) parts.push(address.zipCode);
        if (address.country) parts.push(address.country);
        return parts.join(', ') || 'N/A';
    };

    const renderSellerItem = ({ item }) => (
        <TouchableOpacity
            style={styles.sellerCard}
            onPress={() => {
                setSelectedSeller(item);
                setModalVisible(true);
            }}
        >
            <View style={styles.sellerHeader}>
                <View style={styles.sellerInfo}>
                    <Text style={styles.sellerName}>{item.name || 'No Name'}</Text>
                    <Text style={styles.sellerEmail}>{item.email}</Text>
                </View>
                <View style={styles.statusContainer}>
                    <View style={[styles.statusBadge, {
                        backgroundColor: item.sellerVerificationStatus === 'verified' ? '#4CAF50' : '#FF9800'
                    }]}>
                        <Ionicons
                            name={item.sellerVerificationStatus === 'verified' ? 'checkmark-circle' : 'time'}
                            size={16}
                            color="#fff"
                        />
                        <Text style={styles.statusText}>
                            {item.sellerVerificationStatus === 'verified' ? 'Verified' : 'Pending'}
                        </Text>
                    </View>
                </View>
            </View>

            <View style={styles.sellerDetails}>
                <Text style={styles.detailText}>Phone: {item.phoneNumber || 'N/A'}</Text>
                <Text style={styles.detailText}>Bank Account: {item.bankAccount ? 'Added' : 'Not Added'}</Text>
                <Text style={styles.detailText}>Joined: {formatDate(item.createdAt)}</Text>
            </View>
        </TouchableOpacity>
    );

    if (loading) {
        return (
            <SafeAreaWrapper>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#FF6B6B" />
                    <Text style={styles.loadingText}>Loading seller details...</Text>
                </View>
            </SafeAreaWrapper>
        );
    }

    if (!isAdmin) {
        return (
            <SafeAreaWrapper>
                <View style={styles.errorContainer}>
                    <Ionicons name="shield-outline" size={60} color="#FF6B6B" />
                    <Text style={styles.errorTitle}>Access Denied</Text>
                    <Text style={styles.errorText}>You don't have permission to view this page.</Text>
                </View>
            </SafeAreaWrapper>
        );
    }

    return (
        <SafeAreaWrapper>
            <SafeAreaHeader>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <Ionicons name="arrow-back" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Seller Details</Text>
                <View style={styles.backButton} />
            </SafeAreaHeader>

            {/* Search Bar */}
            <View style={styles.searchContainer}>
                <Ionicons name="search-outline" size={20} color="#666" style={styles.searchIcon} />
                <TextInput
                    style={styles.searchInput}
                    placeholder="Search by name, email, or phone..."
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholderTextColor="#999"
                />
                {searchQuery.length > 0 && (
                    <TouchableOpacity onPress={() => setSearchQuery('')}>
                        <Ionicons name="close-circle" size={20} color="#666" />
                    </TouchableOpacity>
                )}
            </View>

            {/* Statistics Cards */}
            <View style={styles.statsContainer}>
                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{sellers.length}</Text>
                    <Text style={styles.statLabel}>Total Sellers</Text>
                </View>
                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>
                        {sellers.filter(s => s.sellerVerificationStatus === 'verified').length}
                    </Text>
                    <Text style={styles.statLabel}>Verified</Text>
                </View>
                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>
                        {sellers.filter(s => s.bankAccount).length}
                    </Text>
                    <Text style={styles.statLabel}>With Bank Details</Text>
                </View>
            </View>

            {/* Sellers List */}
            <FlatList
                data={filteredSellers}
                renderItem={renderSellerItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Ionicons name="storefront-outline" size={80} color="#ccc" />
                        <Text style={styles.emptyText}>No sellers found</Text>
                        <Text style={styles.emptySubtext}>
                            {searchQuery ? 'Try adjusting your search criteria' : 'No sellers have registered yet'}
                        </Text>
                    </View>
                }
            />

            {/* Seller Details Modal */}
            <Modal
                visible={modalVisible}
                animationType="slide"
                presentationStyle="pageSheet"
                onRequestClose={() => setModalVisible(false)}
            >
                <SafeAreaWrapper>
                    <SafeAreaHeader>
                        <TouchableOpacity
                            style={styles.backButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Ionicons name="close" size={24} color="#333" />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle}>Seller Details</Text>
                        <View style={styles.backButton} />
                    </SafeAreaHeader>

                    {selectedSeller && (
                        <ScrollView style={styles.modalContent}>
                            {/* Personal Information */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Personal Information</Text>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Name:</Text>
                                    <Text style={styles.modalValue}>{selectedSeller.name || 'N/A'}</Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Email:</Text>
                                    <Text style={styles.modalValue}>{selectedSeller.email}</Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Phone:</Text>
                                    <Text style={styles.modalValue}>{selectedSeller.phoneNumber || 'N/A'}</Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Website:</Text>
                                    <Text style={styles.modalValue}>{selectedSeller.website || 'N/A'}</Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Verification Status:</Text>
                                    <Text style={[styles.modalValue, {
                                        color: selectedSeller.sellerVerificationStatus === 'verified' ? '#4CAF50' : '#FF9800',
                                        fontWeight: 'bold'
                                    }]}>
                                        {selectedSeller.sellerVerificationStatus === 'verified' ? 'Verified' : 'Pending'}
                                    </Text>
                                </View>
                            </View>

                            {/* Address */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Address</Text>
                                {selectedSeller.address ? (
                                    <>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Street:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.address.street || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>City:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.address.city || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>State:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.address.state || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Country:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.address.country || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Zip Code:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.address.zipCode || 'N/A'}</Text>
                                        </View>
                                    </>
                                ) : (
                                    <Text style={styles.modalValue}>No address information available</Text>
                                )}
                            </View>

                            {/* Bank Account Details */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Bank Account Details</Text>
                                {selectedSeller.bankAccount ? (
                                    <>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Account Holder Name:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.bankAccount.accountHolderName || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Account Number:</Text>
                                            <Text style={[styles.modalValue, styles.accountNumber]}>
                                                {selectedSeller.bankAccount.accountNumber || 'N/A'}
                                            </Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>IFSC Code:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.bankAccount.ifscCode || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Bank Name:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.bankAccount.bankName || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Branch Name:</Text>
                                            <Text style={styles.modalValue}>{selectedSeller.bankAccount.branchName || 'N/A'}</Text>
                                        </View>
                                        <View style={styles.modalRow}>
                                            <Text style={styles.modalLabel}>Updated:</Text>
                                            <Text style={styles.modalValue}>{formatDate(selectedSeller.bankAccount.updatedAt)}</Text>
                                        </View>
                                    </>
                                ) : (
                                    <View style={styles.noBankAccount}>
                                        <Ionicons name="card-outline" size={40} color="#ccc" />
                                        <Text style={styles.noBankAccountText}>No bank account details added</Text>
                                    </View>
                                )}
                            </View>

                            {/* Account Info */}
                            <View style={styles.modalSection}>
                                <Text style={styles.modalSectionTitle}>Account Information</Text>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>User ID:</Text>
                                    <Text style={[styles.modalValue, styles.smallText]}>{selectedSeller.id}</Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Email Verified:</Text>
                                    <Text style={[styles.modalValue, {
                                        color: selectedSeller.emailVerified ? '#4CAF50' : '#f44336'
                                    }]}>
                                        {selectedSeller.emailVerified ? 'Yes' : 'No'}
                                    </Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Joined:</Text>
                                    <Text style={styles.modalValue}>{formatDate(selectedSeller.createdAt)}</Text>
                                </View>
                                <View style={styles.modalRow}>
                                    <Text style={styles.modalLabel}>Last Updated:</Text>
                                    <Text style={styles.modalValue}>{formatDate(selectedSeller.updatedAt)}</Text>
                                </View>
                            </View>
                        </ScrollView>
                    )}
                </SafeAreaWrapper>
            </Modal>
        </SafeAreaWrapper>
    );
};

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#666',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
        padding: 20,
    },
    errorTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
        marginTop: 16,
        marginBottom: 8,
    },
    errorText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
    },
    backButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        textAlign: 'center',
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
        borderRadius: 10,
        marginHorizontal: 16,
        marginVertical: 10,
        paddingHorizontal: 12,
        height: 44,
    },
    searchIcon: {
        marginRight: 8,
    },
    searchInput: {
        flex: 1,
        height: 44,
        fontSize: 16,
        color: '#333',
    },
    statsContainer: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        marginBottom: 10,
    },
    statCard: {
        flex: 1,
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginHorizontal: 4,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    statNumber: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#FF6B6B',
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
    listContainer: {
        padding: 16,
    },
    sellerCard: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    sellerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    sellerInfo: {
        flex: 1,
    },
    sellerName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },
    sellerEmail: {
        fontSize: 14,
        color: '#666',
    },
    statusContainer: {
        alignItems: 'flex-end',
    },
    statusBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        marginLeft: 4,
    },
    sellerDetails: {
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
        paddingTop: 12,
    },
    detailText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 4,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#666',
        marginTop: 16,
        marginBottom: 8,
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        textAlign: 'center',
        paddingHorizontal: 40,
    },
    modalContent: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    modalSection: {
        backgroundColor: '#fff',
        marginHorizontal: 16,
        marginVertical: 8,
        borderRadius: 12,
        padding: 16,
    },
    modalSectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
        paddingBottom: 8,
    },
    modalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    modalLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        flex: 1,
    },
    modalValue: {
        fontSize: 14,
        color: '#666',
        flex: 2,
        textAlign: 'right',
    },
    accountNumber: {
        fontFamily: 'monospace',
        fontWeight: 'bold',
    },
    noBankAccount: {
        alignItems: 'center',
        paddingVertical: 20,
    },
    noBankAccountText: {
        fontSize: 16,
        color: '#999',
        marginTop: 12,
    },
    smallText: {
        fontSize: 12,
    },
});

export default AdminSellerDetailsScreen;
