// Quick test to verify receipt length
const testReceiptLength = () => {
  const userId = "test_uid_123456789012345678901234567890"; // Sample long UID
  const timestamp = Date.now();
  
  // Old format (would be too long)
  const oldReceipt = `receipt_${userId}_${timestamp}`;
  console.log('Old receipt format:', oldReceipt);
  console.log('Old receipt length:', oldReceipt.length);
  
  // New format (should be under 40 chars)
  const newReceipt = `rcpt_${timestamp.toString().slice(-8)}_${userId.slice(-6)}`;
  console.log('New receipt format:', newReceipt);
  console.log('New receipt length:', newReceipt.length);
  
  console.log('✅ Receipt length is within Razorpay limit (40 chars):', newReceipt.length <= 40);
};

testReceiptLength();
