import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getStockStatus } from '../utils/stockUtils';

/**
 * Stock Display Component
 * Shows stock status to buyers on product cards and detail pages
 */
const StockDisplay = ({
    stock = 0,
    variant = 'default', // 'default', 'compact', 'detailed'
    showIcon = true,
    style,
    onPress
}) => {
    const stockInfo = getStockStatus(stock);

    const renderDefault = () => (
        <View style={[styles.container, style]}>
            <View style={[styles.statusBadge, { backgroundColor: stockInfo.color }]}>
                {showIcon && (
                    <Ionicons
                        name={stockInfo.available ? "checkmark-circle" : "close-circle"}
                        size={14}
                        color="#fff"
                        style={styles.icon}
                    />
                )}
                <Text style={styles.statusText}>{stockInfo.label}</Text>
            </View>
        </View>
    );

    const renderCompact = () => (
        <View style={[styles.compactContainer, style]}>
            <View style={[styles.compactDot, { backgroundColor: stockInfo.color }]} />
            <Text style={[styles.compactText, { color: stockInfo.color }]}>
                {stockInfo.label}
            </Text>
        </View>
    );

    const renderDetailed = () => (
        <TouchableOpacity
            style={[styles.detailedContainer, style]}
            onPress={onPress}
            disabled={!onPress}
        >
            <View style={styles.detailedHeader}>
                <View style={[styles.statusIndicator, { backgroundColor: stockInfo.color }]} />
                <Text style={styles.detailedTitle}>Stock Status</Text>
            </View>

            <Text style={[styles.detailedStatus, { color: stockInfo.color }]}>
                {stockInfo.label}
            </Text>

            {stockInfo.available && stock > 0 && (
                <Text style={styles.stockCount}>
                    {stock} {stock === 1 ? 'item' : 'items'} available
                </Text>
            )}

            {stockInfo.showWarning && (
                <View style={styles.warningContainer}>
                    <Ionicons name="warning" size={14} color="#FF9500" />
                    <Text style={styles.warningText}>Limited stock - order soon!</Text>
                </View>
            )}

            {!stockInfo.available && (
                <View style={styles.outOfStockContainer}>
                    <Ionicons name="alert-circle" size={14} color="#FF3B30" />
                    <Text style={styles.outOfStockText}>Currently unavailable</Text>
                </View>
            )}
        </TouchableOpacity>
    );

    switch (variant) {
        case 'compact':
            return renderCompact();
        case 'detailed':
            return renderDetailed();
        default:
            return renderDefault();
    }
};

/**
 * Stock Badge Component
 * Simple badge for showing stock status on product cards
 */
export const StockBadge = ({ stock, style }) => {
    const stockInfo = getStockStatus(stock);

    if (stockInfo.available && !stockInfo.showWarning) {
        return null; // Don't show badge for normal stock levels
    }

    return (
        <View style={[styles.badge, { backgroundColor: stockInfo.color }, style]}>
            <Text style={styles.badgeText}>
                {stockInfo.status === 'out_of_stock' ? 'OUT OF STOCK' : `${stock} LEFT`}
            </Text>
        </View>
    );
};

/**
 * Stock Warning Component
 * Shows warning when stock is low during checkout
 */
export const StockWarning = ({ items, style }) => {
    const lowStockItems = items.filter(item => {
        const stockInfo = getStockStatus(item.stock || 0);
        return stockInfo.showWarning || !stockInfo.available;
    });

    if (lowStockItems.length === 0) {
        return null;
    }

    return (
        <View style={[styles.warningContainer, styles.checkoutWarning, style]}>
            <Ionicons name="warning" size={20} color="#FF9500" />
            <View style={styles.warningContent}>
                <Text style={styles.warningTitle}>Stock Alert</Text>
                {lowStockItems.map((item, index) => {
                    const stockInfo = getStockStatus(item.stock || 0);
                    return (
                        <Text key={index} style={styles.warningItemText}>
                            • {item.title}: {stockInfo.label}
                        </Text>
                    );
                })}
                <Text style={styles.warningFooter}>
                    Complete your order quickly to secure these items.
                </Text>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: 'flex-start',
    },
    statusBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    icon: {
        marginRight: 4,
    },
    statusText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    compactContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    compactDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginRight: 6,
    },
    compactText: {
        fontSize: 12,
        fontWeight: '500',
    },
    detailedContainer: {
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        padding: 12,
        borderWidth: 1,
        borderColor: 'rgba(0,0,0,0.08)',
    },
    detailedHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    statusIndicator: {
        width: 12,
        height: 12,
        borderRadius: 6,
        marginRight: 8,
    },
    detailedTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
    },
    detailedStatus: {
        fontSize: 16,
        fontWeight: '700',
        marginBottom: 4,
    },
    stockCount: {
        fontSize: 12,
        color: '#666',
        marginBottom: 8,
    },
    warningContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF3CD',
        padding: 8,
        borderRadius: 6,
        marginTop: 4,
    },
    warningText: {
        fontSize: 12,
        color: '#856404',
        marginLeft: 4,
        fontWeight: '500',
    },
    outOfStockContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F8D7DA',
        padding: 8,
        borderRadius: 6,
        marginTop: 4,
    },
    outOfStockText: {
        fontSize: 12,
        color: '#721C24',
        marginLeft: 4,
        fontWeight: '500',
    },
    badge: {
        position: 'absolute',
        top: 8,
        right: 8,
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 4,
        zIndex: 1,
    },
    badgeText: {
        color: '#fff',
        fontSize: 10,
        fontWeight: '700',
    },
    checkoutWarning: {
        backgroundColor: '#FFF3CD',
        borderLeftWidth: 4,
        borderLeftColor: '#FF9500',
        padding: 16,
        margin: 16,
        borderRadius: 8,
    },
    warningContent: {
        flex: 1,
        marginLeft: 12,
    },
    warningTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#856404',
        marginBottom: 8,
    },
    warningItemText: {
        fontSize: 14,
        color: '#856404',
        marginBottom: 4,
    },
    warningFooter: {
        fontSize: 12,
        color: '#856404',
        fontStyle: 'italic',
        marginTop: 8,
    },
});

export default StockDisplay;