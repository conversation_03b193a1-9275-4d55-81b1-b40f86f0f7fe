# Transaction Management System Documentation

## Overview
This comprehensive transaction management system handles the flow of funds from completed orders to seller payments, with full admin control and transparency.

## Components

### 1. Transaction Utilities (`utils/transactionUtils.js`)
Contains core functions for transaction management:

#### `createSellerTransactionsFromOrder(orderId)`
- Automatically creates seller transactions when orders are completed
- Calculates platform fees (currently 0%)
- Generates unique transaction numbers
- Stores detailed transaction records in `sellerTransactions` collection

#### `markTransactionAsCompleted(transactionId, transferDetails, adminNotes)`
- Marks transactions as completed when admin transfers funds
- Records transfer details (method, reference number, bank name, etc.)
- Updates transaction status and timestamp
- Can trigger notifications to sellers

### 2. Seller Transaction Screen (`screens/SellerTransactionsScreen.js`)
Sellers can:
- View all their transactions with full details
- See transaction status (pending, processing, completed, failed)
- Track earnings (total, received, pending)
- View order and buyer information for each transaction
- Access detailed transaction information in a modal
- See when payments were received in their bank accounts

### 3. Admin Transaction Management (`screens/AdminTransactionManagementScreen.js`)
Admins can:
- View all seller transactions across the platform
- Filter transactions by status and search by seller/order
- Mark transactions as completed with transfer details
- Add admin notes to transactions
- Quick action buttons for common operations
- Real-time updates of transaction statuses

### 4. Order Integration (`utils/orderUtils.js`)
#### `completeOrder(orderId)`
- Marks orders as completed
- Automatically creates seller transactions
- Ensures proper order status flow (delivered → completed)

## Database Collections

### `sellerTransactions`
```javascript
{
  id: "transaction-id",
  sellerId: "seller-user-id",
  buyerId: "buyer-user-id", 
  orderId: "order-id",  amount: 1000.50,  // Net amount (same as gross since no platform fee)
  platformFee: 0.00,
  grossAmount: 1000.50,
  status: "pending", // pending, processing, completed, failed
  transactionNumber: "TXN-2025-001234",
  description: "Payment for order items",
  adminNotes: "",
  transactionDetails: {
    method: "Bank Transfer",
    referenceNumber: "REF123456789",
    bankName: "Example Bank",
    transferredBy: "admin-user-id",
    transferredAt: "2025-06-12T10:30:00Z"
  },
  createdAt: "2025-06-12T08:00:00Z",
  updatedAt: "2025-06-12T10:30:00Z",
  transferredAt: "2025-06-12T10:30:00Z"
}
```

## Workflow

### 1. Order Completion
1. Order is delivered to customer
2. Admin or system calls `completeOrder(orderId)`
3. Order status changes to "completed"
4. `createSellerTransactionsFromOrder()` is automatically called
5. Seller transactions are created for each seller in the order

### 2. Payment Processing
1. Admin views pending transactions in AdminTransactionManagementScreen
2. Admin initiates bank transfer to seller
3. Admin marks transaction as completed using:
   - Quick action button (for simple completion)
   - Detailed modal (for adding transfer details)
4. Transaction status updates to "completed"
5. Transfer timestamp is recorded
6. Seller can see the payment in their transaction history

### 3. Seller Experience
1. Seller receives order and ships item
2. Once order is completed, transaction appears in their dashboard
3. Seller can track transaction status
4. When admin processes payment, seller sees "completed" status
5. Seller has full details of the transfer for their records

## Configuration

### Platform Fee
Default: 0% (no platform fee currently)
```javascript
const platformFeeRate = 0;
```

### Transaction Numbering
Format: `TXN-YYYY-NNNNNN`
- Year-based numbering
- Sequential counter per year
- Stored in `platformSettings` collection

## Security

### Firestore Rules
```javascript
// Allow sellers to read their own transactions
match /sellerTransactions/{transactionId} {
  allow read: if request.auth != null && 
    (resource.data.sellerId == request.auth.uid || 
     isAdmin(request.auth.uid));
  allow write: if isAdmin(request.auth.uid);
}
```

## Error Handling
- All functions include comprehensive error handling
- User-friendly error messages
- Detailed logging for debugging
- Rollback mechanisms for critical operations

## Future Enhancements
- Automated payment processing integration
- Bulk payment processing
- Seller payment schedule configuration
- Advanced reporting and analytics
- Email notifications for status changes
- Integration with accounting systems

## Usage Examples

### Creating Transactions (Order Completion)
```javascript
import { completeOrder } from './utils/orderUtils';

// When an order is delivered and ready to be completed
await completeOrder('order-12345');
```

### Admin Processing Payment
```javascript
import { markTransactionAsCompleted } from './utils/transactionUtils';

const transferDetails = {
  method: 'Bank Transfer',
  referenceNumber: 'TXN123456789',
  bankName: 'HDFC Bank',
  transferredBy: 'admin-user-id',
  transferredAt: new Date()
};

await markTransactionAsCompleted(
  'transaction-id', 
  transferDetails, 
  'Payment processed successfully'
);
```

## Testing
Run the test suite:
```javascript
import { runTransactionTests } from './test-transaction-system';
runTransactionTests();
```

This system provides complete transparency and control over the financial flow between buyers, the platform, and sellers, ensuring trust and proper record-keeping for all parties involved.
