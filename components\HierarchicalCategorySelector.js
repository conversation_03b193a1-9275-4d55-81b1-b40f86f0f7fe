import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Modal,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  getBroadCategories,
  getDetailedCategories,
  validateCategorySelection,
  searchCategories
} from '../utils/categoryHierarchy';

const HierarchicalCategorySelector = ({
  selectedBroadCategory,
  selectedDetailedCategory,
  selectedBroadCategories = [], // For multi-select
  selectedDetailedCategories = [], // For multi-select
  onBroadCategorySelect,
  onDetailedCategorySelect,
  onBroadCategoriesSelect, // For multi-select
  onDetailedCategoriesSelect, // For multi-select
  multiSelect = false,
  disabled = false,
  style
}) => {
  const [broadCategories] = useState(getBroadCategories());
  const [detailedCategories, setDetailedCategories] = useState([]);
  const [showBroadModal, setShowBroadModal] = useState(false);
  const [showDetailedModal, setShowDetailedModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredBroadCategories, setFilteredBroadCategories] = useState(broadCategories);
  const [filteredDetailedCategories, setFilteredDetailedCategories] = useState([]);

  // Update detailed categories when broad category changes
  useEffect(() => {
    if (selectedBroadCategory) {
      const detailed = getDetailedCategories(selectedBroadCategory);
      setDetailedCategories(detailed);
      setFilteredDetailedCategories(detailed);
    } else {
      setDetailedCategories([]);
      setFilteredDetailedCategories([]);
    }
  }, [selectedBroadCategory]);

  // Filter broad categories based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = broadCategories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredBroadCategories(filtered);
    } else {
      setFilteredBroadCategories(broadCategories);
    }
  }, [searchQuery, broadCategories]);

  // Filter detailed categories based on search
  useEffect(() => {
    if (searchQuery.trim() && detailedCategories.length > 0) {
      const filtered = detailedCategories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDetailedCategories(filtered);
    } else {
      setFilteredDetailedCategories(detailedCategories);
    }
  }, [searchQuery, detailedCategories]);

  const handleBroadCategorySelect = (category) => {
    onBroadCategorySelect(category);
    // Clear detailed category when broad category changes
    onDetailedCategorySelect('');
    setShowBroadModal(false);
    setSearchQuery('');
  };

  const handleDetailedCategorySelect = (category) => {
    onDetailedCategorySelect(category);
    setShowDetailedModal(false);
    setSearchQuery('');
  };

  const openBroadModal = () => {
    if (!disabled) {
      setSearchQuery('');
      setShowBroadModal(true);
    }
  };

  const openDetailedModal = () => {
    if (!disabled && selectedBroadCategory) {
      setSearchQuery('');
      setShowDetailedModal(true);
    }
  };

  const renderCategoryModal = (
    visible,
    onClose,
    categories,
    onSelect,
    title,
    selectedValue
  ) => (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />            <TextInput
              style={styles.searchInput}
              placeholder="Search categories..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              placeholderTextColor="#000"
            />
          </View>

          <ScrollView style={styles.categoriesList}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryItem,
                  selectedValue === category && styles.selectedCategoryItem
                ]}
                onPress={() => onSelect(category)}
              >
                <Text
                  style={[
                    styles.categoryItemText,
                    selectedValue === category && styles.selectedCategoryItemText
                  ]}
                >
                  {category ? String(category) : ''}
                </Text>
                {selectedValue === category && (
                  <Ionicons name="checkmark" size={20} color="#FF6B6B" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Broad Category Selector */}
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Category Type</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            disabled && styles.selectorDisabled,
            !selectedBroadCategory && styles.selectorPlaceholder
          ]}
          onPress={openBroadModal}
          disabled={disabled}
        >
          <Text
            style={[
              styles.selectorText,
              !selectedBroadCategory && styles.selectorPlaceholderText
            ]}
          >
            {selectedBroadCategory ? String(selectedBroadCategory) : 'Select category type'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Detailed Category Selector */}
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Specific Category</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            (disabled || !selectedBroadCategory) && styles.selectorDisabled,
            !selectedDetailedCategory && styles.selectorPlaceholder
          ]}
          onPress={openDetailedModal}
          disabled={disabled || !selectedBroadCategory}
        >
          <Text
            style={[
              styles.selectorText,
              !selectedDetailedCategory && styles.selectorPlaceholderText
            ]}
          >
            {selectedDetailedCategory ? String(selectedDetailedCategory) :
             (selectedBroadCategory ? 'Select specific category' : 'Select category type first')}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Broad Category Modal */}
      {renderCategoryModal(
        showBroadModal,
        () => setShowBroadModal(false),
        filteredBroadCategories,
        handleBroadCategorySelect,
        'Select Category Type',
        selectedBroadCategory
      )}

      {/* Detailed Category Modal */}
      {renderCategoryModal(
        showDetailedModal,
        () => setShowDetailedModal(false),
        filteredDetailedCategories,
        handleDetailedCategorySelect,
        'Select Specific Category',
        selectedDetailedCategory
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  selectorContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  selectorDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  selectorPlaceholder: {
    borderColor: '#ccc',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectorPlaceholderText: {
    color: '#999',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  categoriesList: {
    maxHeight: 400,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedCategoryItem: {
    backgroundColor: '#fff5f5',
  },
  categoryItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedCategoryItemText: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
});

export default HierarchicalCategorySelector;
