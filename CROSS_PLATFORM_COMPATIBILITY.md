# Cross-Platform Compatibility Guide

This document provides information on how to ensure your app works well across different Android and iOS devices.

## Table of Contents

1. [Overview](#overview)
2. [Responsive Design Utilities](#responsive-design-utilities)
3. [Platform-Specific Styling](#platform-specific-styling)
4. [Testing on Different Devices](#testing-on-different-devices)
5. [Common Issues and Solutions](#common-issues-and-solutions)

## Overview

The app has been refactored to ensure compatibility across:
- Various Android screen sizes (from small to large devices)
- Older Android phones with different aspect ratios and resolutions
- Different iOS devices beyond just iPhone 16 Pro

Key improvements include:
- Responsive layouts using flexible dimensions instead of fixed pixel values
- Platform-specific UI adjustments for Android and iOS
- Better handling of safe area insets and keyboard behavior
- Optimized animations and transitions for lower-end devices

## Responsive Design Utilities

### Responsive Utilities (`utils/responsiveUtils.js`)

This utility provides functions to scale dimensions based on screen size:

```javascript
// Scale width based on screen size
const width = scaleWidth(100);

// Scale height based on screen size
const height = scaleHeight(50);

// Scale font size with more moderate scaling
const fontSize = scaleFontSize(16);
```

### Platform-Specific Styling (`utils/platformUtils.js`)

This utility provides functions for platform-specific styling:

```javascript
// Create platform-specific shadows
const shadowStyle = createShadow({
  elevation: 5,          // Android elevation
  color: '#000',         // Shadow color
  opacity: 0.2,          // Shadow opacity
  radius: 3,             // Shadow radius
  offset: { width: 0, height: 2 } // Shadow offset
});

// Create platform-specific text styles
const textStyle = createTextStyle({
  fontWeight: '600',
  letterSpacing: 0.5
});
```

### Theme System (`utils/theme.js`)

The theme system provides consistent styling across the app:

```javascript
// Use theme colors
const backgroundColor = theme.colors.background;

// Use theme spacing
const padding = theme.spacing.md;

// Use theme typography
const fontSize = theme.typography.fontSizes.md;
```

## Testing on Different Devices

### Android Emulator Setup

1. **Launch Android Emulator from VS Code**:
   - Install the "Android iOS Emulator" extension in VS Code
   - Use the command palette (Ctrl+Shift+P) and select "Android iOS Emulator: Run Android Emulator"
   - Select a device from the list (try different API levels and screen sizes)

2. **Create Multiple AVDs for Testing**:
   - Open Android Studio > AVD Manager
   - Create the following test devices:
     - Small phone: Pixel 2 (5.0", 1080x1920, 420dpi)
     - Medium phone: Pixel 4 (5.7", 1080x2280, 440dpi)
     - Large phone: Pixel 6 Pro (6.7", 1440x3120, 560dpi)
     - Tablet: Pixel C (9.9", 2560x1800, 308dpi)
     - Older device: Nexus 5 with API 24 (Android 7.0)

3. **Run the App on Different Emulators**:
   ```bash
   npx expo run:android --device "Pixel_4_API_33"
   ```

### iOS Simulator Setup

1. **Launch iOS Simulator from VS Code**:
   - Use the command palette (Ctrl+Shift+P) and select "Android iOS Emulator: Run iOS Simulator"
   - Select a device from the list

2. **Test on Different iOS Devices**:
   - iPhone SE (2nd generation): Small screen
   - iPhone 13: Medium screen
   - iPhone 13 Pro Max: Large screen
   - iPad (9th generation): Tablet

3. **Run the App on Different Simulators**:
   ```bash
   npx expo run:ios --device "iPhone 13"
   ```

## Common Issues and Solutions

### Android-Specific Issues

1. **Status Bar Overlapping Content**:
   - Use `SafeAreaWrapper` component which handles status bar height
   - For manual adjustment: `paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0`

2. **Keyboard Handling**:
   - Use `KeyboardAvoidingView` with different behavior:
     ```javascript
     <KeyboardAvoidingView
       behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
       keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 30}
     >
     ```

3. **Elevation vs Shadow**:
   - Use the `createShadow` utility which handles both platforms:
     ```javascript
     ...createShadow({
       elevation: 5,
       opacity: 0.2,
       radius: 3,
     })
     ```

4. **Text Rendering Issues**:
   - Use the `ResponsiveText` component which applies platform-specific text optimizations

### iOS-Specific Issues

1. **Safe Area Insets**:
   - Use `SafeAreaWrapper` component which handles safe area insets
   - For manual adjustment: `paddingBottom: insets.bottom`

2. **Dynamic Island / Notch**:
   - The `SafeAreaWrapper` component handles this automatically

## Responsive Component Usage

The app now includes several responsive components:

1. **SafeAreaWrapper**: Handles safe area insets consistently
2. **ResponsiveText**: Scales text based on screen size
3. **ResponsiveButton**: Platform-optimized buttons
4. **ResponsiveInput**: Platform-optimized text inputs

Example usage:

```javascript
<SafeAreaWrapper>
  <ResponsiveText variant="h1">Hello World</ResponsiveText>
  <ResponsiveInput
    label="Username"
    value={username}
    onChangeText={setUsername}
  />
  <ResponsiveButton
    title="Submit"
    onPress={handleSubmit}
    variant="primary"
  />
</SafeAreaWrapper>
```
