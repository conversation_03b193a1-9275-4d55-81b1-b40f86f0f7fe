{"name": "swipesense", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "build:android": "npx react-native build-android --mode=debug", "build:apk": "cd android && gradlew.bat assembleDebug", "build:bundle": "npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res"}, "dependencies": {"@firebase/auth": "^1.10.0", "@firebase/firestore": "^4.7.10", "@firebase/functions": "^0.12.3", "@firebase/storage": "^0.13.7", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "6.5.11", "@react-navigation/native": "6.1.9", "@react-navigation/stack": "6.3.20", "expo": "^53.0.0", "expo-auth-session": "~6.1.5", "expo-dev-client": "~5.1.8", "expo-image-manipulator": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-modules-autolinking": "^2.1.10", "expo-modules-core": "^2.3.13", "expo-notifications": "^0.31.2", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.12", "expo-web-browser": "~14.1.6", "firebase": "^10.7.1", "lodash": "^4.17.21", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-native": "0.79.2", "react-native-deck-swiper": "^2.0.17", "react-native-dotenv": "^3.4.11", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.24.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^18.0.0", "metro-react-native-babel-preset": "^0.77.0", "rimraf": "^6.0.1"}, "private": true, "overrides": {"@firebase/auth": {"@react-native-async-storage/async-storage": "2.1.2"}}}