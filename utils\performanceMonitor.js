/**
 * Performance monitoring utility for tracking swipe gesture performance
 * and cart addition responsiveness
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.isEnabled = __DEV__; // Only enable in development
  }

  // Start timing a performance metric
  startTiming(metricName) {
    if (!this.isEnabled) return;
    
    this.metrics.set(metricName, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  // End timing and calculate duration
  endTiming(metricName) {
    if (!this.isEnabled) return;
    
    const metric = this.metrics.get(metricName);
    if (!metric) {
      console.warn(`[PerformanceMonitor] Metric ${metricName} was not started`);
      return;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    
    this.logMetric(metricName, metric.duration);
    return metric.duration;
  }

  // Log performance metric
  logMetric(metricName, duration) {
    if (!this.isEnabled) return;

    const threshold = this.getThreshold(metricName);
    const status = duration <= threshold ? '✅' : '⚠️';
    
    console.log(`[Performance] ${status} ${metricName}: ${duration.toFixed(2)}ms (threshold: ${threshold}ms)`);
    
    if (duration > threshold) {
      console.warn(`[Performance] ${metricName} exceeded performance threshold!`);
    }
  }

  // Get performance threshold for different metrics
  getThreshold(metricName) {
    const thresholds = {
      'swipe_gesture_detection': 16, // 60fps = 16.67ms per frame
      'cart_animation_trigger': 50,  // Animation should start within 50ms
      'local_state_update': 10,      // Local state updates should be very fast
      'ui_feedback_delay': 100,      // Total UI feedback delay
      'database_operation': 2000,    // Database operations (background)
      'swipe_completion_total': 150  // Total swipe completion time
    };
    
    return thresholds[metricName] || 100; // Default threshold
  }

  // Track swipe gesture performance
  trackSwipeGesture(direction, itemId) {
    if (!this.isEnabled) return;
    
    const metricName = `swipe_${direction}_${itemId}`;
    this.startTiming(metricName);
    
    return {
      endTracking: () => this.endTiming(metricName)
    };
  }

  // Track cart addition performance
  trackCartAddition(itemId) {
    if (!this.isEnabled) return;
    
    const metricName = `cart_addition_${itemId}`;
    this.startTiming(metricName);
    
    return {
      markAnimationTriggered: () => {
        this.endTiming(`${metricName}_animation`);
        this.startTiming(`${metricName}_database`);
      },
      markDatabaseComplete: () => {
        this.endTiming(`${metricName}_database`);
        this.endTiming(metricName);
      }
    };
  }

  // Get performance summary
  getPerformanceSummary() {
    if (!this.isEnabled) return null;
    
    const summary = {
      totalMetrics: this.metrics.size,
      averageDurations: {},
      slowestOperations: [],
      fastestOperations: []
    };

    const durations = Array.from(this.metrics.entries())
      .filter(([_, metric]) => metric.duration !== null)
      .map(([name, metric]) => ({ name, duration: metric.duration }));

    // Calculate averages by operation type
    const operationTypes = ['swipe', 'cart', 'animation', 'database'];
    operationTypes.forEach(type => {
      const typeDurations = durations.filter(d => d.name.includes(type));
      if (typeDurations.length > 0) {
        const average = typeDurations.reduce((sum, d) => sum + d.duration, 0) / typeDurations.length;
        summary.averageDurations[type] = average.toFixed(2);
      }
    });

    // Find slowest and fastest operations
    const sortedDurations = durations.sort((a, b) => b.duration - a.duration);
    summary.slowestOperations = sortedDurations.slice(0, 5);
    summary.fastestOperations = sortedDurations.slice(-5).reverse();

    return summary;
  }

  // Clear all metrics
  clearMetrics() {
    this.metrics.clear();
  }

  // Log performance summary
  logSummary() {
    if (!this.isEnabled) return;
    
    const summary = this.getPerformanceSummary();
    if (!summary) return;

    console.group('[Performance Summary]');
    console.log(`Total metrics tracked: ${summary.totalMetrics}`);
    
    console.group('Average durations by operation type:');
    Object.entries(summary.averageDurations).forEach(([type, avg]) => {
      console.log(`${type}: ${avg}ms`);
    });
    console.groupEnd();

    if (summary.slowestOperations.length > 0) {
      console.group('Slowest operations:');
      summary.slowestOperations.forEach(op => {
        console.log(`${op.name}: ${op.duration.toFixed(2)}ms`);
      });
      console.groupEnd();
    }

    console.groupEnd();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Export convenience functions
export const startTiming = (metricName) => performanceMonitor.startTiming(metricName);
export const endTiming = (metricName) => performanceMonitor.endTiming(metricName);
export const trackSwipeGesture = (direction, itemId) => performanceMonitor.trackSwipeGesture(direction, itemId);
export const trackCartAddition = (itemId) => performanceMonitor.trackCartAddition(itemId);
export const logPerformanceSummary = () => performanceMonitor.logSummary();
export const clearMetrics = () => performanceMonitor.clearMetrics();

export default performanceMonitor;
