import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { auth } from '../firebase.config';
import { FIREBASE_WEB_CLIENT_ID } from '@env';
import { signInWithEmailAndPassword, GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';

const AuthScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleEmailPasswordLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    try {
      await signInWithEmailAndPassword(auth, email, password);
      navigation.navigate('Home');
    } catch (error) {
      console.error('Login error:', error);

      // Provide user-friendly error messages based on error code
      let errorMessage = 'Failed to login. Please try again.';

      switch (error.code) {
        case 'auth/invalid-credential':
          errorMessage = 'The email or password you entered is incorrect. If you don\'t have an account yet, please sign up first.';
          break;
        case 'auth/user-not-found':
          errorMessage = 'No account exists with this email address. Please check your email or sign up for a new account.';
          break;
        case 'auth/wrong-password':
          errorMessage = 'The password you entered is incorrect. Please try again.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'auth/user-disabled':
          errorMessage = 'This account has been disabled. Please contact support for assistance.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Access to this account has been temporarily disabled due to many failed login attempts. You can try again later or reset your password.';
          break;
        case 'auth/network-request-failed':
          errorMessage = 'A network error occurred. Please check your internet connection and try again.';
          break;
        default:
          // Use the error message from Firebase if available
          if (error.message) {
            errorMessage = error.message;
          }
          break;
      }

      setError(errorMessage);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const response = await WebBrowser.openAuthSessionAsync(
        `https://accounts.google.com/o/oauth2/v2/auth?${new URLSearchParams({
          client_id: FIREBASE_WEB_CLIENT_ID,
          redirect_uri: AuthSession.makeRedirectUri({ useProxy: true }),
          response_type: 'id_token',
          scope: 'openid profile email',
        })}`,
        AuthSession.makeRedirectUri({ useProxy: true })
      );

      if (response.type === 'success') {
        const { params } = response;
        const credential = GoogleAuthProvider.credential(params.id_token);
        await signInWithCredential(auth, credential);
        navigation.navigate('Home');
      }
    } catch (error) {
      console.error('Google sign-in error:', error);

      // Provide user-friendly error messages for Google sign-in errors
      let errorMessage = 'Failed to sign in with Google. Please try again.';

      if (error.code) {
        switch (error.code) {
          case 'auth/invalid-credential':
            errorMessage = 'We couldn\'t verify your Google account. Please try again or use email login instead.';
            break;
          case 'auth/account-exists-with-different-credential':
            errorMessage = 'An account already exists with the same email address but different sign-in credentials. Try signing in with a different method.';
            break;
          case 'auth/popup-closed-by-user':
            errorMessage = 'The Google sign-in was cancelled. Please try again if you want to sign in with Google.';
            break;
          case 'auth/network-request-failed':
            errorMessage = 'A network error occurred. Please check your internet connection and try again.';
            break;
          default:
            if (error.message) {
              errorMessage = error.message;
            }
            break;
        }
      } else if (error.message && error.message.includes('popup')) {
        errorMessage = 'The Google sign-in window was closed. Please try again.';
      }

      setError(errorMessage);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>SwipeSense Login</Text>

      {error ? <Text style={styles.error}>{error}</Text> : null}

      <TextInput
        style={styles.input}
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <TextInput
        style={styles.input}
        placeholder="Password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />

      <TouchableOpacity
        style={styles.button}
        onPress={handleEmailPasswordLogin}
      >
        <Text style={styles.buttonText}>Login with Email</Text>
      </TouchableOpacity>



      <TouchableOpacity
        style={[styles.button, { backgroundColor: '#4285F4' }]}
        onPress={handleGoogleSignIn}
      >
        <Text style={styles.buttonText}>Continue with Google</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={() => navigation.navigate('SignUp')}>
        <Text style={styles.linkText}>Don't have an account? Sign up</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  input: {
    width: '100%',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 15,
    marginBottom: 15,
  },
  button: {
    width: '100%',
    backgroundColor: '#000',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 15,
  },

  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  error: {
    color: 'red',
    marginBottom: 15,
  },
  linkText: {
    color: '#4285F4',
    marginTop: 15,
  },
});

export default AuthScreen;