import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * Reusable Status Dropdown Component
 * Replaces horizontal scrollable filter buttons with a professional dropdown
 */
const StatusDropdown = ({
  options = [],
  selectedValue = 'all',
  onValueChange,
  label = 'Filter by Status:',
  placeholder = 'Select an option',
  style,
  disabled = false
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Helper function to get display name
  const getDisplayName = (value) => {
    const option = options.find(opt => opt.value === value);
    return option ? option.label : placeholder;
  };

  // Helper function to get icon
  const getIcon = (value) => {
    const option = options.find(opt => opt.value === value);
    return option?.icon || 'help-circle-outline';
  };

  // Helper function to get color
  const getColor = (value) => {
    const option = options.find(opt => opt.value === value);
    return option?.color || '#666';
  };

  // Render dropdown item
  const renderDropdownItem = (option) => (
    <TouchableOpacity
      key={option.value}
      style={[
        styles.dropdownItem,
        selectedValue === option.value && styles.dropdownItemSelected
      ]}
      onPress={() => {
        onValueChange(option.value);
        setDropdownVisible(false);
      }}
    >
      {option.icon && (
        <Ionicons 
          name={option.icon} 
          size={16} 
          color={option.color || '#666'} 
          style={styles.dropdownItemIcon}
        />
      )}
      <Text style={[
        styles.dropdownItemText,
        selectedValue === option.value && styles.dropdownItemTextSelected
      ]}>
        {option.label}
      </Text>
      {selectedValue === option.value && (
        <Ionicons name="checkmark" size={16} color="#FF6B6B" />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={styles.dropdownContainer}>
        <TouchableOpacity
          style={[
            styles.dropdownButton,
            disabled && styles.dropdownButtonDisabled
          ]}
          onPress={() => !disabled && setDropdownVisible(!dropdownVisible)}
          disabled={disabled}
        >
          <View style={styles.dropdownButtonContent}>
            <Ionicons 
              name={getIcon(selectedValue)} 
              size={16} 
              color={disabled ? '#ccc' : getColor(selectedValue)} 
              style={styles.dropdownButtonIcon}
            />
            <Text style={[
              styles.dropdownButtonText,
              disabled && styles.dropdownButtonTextDisabled
            ]}>
              {getDisplayName(selectedValue)}
            </Text>
            <Ionicons 
              name={dropdownVisible ? "chevron-up" : "chevron-down"} 
              size={16} 
              color={disabled ? '#ccc' : '#666'} 
            />
          </View>
        </TouchableOpacity>
        
        {dropdownVisible && !disabled && (
          <View style={styles.dropdownMenu}>
            <ScrollView style={styles.dropdownScrollView} nestedScrollEnabled>
              {options.map(option => renderDropdownItem(option))}
            </ScrollView>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  dropdownContainer: {
    position: 'relative',
    zIndex: 10,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    paddingHorizontal: 12,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dropdownButtonDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  dropdownButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dropdownButtonIcon: {
    marginRight: 8,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dropdownButtonTextDisabled: {
    color: '#999',
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginTop: 4,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 11,
  },
  dropdownScrollView: {
    maxHeight: 200,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemSelected: {
    backgroundColor: '#f8f9fa',
  },
  dropdownItemIcon: {
    marginRight: 8,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  dropdownItemTextSelected: {
    fontWeight: '500',
    color: '#FF6B6B',
  },
});

export default StatusDropdown;
