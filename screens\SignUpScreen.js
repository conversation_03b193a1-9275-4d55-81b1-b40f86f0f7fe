import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, ScrollView, Platform, SafeAreaView, KeyboardAvoidingView, Dimensions } from 'react-native';
import { auth, db } from '../firebase.config'; // Import db
import { Ionicons } from '@expo/vector-icons';
import { createUserWithEmailAndPassword, sendEmailVerification, GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
import { doc, setDoc, updateDoc, serverTimestamp, getDoc } from 'firebase/firestore'; // Import Firestore functions
import { areNewRegistrationsAllowed } from '../utils/settingsUtils';
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
import authStateManager from '../utils/authState';

// Ensure the web browser closes correctly
WebBrowser.maybeCompleteAuthSession();

// Retrieve Client IDs from environment variables
const WEB_CLIENT_ID = process.env.FIREBASE_WEB_CLIENT_ID;
const IOS_CLIENT_ID = process.env.FIREBASE_IOS_CLIENT_ID || WEB_CLIENT_ID;
const ANDROID_CLIENT_ID = process.env.FIREBASE_ANDROID_CLIENT_ID || WEB_CLIENT_ID;

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Responsive constants (similar to LoginScreen for consistency)
const INPUT_WIDTH = SCREEN_WIDTH * 0.9;
const INPUT_HEIGHT = SCREEN_HEIGHT * 0.06 > 50 ? SCREEN_HEIGHT * 0.06 : 50; // Min height 50
const BUTTON_HEIGHT = SCREEN_HEIGHT * 0.06 > 50 ? SCREEN_HEIGHT * 0.06 : 50; // Min height 50
const TITLE_FONT_SIZE = SCREEN_WIDTH * 0.07;
const INPUT_FONT_SIZE = SCREEN_WIDTH * 0.04;
const BUTTON_FONT_SIZE = SCREEN_WIDTH * 0.045;
const LINK_FONT_SIZE = SCREEN_WIDTH * 0.04;
const ACCOUNT_TYPE_BADGE_PADDING_VERTICAL = SCREEN_HEIGHT * 0.008;
const ACCOUNT_TYPE_BADGE_PADDING_HORIZONTAL = SCREEN_WIDTH * 0.03;
const ACCOUNT_TYPE_FONT_SIZE = SCREEN_WIDTH * 0.035;
const ACCOUNT_TYPE_ICON_SIZE = SCREEN_WIDTH * 0.045;

const SignUpScreen = ({ navigation, route }) => {
  // Get isSeller parameter from route or default to false
  const { isSeller = false } = route.params || {}; const [name, setName] = useState(''); // Add state for name
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false); // Separate loading state for Google
  const [isRegistering, setIsRegistering] = useState(false); // Flag to prevent multiple registration attempts

  // Configure Google Sign-In
  const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
    webClientId: WEB_CLIENT_ID,
    iosClientId: IOS_CLIENT_ID,
    androidClientId: ANDROID_CLIENT_ID,
    useProxy: true, // Use Expo Auth Proxy for Google login
  });

  // Handle Google Sign-In response
  useEffect(() => {
    const handleGoogleSignUp = async () => {
      if (response?.type === 'success') {
        setGoogleLoading(true);
        const { id_token } = response.params;
        const credential = GoogleAuthProvider.credential(id_token);

        try {
          const userCredential = await signInWithCredential(auth, credential);
          const user = userCredential.user;
          console.log('Google Sign-Up successful for:', user.email);

          // Check if user exists in Firestore
          const userDocRef = doc(db, 'users', user.uid);
          const docSnap = await getDoc(userDocRef);

          if (!docSnap.exists()) {
            console.log('New Google user. Creating Firestore document.');

            // Create a user document in Firestore
            const userData = {
              uid: user.uid,
              email: user.email,
              name: user.displayName || '',
              name_lowercase: (user.displayName || '').toLowerCase(),
              bio: '',
              profilePictureUrl: user.photoURL || null,
              createdAt: serverTimestamp(),
              isSeller: false, // Always false for Google sign-up (buyer only)
              emailVerified: user.emailVerified || true, // Google accounts are typically pre-verified
              lastUsernameChangeDate: serverTimestamp(),
            };

            await setDoc(userDocRef, userData);
            console.log('User document created in Firestore!');

            // Navigate to complete profile
            navigation.navigate('CompleteProfile', { userId: user.uid });
          } else {
            // User already exists
            const userData = docSnap.data();

            if (userData.isSeller) {
              // Set user type mismatch flag to prevent App.js from processing auth events
              authStateManager.setUserTypeMismatch(user.uid);

              // IMMEDIATELY sign out the user - no delays, no complex logic
              try {
                await auth.signOut();
                console.log('Google user signed out due to account type mismatch in signup');
              } catch (signOutError) {
                console.error('Error signing out Google user in signup:', signOutError);
              }

              Alert.alert(
                'Wrong Account Type',
                'This Google account is already registered as a Seller. Please use the Seller Login tab to log in with your seller account.',
                [{ text: 'OK' }] // No navigation, just dismiss
              );
              setGoogleLoading(false);
              return; // Exit early to prevent further processing
            } else {
              console.log('Existing buyer account found, proceeding to main app');
              // Navigation will be handled by the auth state listener
            }
          }
        } catch (error) {
          console.error("Firebase Google Sign-Up Error:", error);

          // Provide user-friendly error messages for Google sign-up errors
          let errorTitle = 'Google Sign-Up Failed';
          let errorMessage = 'Could not sign up with Google. Please try again.';

          if (error.code) {
            switch (error.code) {
              case 'auth/account-exists-with-different-credential':
                errorTitle = 'Account Already Exists';
                errorMessage = 'An account already exists with the same email address but different sign-in credentials. Try signing in with a different method.';
                break;
              case 'auth/invalid-credential':
                errorTitle = 'Invalid Google Account';
                errorMessage = 'We couldn\'t verify your Google account. Please try again or use email sign-up instead.';
                break;
              case 'auth/operation-not-allowed':
                errorTitle = 'Google Sign-Up Disabled';
                errorMessage = 'Google sign-up is currently disabled. Please try again later or use email sign-up instead.';
                break;
              case 'auth/user-disabled':
                errorTitle = 'Account Disabled';
                errorMessage = 'This Google account has been disabled. Please contact support for assistance.';
                break;
              case 'auth/popup-closed-by-user':
                errorTitle = 'Sign-Up Cancelled';
                errorMessage = 'The Google sign-up was cancelled. Please try again if you want to sign up with Google.';
                break;
              case 'auth/network-request-failed':
                errorTitle = 'Network Error';
                errorMessage = 'A network error occurred. Please check your internet connection and try again.';
                break;
              default:
                if (error.message) {
                  errorMessage = error.message;
                }
                break;
            }
          }

          Alert.alert(errorTitle, errorMessage);
        } finally {
          setGoogleLoading(false);
        }
      } else if (response?.type === 'error') {
        console.error("Google Auth Error:", response.error);

        // Handle specific Google Auth errors
        let errorTitle = 'Google Sign-Up Error';
        let errorMessage = 'An error occurred during Google sign-up. Please try again.';

        if (response.error && typeof response.error === 'string') {
          if (response.error.includes('popup_closed')) {
            errorTitle = 'Sign-Up Cancelled';
            errorMessage = 'The Google sign-up was cancelled. Please try again if you want to sign up with Google.';
          } else if (response.error.includes('network')) {
            errorTitle = 'Network Error';
            errorMessage = 'A network error occurred. Please check your internet connection and try again.';
          } else if (response.error.includes('already_in_use')) {
            errorTitle = 'Email Already in Use';
            errorMessage = 'This Google account is already registered. Please try logging in instead.';
          } else {
            errorMessage = response.error;
          }
        }

        Alert.alert(errorTitle, errorMessage);
        setGoogleLoading(false);
      } else if (response?.type === 'cancel') {
        console.log('Google Sign-Up cancelled by user.');
        setGoogleLoading(false);
      }
    };

    handleGoogleSignUp();
  }, [response, navigation]);
  const handleSignUp = async () => {
    // Prevent multiple registration attempts
    if (isRegistering || loading) {
      console.log('Registration already in progress, ignoring click');
      return;
    }

    // Set flags to prevent multiple attempts
    setIsRegistering(true);
    setLoading(true);

    try {      // Check if new registrations are allowed
      try {
        const registrationsAllowed = await areNewRegistrationsAllowed();
        if (!registrationsAllowed) {
          Alert.alert(
            'Registration Disabled',
            'New registrations are currently disabled. Please try again later or contact support for assistance.',
            [{ text: 'OK' }]
          );
          setLoading(false);
          setIsRegistering(false);
          return;
        }
      } catch (error) {
        console.error('Error checking registration settings:', error);
        // Continue with registration if check fails (fail-safe approach)
      }

      // Enhanced validation with more helpful error messages      // Check for empty fields with specific messages
      if (!name) {
        Alert.alert('Name Required', 'Please enter your full name to continue.');
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      if (!email) {
        Alert.alert('Email Required', 'Please enter your email address to continue.');
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      if (!password) {
        Alert.alert('Password Required', 'Please create a password to secure your account.');
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      if (!confirmPassword) {
        Alert.alert('Confirm Password', 'Please confirm your password to ensure it\'s entered correctly.');
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      // Check password match with clear error message
      if (password !== confirmPassword) {
        Alert.alert(
          'Passwords Don\'t Match',
          'The passwords you entered don\'t match. Please make sure both passwords are identical.'
        );
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      // Enhanced password strength validation
      if (password.length < 6) {
        Alert.alert(
          'Password Too Short',
          'Your password must be at least 6 characters long for security. We recommend including numbers and special characters for a stronger password.'
        );
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      // Validate email format with helpful error message
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        Alert.alert(
          'Invalid Email Format',
          'Please enter a valid email address (e.g., <EMAIL>). A valid email is required to verify your account.'
        );
        setLoading(false);
        setIsRegistering(false);
        return;
      }

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      console.log('User signed up successfully in Auth!');

      // Prepare user data for Firestore
      const userData = {
        uid: user.uid,
        email: user.email,
        name: name, // Use the name from state
        name_lowercase: name.toLowerCase(), // Add lowercase version for searching
        bio: '', // Initialize with empty bio
        profilePictureUrl: null, // Initialize with null or a default placeholder URL
        createdAt: serverTimestamp(), // Record creation time
        isSeller: isSeller, // Use the isSeller parameter from route
        emailVerified: false, // Track email verification status
        lastUsernameChangeDate: serverTimestamp(), // Track when username was last changed
      };

      // Add empty address fields for sellers
      if (isSeller) {
        userData.address = {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: '',
        };
        userData.website = '';
      }

      // Create user document in Firestore
      const userDocRef = doc(db, 'users', user.uid);
      await setDoc(userDocRef, userData);
      console.log('User document created in Firestore!');      // Send verification email for both buyer and seller accounts
      await sendEmailVerification(user);
      console.log("Verification email sent to:", user.email);

      // Both buyer and seller accounts need to verify email first
      console.log("Account created - verification email sent, letting App.js handle navigation");

      // For seller accounts, set initial verification status
      if (isSeller) {
        await updateDoc(userDocRef, {
          sellerVerificationStatus: 'needsEmailVerification'
        });
      }

      // Don't navigate here - let App.js auth state listener handle the navigation
      // This prevents double navigation and flashing issues
    } catch (error) {
      console.error('Signup error:', error);

      // Provide user-friendly error messages based on error code
      let errorTitle = 'Sign Up Failed';
      let errorMessage = 'Failed to create account. Please try again.';

      switch (error.code) {
        case 'auth/email-already-in-use':
          errorTitle = 'Email Already in Use';
          errorMessage = 'This email address is already registered. Please use a different email or try logging in instead.';
          break;
        case 'auth/weak-password':
          errorTitle = 'Weak Password';
          errorMessage = 'Your password is too weak. Please use a stronger password with at least 6 characters, including numbers and special characters.';
          break;
        case 'auth/invalid-email':
          errorTitle = 'Invalid Email';
          errorMessage = 'Please enter a valid email address (e.g., <EMAIL>).';
          break;
        case 'auth/operation-not-allowed':
          errorTitle = 'Sign Up Disabled';
          errorMessage = 'Email/password sign up is currently disabled. Please try again later or contact support.';
          break;
        case 'auth/network-request-failed':
          errorTitle = 'Network Error';
          errorMessage = 'A network error occurred. Please check your internet connection and try again.';
          break;
        case 'auth/too-many-requests':
          errorTitle = 'Too Many Attempts';
          errorMessage = 'Too many unsuccessful sign-up attempts. Please try again later.';
          break;
        case 'auth/internal-error':
          errorTitle = 'System Error';
          errorMessage = 'An internal error occurred. Please try again later or contact support if the problem persists.';
          break;
        default:
          // Use the error message from Firebase if available
          if (error.message) {
            errorMessage = error.message;
          }
          break;
      }

      Alert.alert(errorTitle, errorMessage);
    } finally {
      // Reset flags after process completes
      setLoading(false);
      setIsRegistering(false);
    }
  };

  return (
    // Use ScrollView to prevent content overflow when keyboard is open or seller fields are shown
    <SafeAreaView style={{ flex: 1, backgroundColor: '#fff' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.container}>
            <Text style={styles.title}>Create Account</Text>

            {/* Account Type Indicator */}
            <View style={styles.accountTypeContainer}>
              <View style={styles.accountTypeBadge}>
                <Ionicons
                  name={isSeller ? "storefront-outline" : "person-outline"}
                  size={18}
                  color="#fff"
                />
                <Text style={styles.accountTypeText}>
                  {isSeller ? "Seller Account" : "Buyer Account"}
                </Text>
              </View>
            </View>            <TextInput
              style={styles.input}
              placeholder="Full Name"
              placeholderTextColor="#000"
              value={name}
              onChangeText={setName}
              autoCapitalize="words"
              editable={!loading && !isRegistering}
            />

            <TextInput
              style={styles.input}
              placeholder="Email"
              placeholderTextColor="#000"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              editable={!loading && !isRegistering}
            />            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Password (min. 6 characters)"
                placeholderTextColor="#000"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                editable={!loading && !isRegistering}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
                disabled={loading || isRegistering}
              >
                <Ionicons
                  name={showPassword ? "eye-off" : "eye"}
                  size={24}
                  color="#666"
                />
              </TouchableOpacity>
            </View>

            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Confirm Password"
                placeholderTextColor="#000"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showConfirmPassword}
                editable={!loading && !isRegistering}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={loading || isRegistering}
              >
                <Ionicons
                  name={showConfirmPassword ? "eye-off" : "eye"}
                  size={24}
                  color="#666"
                />
              </TouchableOpacity>
            </View>

            {(loading || isRegistering) ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#FF6B6B" />
                <Text style={styles.loadingText}>
                  {isRegistering ? 'Creating your account...' : 'Loading...'}
                </Text>
              </View>
            ) : (
              <TouchableOpacity
                style={[styles.button, (loading || isRegistering || googleLoading) && styles.disabledButton]}
                onPress={handleSignUp}
                disabled={loading || isRegistering || googleLoading}
                activeOpacity={0.7}
              >
                <Text style={styles.buttonText}>Sign Up</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              onPress={() => navigation.navigate('Login', { isSeller })}
              disabled={loading || googleLoading || isRegistering}
              style={{ opacity: (loading || googleLoading || isRegistering) ? 0.5 : 1 }}
            >
              <Text style={styles.linkText}>Already have an account? Log In</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center', // Center content vertically
    alignItems: 'center', // Center content horizontally
    paddingVertical: 20,
  },
  container: {
    width: SCREEN_WIDTH, // Ensure container takes full width
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SCREEN_WIDTH * 0.05, // Use percentage for padding
    backgroundColor: '#fff',
  },
  title: {
    fontSize: TITLE_FONT_SIZE,
    fontWeight: 'bold',
    marginBottom: SCREEN_HEIGHT * 0.03, // Responsive margin
    color: '#333',
    textAlign: 'center',
  }, input: {
    width: INPUT_WIDTH,
    height: INPUT_HEIGHT,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: INPUT_HEIGHT / 2, // Make it more pill-shaped
    paddingHorizontal: INPUT_WIDTH * 0.05,
    marginBottom: SCREEN_HEIGHT * 0.015, // Responsive margin (slightly less than login for more inputs)
    fontSize: INPUT_FONT_SIZE,
    backgroundColor: '#f8f8f8',
    color: '#000', // Ensure text is black
  },
  passwordContainer: {
    width: INPUT_WIDTH,
    height: INPUT_HEIGHT,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: INPUT_HEIGHT / 2,
    backgroundColor: '#f8f8f8',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SCREEN_HEIGHT * 0.015,
    paddingHorizontal: INPUT_WIDTH * 0.05,
  },
  passwordInput: {
    flex: 1,
    height: '100%',
    fontSize: INPUT_FONT_SIZE,
    color: '#000', // Ensure text and dots are black
  },
  eyeIcon: {
    padding: 5,
    marginLeft: 10,
  }, button: {
    width: INPUT_WIDTH,
    height: BUTTON_HEIGHT,
    backgroundColor: '#FF6B6B',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BUTTON_HEIGHT / 2, // Make it more pill-shaped
    marginTop: SCREEN_HEIGHT * 0.015, // Responsive margin
    flexDirection: 'row',
    elevation: 2, // Add some elevation for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: BUTTON_FONT_SIZE,
    fontWeight: 'bold',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SCREEN_HEIGHT * 0.02,
    paddingVertical: 10,
  },
  loadingText: {
    marginTop: 10,
    fontSize: INPUT_FONT_SIZE,
    color: '#666',
    textAlign: 'center',
  },
  googleButton: {
    backgroundColor: '#4285F4',
    marginTop: SCREEN_HEIGHT * 0.015, // Responsive margin
  },
  googleIcon: {
    marginRight: 10,
  },
  linkText: {
    color: '#FF6B6B',
    marginTop: SCREEN_HEIGHT * 0.02, // Responsive margin
    fontSize: LINK_FONT_SIZE,
    textAlign: 'center',
  },
  activityIndicator: {
    marginTop: SCREEN_HEIGHT * 0.02,
  },
  accountTypeContainer: {
    marginBottom: SCREEN_HEIGHT * 0.025, // Responsive margin
    alignItems: 'center', // Center the badge
  },
  accountTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF8787', // Changed from #6c757d to a pink shade
    paddingVertical: ACCOUNT_TYPE_BADGE_PADDING_VERTICAL,
    paddingHorizontal: ACCOUNT_TYPE_BADGE_PADDING_HORIZONTAL,
    borderRadius: 15,
  },
  accountTypeText: {
    color: '#fff',
    fontSize: ACCOUNT_TYPE_FONT_SIZE,
    marginLeft: 8,
    fontWeight: '500',
  },
});

export default SignUpScreen;