import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, FlatList, StyleSheet, Platform, SafeAreaView, Alert, Image, KeyboardAvoidingView } from 'react-native';
import { db, auth } from '../firebase.config';
import { collection, query, orderBy, onSnapshot, addDoc, updateDoc, serverTimestamp, doc, getDoc, arrayUnion } from 'firebase/firestore';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function ChatThreadScreen({ route, navigation }) {
  const { threadId, otherUserId } = route.params;
  const [otherUserInfo, setOtherUserInfo] = useState({ name: '', profilePictureUrl: '' });
  const userId = auth.currentUser.uid;
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const insets = useSafeAreaInsets();
  const flatListRef = useRef();

  // Fetch the other user's profile data for header display
  useEffect(() => {
    const fetchOtherUserInfo = async () => {
      try {
        const userDoc = await getDoc(doc(db, 'users', otherUserId));
        if (userDoc.exists()) {
          const data = userDoc.data();
          setOtherUserInfo({
            name: data.name || '',
            profilePictureUrl: data.profilePictureUrl || ''
          });
        }
      } catch (e) {
        console.error('Error fetching other user info:', e);
      }
    };
    fetchOtherUserInfo();
  }, [otherUserId]);

  // Listen for new messages
  useEffect(() => {
    const messagesRef = collection(db, 'threads', threadId, 'messages');
    const q = query(messagesRef, orderBy('timestamp', 'asc'));
    const unsub = onSnapshot(q, async snapshot => {
      const list = snapshot.docs.map(docSnap => ({ id: docSnap.id, ...docSnap.data() }));
      setMessages(list);
      // mark delivered & read for current user
      snapshot.docs.forEach(async docSnap => {
        const msg = docSnap.data();
        const msgRef = doc(db, 'threads', threadId, 'messages', docSnap.id);
        if (msg.senderId !== userId && !msg.deliveredTo?.includes(userId)) {
          await updateDoc(msgRef, { deliveredTo: arrayUnion(userId) });
        }
        if (msg.senderId !== userId && !msg.readBy?.includes(userId)) {
          await updateDoc(msgRef, { readBy: arrayUnion(userId) });
        }
      });
      // scroll to bottom
      setTimeout(() => flatListRef.current?.scrollToEnd({ animated: true }), 100);
    });
    return unsub;
  }, [threadId]);

  // Header with back, online indicator, block, report
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: '',
      // Custom header: back button + other user's avatar and name
      headerLeft: () => (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={{ paddingHorizontal: 15 }}>
            <Ionicons name="arrow-back-outline" size={24} color="#333" />
          </TouchableOpacity>
          {otherUserInfo.profilePictureUrl ? (
            <TouchableOpacity onPress={() => navigation.navigate('UserProfile', { userId: otherUserId })}>
              <Image
                source={{ uri: otherUserInfo.profilePictureUrl }}
                style={{ width: 32, height: 32, borderRadius: 16, marginRight: 8 }}
              />
            </TouchableOpacity>
          ) : null}
          <Text style={{ fontSize: 18, color: '#000' }}>{otherUserInfo.name}</Text>
        </View>
      ),
      headerRight: () => (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <OnlineIndicator userId={otherUserId} />
          <TouchableOpacity onPress={blockUser} style={styles.iconButton}>
            <Ionicons name="ban-outline" size={24} color="red" />
          </TouchableOpacity>
          <TouchableOpacity onPress={reportUser} style={styles.iconButton}>
            <Ionicons name="warning-outline" size={24} color="#FFA500" />
          </TouchableOpacity>
        </View>
      ),
    });
  }, [navigation, otherUserId, otherUserInfo]);

  const sendMessage = useCallback(async () => {
    if (!input.trim()) return;
    try {
      const messagesRef = collection(db, 'threads', threadId, 'messages');
      await addDoc(messagesRef, {
        text: input.trim(),
        senderId: userId,
        timestamp: serverTimestamp(),
        deliveredTo: [userId],
        readBy: []
      });
      
      // Update thread doc: set last message and mark as read by sender
      const threadDocRef = doc(db, 'threads', threadId);
      await updateDoc(threadDocRef, {
        lastMessage: input.trim(),
        lastMessageTimestamp: serverTimestamp(),
        updatedAt: serverTimestamp(),
        readBy: [userId]
      });
      
      setInput('');
    } catch (e) {
      console.error('Send message error:', e);
      Alert.alert('Error', 'Failed to send message');
    }
  }, [input, threadId, userId]);

  async function blockUser() {
    Alert.alert(
      'Block User',
      'Are you sure you want to block this user? You will not see their messages.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Block', style: 'destructive', onPress: async () => {
            try {
              const userDoc = doc(db, 'users', userId);
              await updateDoc(userDoc, { blockedUsers: serverTimestamp(), /* optionally arrayUnion*/ });
              Alert.alert('Blocked', 'User has been blocked.');
            } catch (e) { console.error(e); }
          }
        }
      ]
    );
  }

  async function reportUser() {
    try {
      await addDoc(collection(db, 'reports'), {
        reporterId: userId,
        reportedId: otherUserId,
        threadId,
        createdAt: serverTimestamp()
      });
      Alert.alert('Reported', 'User has been reported for review.');
    } catch (e) {
      console.error('Report error:', e);
    }
  }

  const renderMessage = ({ item }) => {
    const isMe = item.senderId === userId;
    // determine tick state for sender
    const otherId = isMe ? otherUserId : null;
    const delivered = isMe && item.deliveredTo?.includes(otherId);
    const read = isMe && item.readBy?.includes(otherId);
    return (
      <View style={[styles.messageContainer, isMe ? styles.myMessage : styles.theirMessage]}>
        <Text style={styles.messageText}>{item.text}</Text>
        {isMe && (
          <View style={styles.tickIcon}>
            {!delivered ? (
              <Ionicons name="checkmark" size={16} color="#666" />
            ) : read ? (
              <Ionicons name="checkmark-done" size={16} color="#4AC3FF" />
            ) : (
              <Ionicons name="checkmark-done" size={16} color="#666" />
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={insets.top + 18} // increase offset so input sits higher above the keyboard
    >
      <SafeAreaView style={styles.container}>
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={item => item.id}
          renderItem={renderMessage}
          contentContainerStyle={styles.messagesList}
        />
        <View style={[styles.inputRow, { paddingBottom: insets.bottom }]}>
          <TextInput
            style={styles.input}
            value={input}
            onChangeText={setInput}
            placeholder="Type a message..."
          />
          <TouchableOpacity onPress={sendMessage} style={styles.sendButton}>
            <Ionicons name="send-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}

// OnlineIndicator reused
const OnlineIndicator = ({ userId }) => {
  const [isOnline, setIsOnline] = useState(false);
  useEffect(() => {
    const unsub = onSnapshot(doc(db, 'users', userId), snap => {
      const data = snap.data() || {};
      setIsOnline(!!data.online);
    });
    return unsub;
  }, [userId]);
  return (
    <Ionicons name="ellipse" size={12} color={isOnline ? 'green' : 'grey'} style={{ marginHorizontal: 8 }} />
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  messagesList: { padding: 10 },
  messageContainer: { marginVertical: 4, padding: 8, borderRadius: 8, maxWidth: '80%' },
  myMessage: { alignSelf: 'flex-end', backgroundColor: '#DCF8C6' },
  theirMessage: { alignSelf: 'flex-start', backgroundColor: '#ECECEC' },
  messageText: { fontSize: 16 },
  inputRow: { flexDirection: 'row', padding: 10, borderTopWidth: 1, borderColor: '#eee' },
  input: { flex: 1, borderWidth: 1, borderColor: '#ccc', borderRadius: 20, paddingHorizontal: 15, fontSize: 16 },
  sendButton: { backgroundColor: '#FF6B6B', borderRadius: 20, padding: 10, marginLeft: 8, justifyContent: 'center', alignItems: 'center' },
  iconButton: { paddingHorizontal: 10 },
  tickIcon: { marginLeft: 4, alignSelf: 'flex-end' }
});
