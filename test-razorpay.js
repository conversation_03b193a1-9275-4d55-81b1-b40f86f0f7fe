// Test script to check if the Razorpay Firebase function is working
// This script can be run from a browser console for testing

const testCreateRazorpayOrder = async () => {
  console.log('🧪 Testing createRazorpayOrder Firebase Function...');
  
  try {
    // This will work if you're logged into the app and open the browser console
    // Make sure you have the firebase config imported in your app
    
    const createOrderFn = firebase.functions().httpsCallable('createRazorpayOrder');
    const result = await createOrderFn({
      amount: 100, // ₹100
      currency: 'INR',
      receipt: `test_receipt_${Date.now()}`,
      notes: {
        test: 'true',
        environment: 'development'
      }
    });
    
    console.log('✅ Function executed successfully!');
    console.log('📋 Result:', result.data);
    
    if (result.data.orderId && result.data.orderId.startsWith('order_')) {
      console.log('🎉 SUCCESS: Valid Razorpay order ID created:', result.data.orderId);
      console.log('💰 Amount:', result.data.amount / 100, 'INR');
      console.log('🆔 Receipt:', result.data.receipt);
      console.log('📅 Created at:', new Date(result.data.created_at * 1000));
      return true;
    } else {
      console.log('❌ FAILED: Invalid order ID format');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing function:', error);
    
    if (error.code === 'unauthenticated') {
      console.log('🔐 You need to be logged in to test this function');
      console.log('💡 Try logging into the app first, then run this test from the browser console');
    } else if (error.code === 'invalid-argument') {
      console.log('📝 Invalid arguments passed to the function');
    } else {
      console.log('🐛 Unexpected error occurred');
    }
    
    return false;
  }
};

// Instructions for running the test
console.log(`
🧪 RAZORPAY FUNCTION TEST INSTRUCTIONS

To test the createRazorpayOrder function:

1. 📱 Open your StyleApp in a web browser or mobile device
2. 🔑 Log in to the app with a valid user account
3. 🌐 Open the browser developer console (F12)
4. 📋 Copy and paste this function: testCreateRazorpayOrder()
5. ▶️  Run the function by typing: testCreateRazorpayOrder()

Expected Result:
- ✅ Function should return a valid Razorpay order ID (starts with 'order_')
- 💰 Amount should be 10000 paise (₹100)
- 🆔 Receipt should be generated
- 📅 Created timestamp should be current

Alternative Test (from this file):
- 🗂️  Update the imports to work with Node.js
- 🔑 Add authentication credentials
- ▶️  Run: node test-razorpay.js
`);

// For browser console testing, make this available globally
if (typeof window !== 'undefined') {
  window.testCreateRazorpayOrder = testCreateRazorpayOrder;
}
