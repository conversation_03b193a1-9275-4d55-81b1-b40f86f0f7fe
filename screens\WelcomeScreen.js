import { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ImageBackground, Animated, Easing, Dimensions, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

// Screen dimensions for responsive design
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Responsive sizes - ensuring title fits on one line
const TITLE_FONT_SIZE = Platform.OS === 'android'
  ? Math.min(SCREEN_WIDTH * 0.10, 48) // Smaller for Android to prevent wrapping
  : Math.min(SCREEN_WIDTH * 0.12, 58); // Cap for iOS too
const TAGLINE_FONT_SIZE = SCREEN_WIDTH * 0.04;
const TOGGLE_WIDTH = SCREEN_WIDTH * 0.9;
const TOGGLE_HEIGHT = TOGGLE_WIDTH * (50 / 320);
const INDICATOR_WIDTH = TOGGLE_WIDTH * (158 / 320);
const INDICATOR_HEIGHT = TOGGLE_HEIGHT * (46 / 50);

// Calculate the target translation for the toggle indicator
const TOGGLE_BACKGROUND_PADDING_PERCENT = 0.00625; // This is 2 / 320, for one side
const INDICATOR_TARGET_TRANSLATE_X = (TOGGLE_WIDTH * (1 - 2 * TOGGLE_BACKGROUND_PADDING_PERCENT)) / 2;

// Background image
const backgroundImage = require('../assets/welcome_bg.png');

const WelcomeScreen = ({ navigation }) => {
  const [isSeller, setIsSeller] = useState(false);

  // Animation values
  const toggleAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Handle toggle animation for specific selection
  const toggleToBuyer = () => {
    if (isSeller) {
      // Scale down and up animation for button press effect
      Animated.sequence([
        Animated.timing(scaleAnimation, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.ease
        }),
        Animated.timing(scaleAnimation, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.ease
        })
      ]).start();

      // Toggle animation to buyer (0)
      Animated.timing(toggleAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1)
      }).start(() => {
        setIsSeller(false);
      });
    }
  };

  const toggleToSeller = () => {
    if (!isSeller) {
      // Scale down and up animation for button press effect
      Animated.sequence([
        Animated.timing(scaleAnimation, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.ease
        }),
        Animated.timing(scaleAnimation, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.ease
        })
      ]).start();

      // Toggle animation to seller (1)
      Animated.timing(toggleAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1)
      }).start(() => {
        setIsSeller(true);
      });
    }
  };

  // Interpolate values for animations
  const toggleTranslateX = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, INDICATOR_TARGET_TRANSLATE_X] // Adjusted for responsive translation
  });

  const buyerTextColor = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['#FF6B6B', '#666666']
  });

  const sellerTextColor = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['#666666', '#FF6B6B']
  });



  const buyerScale = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1.05, 0.95]
  });
  const sellerScale = toggleAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.95, 1.05]
  });

  // Handlers for navigation with explicit Text wrapping
  const handleLoginPress = () => navigation.navigate('Login', { isSeller: isSeller });
  const handleSignUpPress = () => navigation.navigate('SignUp', { isSeller: isSeller });

  return (
    <ImageBackground source={backgroundImage} style={styles.background}>
      <LinearGradient
        colors={['rgba(0,0,0,0.5)', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.2)']}
        style={styles.gradientOverlay}
      >
        <View style={styles.container}>

          <View style={styles.contentContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.title} numberOfLines={1} adjustsFontSizeToFit>SwipeSense</Text>
              <View style={styles.taglineContainer}>
                <Ionicons
                  name={isSeller ? "storefront" : "sparkles"}
                  size={22}
                  color="rgba(255, 192, 203, 0.8)"
                  style={styles.taglineIcon}
                />
                <Text style={styles.tagline}>
                  {isSeller ? "Showcase your fashion collection" : "Discover your next favorite outfit"}
                </Text>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.loginButton]}
                onPress={handleLoginPress}
              >
                <View style={styles.buttonContent}>
                  <View style={styles.iconContainer}>
                    <Ionicons name="log-in-outline" size={22} color="#fff" style={styles.buttonIcon} />
                  </View>
                  <Text style={styles.buttonText}>Log In</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.signupButton]}
                onPress={handleSignUpPress}
              >
                <View style={styles.buttonContent}>
                  <View style={styles.iconContainer}>
                    <Ionicons name="person-add-outline" size={22} color="#fff" style={styles.buttonIcon} />
                  </View>
                  <Text style={[styles.buttonText, styles.signupButtonText]}>Sign Up</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Account Type Toggle (Moved to bottom) */}
          <View style={styles.toggleContainer}>
            <Animated.View
              style={[
                styles.toggleBackground,
                { transform: [{ scaleX: scaleAnimation }, { scaleY: scaleAnimation }] }
              ]}
            >
              <Animated.View
                style={[
                  styles.toggleIndicator,
                  { transform: [{ translateX: toggleTranslateX }] }
                ]}
              />
              <View style={styles.toggleLabelsContainer}>
                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={toggleToBuyer}
                  style={styles.toggleLabelTouch}
                >
                  <Animated.View style={{ transform: [{ scale: buyerScale }], alignItems: 'center', width: '100%' }}>
                    <Ionicons name="person-outline" size={22} color={isSeller ? "#666666" : "#FF6B6B"} />
                    <Animated.Text style={[styles.toggleLabelText, { color: buyerTextColor }]}>
                      Buyer
                    </Animated.Text>
                  </Animated.View>
                </TouchableOpacity>

                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={toggleToSeller}
                  style={styles.toggleLabelTouch}
                >
                  <Animated.View style={{ transform: [{ scale: sellerScale }], alignItems: 'center', width: '100%' }}>
                    <Ionicons name="storefront-outline" size={22} color={isSeller ? "#FF6B6B" : "#666666"} />
                    <Animated.Text style={[styles.toggleLabelText, { color: sellerTextColor }]}>
                      Seller
                    </Animated.Text>
                  </Animated.View>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  gradientOverlay: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    paddingBottom: 50,
  },  // Toggle styles
  toggleContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  toggleBackground: {
    width: TOGGLE_WIDTH,
    height: TOGGLE_HEIGHT,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: TOGGLE_HEIGHT / 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: TOGGLE_WIDTH * 0.00625, // Scaled padding (2/320)
    position: 'relative',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.8)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 8,
  },
  toggleIndicator: {
    position: 'absolute',
    width: INDICATOR_WIDTH,
    height: INDICATOR_HEIGHT,
    backgroundColor: '#FFC0CB',
    borderRadius: INDICATOR_HEIGHT / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    marginHorizontal: TOGGLE_WIDTH * 0.003125, // Scaled margin (1/320)
  },
  toggleLabelsContainer: {
    flexDirection: 'row',
    width: '100%',
    position: 'absolute',
    zIndex: 1,
  },
  toggleLabelTouch: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: TOGGLE_HEIGHT, // Match the toggle container height
  },
  toggleLabelText: {
    fontWeight: '600',
    fontSize: 16, // Match the image
    textAlign: 'center',
    width: '100%',
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 40,
    width: SCREEN_WIDTH * 0.9, // Control width to prevent wrapping
    maxWidth: 500, // Maximum width on larger devices
  },
  title: {
    fontSize: TITLE_FONT_SIZE,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: SCREEN_HEIGHT * 0.02,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
    letterSpacing: Platform.OS === 'android' ? 0 : 1, // Reduced for better fit
    textAlign: 'center',
    width: '100%',
    includeFontPadding: false, // Helps with text height calculation on Android
    lineHeight: TITLE_FONT_SIZE * 1.2, // Proper lineHeight for single line
  }, taglineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
  },
  taglineIcon: {
    marginRight: 10,
    opacity: 0.8,
  },
  tagline: {
    fontSize: TAGLINE_FONT_SIZE * 0.95,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    fontWeight: '400',
    letterSpacing: 0.3,
    opacity: 0.9,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  button: {
    width: '90%',
    height: 58,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 29,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 6,
    paddingHorizontal: 10, // Add padding to ensure content is centered
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  iconContainer: {
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  buttonIcon: {
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
  loginButton: {
    backgroundColor: '#FF6B6B',
  },
  signupButton: {
    backgroundColor: 'rgba(255, 107, 107, 0.85)',
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },

  buttonText: {
    color: '#fff',
    fontSize: 19,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    textAlign: 'center', // Ensure text is centered
    flex: 1,
    paddingRight: 30, // Balance the icon container width on the right side
  },
  signupButtonText: {
    color: '#fff',
  },
});

export default WelcomeScreen;
