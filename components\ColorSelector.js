import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Predefined color palette
const COLOR_PALETTE = [
  // Basic Colors
  { name: 'Red', value: '#FF0000', textColor: '#FFFFFF' },
  { name: 'Blue', value: '#0000FF', textColor: '#FFFFFF' },
  { name: 'Green', value: '#008000', textColor: '#FFFFFF' },
  { name: 'Yellow', value: '#FFFF00', textColor: '#000000' },
  { name: 'Black', value: '#000000', textColor: '#FFFFFF' },
  { name: 'White', value: '#FFFFFF', textColor: '#000000' },
  { name: 'Pink', value: '#FFC0CB', textColor: '#000000' },
  { name: 'Purple', value: '#800080', textColor: '#FFFFFF' },
  { name: 'Orange', value: '#FFA500', textColor: '#000000' },
  { name: '<PERSON>', value: '#A52A2A', textColor: '#FFFFFF' },
  { name: 'Gray', value: '#808080', textColor: '#FFFFFF' },
  { name: 'Navy', value: '#000080', textColor: '#FFFFFF' },
  
  // Neutral Tones
  { name: 'Beige', value: '#F5F5DC', textColor: '#000000' },
  { name: 'Khaki', value: '#F0E68C', textColor: '#000000' },
  { name: 'Cream', value: '#FFFDD0', textColor: '#000000' },
  { name: 'Ivory', value: '#FFFFF0', textColor: '#000000' },
  { name: 'Tan', value: '#D2B48C', textColor: '#000000' },
  { name: 'Taupe', value: '#483C32', textColor: '#FFFFFF' },
  { name: 'Mushroom', value: '#A68B5B', textColor: '#FFFFFF' },
  { name: 'Sand', value: '#C2B280', textColor: '#000000' },
  { name: 'Stone', value: '#8C7853', textColor: '#FFFFFF' },
  { name: 'Linen', value: '#FAF0E6', textColor: '#000000' },
  
  // Metallic Colors
  { name: 'Silver', value: '#C0C0C0', textColor: '#000000' },
  { name: 'Gold', value: '#FFD700', textColor: '#000000' },
  { name: 'Rose Gold', value: '#E8B4B8', textColor: '#000000' },
  { name: 'Bronze', value: '#CD7F32', textColor: '#FFFFFF' },
  { name: 'Copper', value: '#B87333', textColor: '#FFFFFF' },
  { name: 'Platinum', value: '#E5E4E2', textColor: '#000000' },
  
  // Red Variants
  { name: 'Crimson', value: '#DC143C', textColor: '#FFFFFF' },
  { name: 'Scarlet', value: '#FF2400', textColor: '#FFFFFF' },
  { name: 'Cherry', value: '#DE3163', textColor: '#FFFFFF' },
  { name: 'Ruby', value: '#E0115F', textColor: '#FFFFFF' },
  { name: 'Wine', value: '#722F37', textColor: '#FFFFFF' },
  { name: 'Burgundy', value: '#800020', textColor: '#FFFFFF' },
  { name: 'Maroon', value: '#800000', textColor: '#FFFFFF' },
  { name: 'Coral', value: '#FF7F50', textColor: '#000000' },
  { name: 'Salmon', value: '#FA8072', textColor: '#000000' },
  { name: 'Rose', value: '#FF007F', textColor: '#FFFFFF' },
  
  // Pink Variants
  { name: 'Hot Pink', value: '#FF69B4', textColor: '#000000' },
  { name: 'Fuchsia', value: '#FF00FF', textColor: '#FFFFFF' },
  { name: 'Magenta', value: '#FF00FF', textColor: '#FFFFFF' },
  { name: 'Blush', value: '#DE5D83', textColor: '#FFFFFF' },
  { name: 'Dusty Rose', value: '#DCAE96', textColor: '#000000' },
  { name: 'Mauve', value: '#E0B4D6', textColor: '#000000' },
  { name: 'Peach', value: '#FFCBA4', textColor: '#000000' },
  { name: 'Pastel Pink', value: '#FFD1DC', textColor: '#000000' },
  
  // Purple Variants
  { name: 'Violet', value: '#8A2BE2', textColor: '#FFFFFF' },
  { name: 'Lavender', value: '#E6E6FA', textColor: '#000000' },
  { name: 'Lilac', value: '#C8A2C8', textColor: '#000000' },
  { name: 'Plum', value: '#DDA0DD', textColor: '#000000' },
  { name: 'Amethyst', value: '#9966CC', textColor: '#FFFFFF' },
  { name: 'Eggplant', value: '#614051', textColor: '#FFFFFF' },
  { name: 'Orchid', value: '#DA70D6', textColor: '#000000' },
  { name: 'Indigo', value: '#4B0082', textColor: '#FFFFFF' },
  
  // Blue Variants
  { name: 'Light Blue', value: '#ADD8E6', textColor: '#000000' },
  { name: 'Sky Blue', value: '#87CEEB', textColor: '#000000' },
  { name: 'Royal Blue', value: '#4169E1', textColor: '#FFFFFF' },
  { name: 'Cobalt', value: '#0047AB', textColor: '#FFFFFF' },
  { name: 'Sapphire', value: '#0F52BA', textColor: '#FFFFFF' },
  { name: 'Powder Blue', value: '#B0E0E6', textColor: '#000000' },
  { name: 'Baby Blue', value: '#89CFF0', textColor: '#000000' },
  { name: 'Steel Blue', value: '#4682B4', textColor: '#FFFFFF' },
  { name: 'Midnight Blue', value: '#191970', textColor: '#FFFFFF' },
  { name: 'Periwinkle', value: '#CCCCFF', textColor: '#000000' },
  { name: 'Cerulean', value: '#007BA7', textColor: '#FFFFFF' },
  
  // Green Variants
  { name: 'Forest Green', value: '#228B22', textColor: '#FFFFFF' },
  { name: 'Lime', value: '#00FF00', textColor: '#000000' },
  { name: 'Mint', value: '#98FB98', textColor: '#000000' },
  { name: 'Sage', value: '#9CAF88', textColor: '#000000' },
  { name: 'Emerald', value: '#50C878', textColor: '#000000' },
  { name: 'Jade', value: '#00A86B', textColor: '#FFFFFF' },
  { name: 'Sea Green', value: '#2E8B57', textColor: '#FFFFFF' },
  { name: 'Olive', value: '#808000', textColor: '#FFFFFF' },
  { name: 'Teal', value: '#008080', textColor: '#FFFFFF' },
  { name: 'Turquoise', value: '#40E0D0', textColor: '#000000' },
  { name: 'Aqua', value: '#00FFFF', textColor: '#000000' },
  { name: 'Mint Green', value: '#98FB98', textColor: '#000000' },
  
  // Yellow/Orange Variants
  { name: 'Lemon', value: '#FFFACD', textColor: '#000000' },
  { name: 'Mustard', value: '#FFDB58', textColor: '#000000' },
  { name: 'Amber', value: '#FFBF00', textColor: '#000000' },
  { name: 'Saffron', value: '#F4C430', textColor: '#000000' },
  { name: 'Honey', value: '#FFC30B', textColor: '#000000' },
  { name: 'Apricot', value: '#FBCEB1', textColor: '#000000' },
  { name: 'Tangerine', value: '#F28500', textColor: '#000000' },
  { name: 'Burnt Orange', value: '#CC5500', textColor: '#FFFFFF' },
  { name: 'Pumpkin', value: '#FF7518', textColor: '#000000' },
  { name: 'Sunset', value: '#FAD5A5', textColor: '#000000' },
  
  // Brown Variants
  { name: 'Chocolate', value: '#7B3F00', textColor: '#FFFFFF' },
  { name: 'Coffee', value: '#6F4E37', textColor: '#FFFFFF' },
  { name: 'Caramel', value: '#AF6E4D', textColor: '#FFFFFF' },
  { name: 'Chestnut', value: '#954535', textColor: '#FFFFFF' },
  { name: 'Mahogany', value: '#C04000', textColor: '#FFFFFF' },
  { name: 'Rust', value: '#B7410E', textColor: '#FFFFFF' },
  { name: 'Cinnamon', value: '#D2691E', textColor: '#FFFFFF' },
  { name: 'Sienna', value: '#A0522D', textColor: '#FFFFFF' },
  
  // Gray Variants
  { name: 'Charcoal', value: '#36454F', textColor: '#FFFFFF' },
  { name: 'Slate', value: '#708090', textColor: '#FFFFFF' },
  { name: 'Ash', value: '#B2BEB5', textColor: '#000000' },
  { name: 'Pearl', value: '#EAE0C8', textColor: '#000000' },
  { name: 'Dove Gray', value: '#6D6C6C', textColor: '#FFFFFF' },
  { name: 'Smoke', value: '#738276', textColor: '#FFFFFF' },
  { name: 'Storm', value: '#4F666A', textColor: '#FFFFFF' },
  
  // Unique/Fashion Colors
  { name: 'Neon Green', value: '#39FF14', textColor: '#000000' },
  { name: 'Neon Pink', value: '#FF073A', textColor: '#FFFFFF' },
  { name: 'Electric Blue', value: '#7DF9FF', textColor: '#000000' },
  { name: 'Holographic', value: '#FF69B4', textColor: '#000000' },
  { name: 'Iridescent', value: '#B19CD9', textColor: '#000000' },
  { name: 'Sunset Orange', value: '#FF8C69', textColor: '#000000' },
  { name: 'Ocean Blue', value: '#006994', textColor: '#FFFFFF' },
  { name: 'Galaxy Purple', value: '#553C9A', textColor: '#FFFFFF' },
  { name: 'Millennial Pink', value: '#F2D2BD', textColor: '#000000' },
  { name: 'Gen Z Yellow', value: '#FFEF00', textColor: '#000000' },
];

const ColorSelector = ({
  selectedColors = [],
  onColorsChange,
  disabled = false,
  style
}) => {
  const [showModal, setShowModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredColors, setFilteredColors] = useState(COLOR_PALETTE);

  // Filter colors based on search
  React.useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = COLOR_PALETTE.filter(color =>
        color.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredColors(filtered);
    } else {
      setFilteredColors(COLOR_PALETTE);
    }
  }, [searchQuery]);

  const handleColorToggle = (colorName) => {
    const newSelected = selectedColors.includes(colorName)
      ? selectedColors.filter(color => color !== colorName)
      : [...selectedColors, colorName];

    onColorsChange(newSelected);
  };

  const openModal = () => {
    if (!disabled) {
      setSearchQuery('');
      setShowModal(true);
    }
  };

  const getColorByName = (colorName) => {
    return COLOR_PALETTE.find(color => color.name === colorName);
  };

  const renderColorItem = ({ item: color }) => {
    const isSelected = selectedColors.includes(color.name);

    return (
      <TouchableOpacity
        style={[
          styles.colorItem,
          isSelected && styles.selectedColorItem
        ]}
        onPress={() => handleColorToggle(color.name)}
      >
        <View style={styles.colorItemContent}>
          <View
            style={[
              styles.colorCircle,
              { backgroundColor: color.value },
              color.name === 'White' && styles.whiteColorBorder
            ]}
          />
          <Text style={[
            styles.colorItemText,
            isSelected && styles.selectedColorItemText
          ]}>
            {color.name}
          </Text>
        </View>
        <Ionicons
          name={isSelected ? "checkbox" : "square-outline"}
          size={20}
          color={isSelected ? "#FF6B6B" : "#666"}
        />
      </TouchableOpacity>
    );
  };

  const renderSelectedColors = () => (
    <View style={styles.selectedColorsContainer}>
      {selectedColors.length === 0 ? (
        <Text style={styles.emptyText}>No colors selected</Text>
      ) : (
        <View style={styles.colorsWrapper}>
          {selectedColors.map((colorName, index) => {
            const colorData = getColorByName(colorName);
            return (
              <View key={index} style={styles.colorTag}>
                <View
                  style={[
                    styles.colorTagCircle,
                    { backgroundColor: colorData?.value || '#ccc' },
                    colorName === 'White' && styles.whiteColorBorder
                  ]}
                />
                <Text style={styles.colorTagText}>{colorName}</Text>
                <TouchableOpacity
                  onPress={() => handleColorToggle(colorName)}
                  style={styles.colorTagRemoveButton}
                  disabled={disabled}
                >
                  <Ionicons name="close" size={14} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      <View style={styles.selectorContainer}>
        <Text style={styles.label}>Colors</Text>
        <TouchableOpacity
          style={[
            styles.selector,
            disabled && styles.selectorDisabled
          ]}
          onPress={openModal}
          disabled={disabled}
        >
          <Text style={styles.selectorText}>
            {selectedColors.length > 0
              ? `${selectedColors.length} color${selectedColors.length > 1 ? 's' : ''} selected`
              : 'Select colors'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#666" />
        </TouchableOpacity>

        {renderSelectedColors()}
      </View>

      {/* Color Selection Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Colors</Text>
              <TouchableOpacity
                onPress={() => setShowModal(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />              <TextInput
                style={styles.searchInput}
                placeholder="Search colors..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                placeholderTextColor="#000"
              />
            </View>

            <FlatList
              data={filteredColors}
              keyExtractor={(item) => item.name}
              renderItem={renderColorItem}
              style={styles.colorsList}
              numColumns={2}
              columnWrapperStyle={styles.colorRow}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  selectorContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  selectorDisabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedColorsContainer: {
    minHeight: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  colorsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 10,
  },
  colorTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 3,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  colorTagCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 6,
  },
  whiteColorBorder: {
    borderWidth: 1,
    borderColor: '#ccc',
  },
  colorTagText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  colorTagRemoveButton: {
    padding: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  colorsList: {
    maxHeight: 400,
    paddingHorizontal: 10,
  },
  colorRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  colorItem: {
    flex: 0.48,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  selectedColorItem: {
    backgroundColor: '#fff5f5',
    borderColor: '#FF6B6B',
  },
  colorItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 10,
  },
  colorItemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  selectedColorItemText: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
});

export default ColorSelector;
