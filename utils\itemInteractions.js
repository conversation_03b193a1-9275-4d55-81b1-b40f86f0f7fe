import { auth, db } from '../firebase.config';
import {
  doc,
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  increment,
  arrayUnion,
  arrayRemove,
  serverTimestamp
} from 'firebase/firestore';
import { Alert } from 'react-native';

/**
 * Toggle like status for an item
 * @param {string} itemId - The ID of the item to like/unlike
 * @returns {Promise<Object>} Result object with success status and isLiked flag
 */
export const toggleLike = async (itemId) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('User not authenticated');
      return { success: false, error: 'User not authenticated' };
    }

    const userId = currentUser.uid;
    const itemRef = doc(db, 'clothingItems', itemId);
    const userLikesRef = collection(db, 'users', userId, 'likes');
    const likeQuery = query(userLikesRef, where('itemId', '==', itemId));

    // Check if the item exists
    const itemDoc = await getDoc(itemRef);
    if (!itemDoc.exists()) {
      console.error('Item does not exist');
      return { success: false, error: 'Item does not exist' };
    }

    // Check if the user already liked this item
    const likeSnapshot = await getDocs(likeQuery);
    const isLiked = !likeSnapshot.empty;

    if (isLiked) {
      // Unlike: Remove from user's likes collection
      for (const doc of likeSnapshot.docs) {
        await deleteDoc(doc.ref);
      }

      // Update item's like count and likedBy array
      await updateDoc(itemRef, {
        likeCount: increment(-1),
        likedBy: arrayRemove(userId)
      });

      console.log(`User ${userId} unliked item ${itemId}`);
      return { success: true, isLiked: false };
    } else {
      // Like: Add to user's likes collection
      await addDoc(userLikesRef, {
        itemId,
        timestamp: serverTimestamp()
      });

      // Update item's like count and likedBy array
      await updateDoc(itemRef, {
        likeCount: increment(1),
        likedBy: arrayUnion(userId)
      });

      console.log(`User ${userId} liked item ${itemId}`);
      return { success: true, isLiked: true };
    }
  } catch (error) {
    console.error('Error toggling like:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Add an item to a collection (save/wishlist)
 * @param {string} itemId - The ID of the item to save
 * @param {string} collectionId - The ID of the collection to save to
 * @param {Object} itemData - Additional item data to save
 * @returns {Promise<Object>} Result object with success status
 */
export const saveToCollection = async (itemId, collectionId, itemData = {}) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('User not authenticated');
      return { success: false, error: 'User not authenticated' };
    }

    const userId = currentUser.uid;
    const itemRef = doc(db, 'clothingItems', itemId);

    // Check if the item exists
    const itemDoc = await getDoc(itemRef);
    if (!itemDoc.exists()) {
      console.error('Item does not exist');
      return { success: false, error: 'Item does not exist' };
    }

    // Add to user's wishlist collection
    const wishlistRef = collection(db, 'users', userId, 'wishlist');
    await addDoc(wishlistRef, {
      itemId,
      collectionId,
      addedAt: serverTimestamp(),
      ...itemData
    });

    // Update item's save count
    await updateDoc(itemRef, {
      saveCount: increment(1)
    });

    console.log(`User ${userId} saved item ${itemId} to collection ${collectionId}`);
    return { success: true };
  } catch (error) {
    console.error('Error saving to collection:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Add an item to the cart
 * @param {string} itemId - The ID of the item to add to cart
 * @param {Object} itemData - Item data to save in cart
 * @returns {Promise<Object>} Result object with success status
 */
export const addToCart = async (itemId, itemData = {}) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('User not authenticated');
      return { success: false, error: 'User not authenticated' };
    }

    const userId = currentUser.uid;

    // Ensure all required fields are present and not undefined
    const cartItem = {
      itemId,
      addedAt: serverTimestamp(),
      title: itemData.title || 'Untitled Item',
      category: itemData.category || 'Uncategorized',
      brand: itemData.brand || 'Unknown Brand',
      imageUrl: itemData.imageUrl || null,
      size: itemData.size || 'One Size',
      price: itemData.price || 0,
      quantity: 1
    };

    // Add to user's cart collection
    const cartRef = collection(db, 'users', userId, 'cart');
    const docRef = await addDoc(cartRef, cartItem);

    console.log(`User ${userId} added item ${itemId} to cart`);
    return { success: true, docId: docRef.id };
  } catch (error) {
    console.error('Error adding to cart:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check if an item is in the user's cart
 * @param {string} itemId - The ID of the item to check
 * @returns {Promise<boolean>} True if the item is in the cart
 */
export const isItemInCart = async (itemId) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) return false;

    const userId = currentUser.uid;
    const cartRef = collection(db, 'users', userId, 'cart');
    const q = query(cartRef, where('itemId', '==', itemId));
    const snapshot = await getDocs(q);

    return !snapshot.empty;
  } catch (error) {
    console.error('Error checking cart status:', error);
    return false;
  }
};

/**
 * Remove an item from the cart
 * @param {string} cartItemId - The ID of the cart item to remove
 * @returns {Promise<Object>} Result object with success status
 */
export const removeFromCart = async (cartItemId) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('User not authenticated');
      return { success: false, error: 'User not authenticated' };
    }

    const userId = currentUser.uid;
    await deleteDoc(doc(db, 'users', userId, 'cart', cartItemId));

    console.log(`User ${userId} removed item ${cartItemId} from cart`);
    return { success: true };
  } catch (error) {
    console.error('Error removing from cart:', error);
    return { success: false, error: error.message };
  }
};
