import { Dimensions, Platform, UIManager } from 'react-native';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// Screen dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Card configuration
export const CARD_WIDTH_PERCENTAGE = 0.95; // 95% of screen width for cards
export const CARD_ASPECT_RATIO = 2 / 3; // Portrait aspect ratio for clothing items

// Calculate available height for cards (screen height minus header, filter, and safe areas)
const HEADER_HEIGHT = 50; // FeedHeader height
const FILTER_HEIGHT = 52; // EnhancedCategoryFilter approximate height
const SAFE_AREA_BUFFER = 40; // Buffer for safe areas and spacing
const AVAILABLE_HEIGHT = SCREEN_HEIGHT - HEADER_HEIGHT - FILTER_HEIGHT - SAFE_AREA_BUFFER;

// Use the smaller of calculated height or available height to ensure it fits
const CALCULATED_HEIGHT = (SCREEN_WIDTH * CARD_WIDTH_PERCENTAGE) / CARD_ASPECT_RATIO;
export const CARD_HEIGHT = Math.min(CALCULATED_HEIGHT, AVAILABLE_HEIGHT * 0.85); // Use 85% of available height
export const ROTATION_ANGLE = 12; // Slightly reduced rotation angle for more subtle effect
export const VISIBLE_CARDS = 3; // Show fewer cards for better performance
export const CARD_SCALE_DECREMENT = 0.03; // Smaller scale difference between cards
export const CARD_POSITION_OFFSET = 15; // Smaller position offset for tighter stacking

// Performance and interaction settings
export const PRELOAD_THRESHOLD = 5; // Preload fewer images for faster initial load
export const UP_SWIPE_THRESHOLD = -15; // Ultra-sensitive upward detection
export const SWIPE_SENSITIVITY = 0.005; // Extremely sensitive - even 0.5% of screen width will trigger

// Screen dimensions export
export { SCREEN_WIDTH, SCREEN_HEIGHT };

// Default categories
export const defaultCategories = [
  'All',
  'Shirts',
  'T-Shirts', 
  'Blouses',
  'Sweaters',
  'Hoodies',
  'Tops',
  'Jeans',
  'Pants',
  'Shorts',
  'Skirts',
  'Leggings',
  'Casual Dresses',
  'Formal Dresses',
  'Party Dresses',
  'Jackets',
  'Coats',
  'Cardigans',
  'Sneakers',
  'Boots',
  'Heels',
  'Flats',
  'Sandals',
  'Bags',
  'Jewelry',
  'Hats',
  'Scarves',
  'Belts',
  'Sunglasses'
];

// Animation durations
export const ANIMATION_DURATION = 300;
export const LIKE_ANIMATION_DURATION = 1000;
export const CART_ANIMATION_DURATION = 1500;

// Timeouts
export const LOADING_TIMEOUT = 5000;
export const INTERACTED_IDS_TIMEOUT = 5000;
export const BACKUP_TIMEOUT = 5000;

// Query limits
export const QUERY_LIMIT = 100;
export const INITIAL_RENDER_COUNT = 10;
export const MAX_RENDER_PER_BATCH = 10;
export const UPDATE_CELLS_BATCHING_PERIOD = 50;
export const WINDOW_SIZE = 5;
