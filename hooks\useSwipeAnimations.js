import { useRef, useCallback } from 'react';
import { Platform, Animated as RNAnimated } from 'react-native';

export const useSwipeAnimations = () => {
  // Animation refs
  const likeAnimationOpacity = useRef(new RNAnimated.Value(0)).current;
  const dislikeAnimationOpacity = useRef(new RNAnimated.Value(0)).current;
  // Ensure scale animations are defined but not animated if not needed
  const likeAnimationScale = useRef(new RNAnimated.Value(1)).current; // Initialize to 1
  const dislikeAnimationScale = useRef(new RNAnimated.Value(1)).current; // Initialize to 1

  // Trigger like animation
  const triggerLikeAnimation = useCallback(() => {
    console.log('[useSwipeAnimations] Triggering like animation');

    // Reset animation values
    likeAnimationOpacity.setValue(0);
    likeAnimationScale.setValue(1); // Ensure scale is 1

    setTimeout(() => {
      const animDuration = Platform.OS === 'android' ? 100 : 50;

      RNAnimated.timing(likeAnimationOpacity, { // Use timing for opacity
        toValue: 1,
        duration: animDuration,
        useNativeDriver: true
      }).start();
      // No longer animating scale for like

      const displayDuration = Platform.OS === 'android' ? 500 : 300;

      const hideTimer = setTimeout(() => {
        RNAnimated.timing(likeAnimationOpacity, {
          toValue: 0,
          duration: animDuration,
          useNativeDriver: true
        }).start(() => {
          console.log('[useSwipeAnimations] Like animation completed');
        });
      }, displayDuration);

      return () => clearTimeout(hideTimer);
    }, 10);
  }, [likeAnimationOpacity, likeAnimationScale]); // Added likeAnimationScale back

  // Trigger dislike animation
  const triggerDislikeAnimation = useCallback(() => {
    console.log('[useSwipeAnimations] Triggering dislike animation');

    // Reset animation values
    dislikeAnimationOpacity.setValue(0);
    dislikeAnimationScale.setValue(1); // Ensure scale is 1

    setTimeout(() => {
      const animDuration = Platform.OS === 'android' ? 100 : 50;

      RNAnimated.timing(dislikeAnimationOpacity, { // Use timing for opacity
        toValue: 1,
        duration: animDuration,
        useNativeDriver: true
      }).start();
      // No longer animating scale for dislike

      const displayDuration = Platform.OS === 'android' ? 500 : 300;

      const hideTimer = setTimeout(() => {
        RNAnimated.timing(dislikeAnimationOpacity, {
          toValue: 0,
          duration: animDuration,
          useNativeDriver: true
        }).start(() => {
          console.log('[useSwipeAnimations] Dislike animation completed');
        });
      }, displayDuration);

      return () => clearTimeout(hideTimer);
    }, 10);
  }, [dislikeAnimationOpacity, dislikeAnimationScale]); // Added dislikeAnimationScale back

  return {
    // Animation values
    likeAnimationOpacity,
    dislikeAnimationOpacity,
    // Ensure scale values are returned
    likeAnimationScale,
    dislikeAnimationScale,

    // Animation triggers
    triggerLikeAnimation,
    triggerDislikeAnimation
  };
};
