const functions = require('firebase-functions');
const admin = require('firebase-admin');
const SibApiV3Sdk = require('sib-api-v3-sdk'); // Brevo (formerly Sendinblue) SDK

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}

// Initialize Brevo API client
const defaultClient = SibApiV3Sdk.ApiClient.instance;
const apiKey = defaultClient.authentications['api-key'];
const brevoApiKey = functions.config().brevo?.apikey || process.env.BREVO_API_KEY;
console.log('[TransactionEmails] Using Brevo API Key:', brevoApiKey ? 'API key is set' : 'API key is MISSING or not configured!');

// Helper function to safely get nested properties
const getSafe = (fn, defaultValue) => {
    try {
        return fn() || defaultValue;
    } catch (e) {
        return defaultValue;
    }
};

/**
 * Send transaction status update email to seller
 */
const sendTransactionStatusUpdateToSellerInternal = async (transactionData, newStatus, adminNotes = '') => {
    try {
        console.log('[TransactionEmails] Internal: Sending transaction status update to seller:', getSafe(() => transactionData.sellerId, 'N/A'));
        console.log('[TransactionEmails] Internal: Transaction data:', JSON.stringify(transactionData, null, 2));
        console.log('[TransactionEmails] Internal: New status:', newStatus);
        console.log('[TransactionEmails] Internal: Admin notes:', adminNotes);        // Validate required fields
        if (!transactionData || !getSafe(() => transactionData.sellerEmail) || !getSafe(() => transactionData.sellerName)) {
            console.error('[TransactionEmails] Internal: Missing required transaction data. SellerEmail:', getSafe(() => transactionData.sellerEmail), 'SellerName:', getSafe(() => transactionData.sellerName));
            throw new functions.https.HttpsError('invalid-argument', 'Missing required transaction data: sellerEmail and sellerName are required.', {
                transactionDataProvided: {
                    hasTransactionData: !!transactionData,
                    hasSellerEmail: !!getSafe(() => transactionData.sellerEmail),
                    hasSellerName: !!getSafe(() => transactionData.sellerName)
                }
            });
        }

        // Validate Brevo API key
        if (!brevoApiKey) {
            console.error('[TransactionEmails] Internal: BREVO_API_KEY not found in environment variables or Firebase config.');
            throw new functions.https.HttpsError('internal', 'Email API key not configured. Please set functions.config().brevo.apikey or BREVO_API_KEY environment variable.');
        }

        const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
        // Ensure the API client instance used by apiInstance is authenticated
        const authenticatedApiClient = SibApiV3Sdk.ApiClient.instance;
        const apiKeyAuth = authenticatedApiClient.authentications['api-key'];
        apiKeyAuth.apiKey = brevoApiKey; // Explicitly set on the instance used by TransactionalEmailsApi

        // Determine email subject and content based on status
        let subject, htmlContent;
        const statusText = getStatusDisplayText(newStatus);
        const transactionNumber = getSafe(() => transactionData.transactionNumber, 'N/A');
        const amount = `₹${(getSafe(() => transactionData.amount, 0)).toFixed(2)}`;
        const sellerName = getSafe(() => transactionData.sellerName, 'Valued Seller');
        const orderId = getSafe(() => transactionData.orderId, 'N/A');
        const itemsCount = getSafe(() => transactionData.itemsCount, 0);        // Transfer details - ensure they exist or provide defaults
        const transferMethod = getSafe(() => transactionData.transactionDetails.method, 'Bank Transfer');
        const referenceNumber = getSafe(() => transactionData.transactionDetails.referenceNumber, 'N/A');
        const bankName = getSafe(() => transactionData.transactionDetails.bankName, 'Your Registered Bank');        // Email configuration
        const configuredSenderEmail = '<EMAIL>'; // Fixed sender email
        const configuredSupportEmail = '<EMAIL>'; // Use same email for support

        // Date formatting
        const transferDate = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        const transferTime = new Date().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });

        switch (newStatus) {
            case 'transfer_in_progress':
                subject = `Transaction ${transactionNumber} - Transfer In Progress`;
                htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2196F3;">Transaction Update</h2>
            <p>Dear ${sellerName},</p>
            <p>Your transaction <strong>${transactionNumber}</strong> for <strong>${amount}</strong> is now being processed for transfer.</p>
            
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Transaction Details:</h3>
              <p><strong>Transaction ID:</strong> ${transactionNumber}</p>
              <p><strong>Amount:</strong> ${amount}</p>
              <p><strong>Status:</strong> ${statusText}</p>
              <p><strong>Order ID:</strong> ${orderId}</p>
              <p><strong>Items:</strong> ${itemsCount} item(s)</p>
            </div>

            <p>The funds will be transferred to your registered bank account within 1-2 business days.</p>
            <p>You will receive another notification once the transfer is completed.</p>

            <p>Best regards,<br>SwipeSense Team</p>
          </div>
        `;
                break;

            case 'transferred':
            case 'completed':
                subject = `Transaction ${transactionNumber} - Payment Transferred`;
                htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4CAF50;">💰 Payment Transferred Successfully!</h2>
            <p>Dear ${sellerName},</p>
            <p>Great news! Your transaction <strong>${transactionNumber}</strong> for <strong>${amount}</strong> has been successfully transferred to your bank account.</p>
            
            <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4CAF50;">
              <h3 style="color: #2e7d32; margin-top: 0;">💳 Transfer Details:</h3>
              <p><strong>Transaction ID:</strong> ${transactionNumber}</p>
              <p><strong>Amount Transferred:</strong> <span style="color: #2e7d32; font-size: 18px; font-weight: bold;">${amount}</span></p>
              <p><strong>Transfer Method:</strong> ${transferMethod}</p>
              <p><strong>Reference Number:</strong> ${referenceNumber}</p>
              <p><strong>Bank Name:</strong> ${bankName}</p>
              <p><strong>Order ID:</strong> ${orderId}</p>
              <p><strong>Items:</strong> ${itemsCount} item(s)</p>
              <p><strong>Transfer Date:</strong> ${transferDate} at ${transferTime}</p>
            </div>

            ${adminNotes ? `
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;">
              <p><strong>📝 Admin Notes:</strong> ${adminNotes}</p>
            </div>` : ''}

            <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h4 style="color: #1976d2; margin-top: 0;">⏰ What happens next?</h4>
              <p>• The funds should reflect in your bank account within a few hours</p>
              <p>• If you don't see the payment within 24 hours, please contact our support team</p>
              <p>• Keep the reference number for your records</p>
              <p>• You can check your transaction history in the app anytime</p>
            </div>

            <p>Thank you for selling on SwipeSense! We appreciate your business and look forward to more successful transactions.</p>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 14px;">
                Best regards,<br>
                <strong>SwipeSense Team</strong><br>
                📧 ${configuredSupportEmail} | 📱 Customer Support
              </p>
            </div>
          </div>
        `;
                break;

            default:
                console.log('[TransactionEmails] Internal: Unknown status for email:', newStatus);
                throw new functions.https.HttpsError('invalid-argument', `Unknown status for email: ${newStatus}`);
        }        const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
        sendSmtpEmail.subject = subject;
        sendSmtpEmail.htmlContent = htmlContent;
        sendSmtpEmail.sender = {
            name: 'SwipeSense',
            email: configuredSenderEmail
        };
        sendSmtpEmail.to = [{
            email: getSafe(() => transactionData.sellerEmail),
            name: sellerName
        }];
        sendSmtpEmail.replyTo = {
            email: configuredSupportEmail,
            name: 'SwipeSense Support'
        };

        // Add headers for better deliverability
        sendSmtpEmail.headers = {
            'X-Mailin-custom': 'transaction-notification',
            'X-Mailin-Tag': 'TransactionNotification'
        };

        // Add tags for better tracking
        sendSmtpEmail.tags = ['transaction-notification', `status-${newStatus}`];
        if (transactionNumber && transactionNumber !== 'N/A') {
            sendSmtpEmail.tags.push(`transaction-${transactionNumber}`);
        }

        console.log('[TransactionEmails] Internal: Prepared email object:', JSON.stringify({
            subject: sendSmtpEmail.subject,
            to: sendSmtpEmail.to,
            sender: sendSmtpEmail.sender,
            tags: sendSmtpEmail.tags
        }, null, 2));

        await apiInstance.sendTransacEmail(sendSmtpEmail);
        console.log('[TransactionEmails] Internal: Transaction status email sent successfully to seller');
        return { success: true, message: 'Email sent successfully.' };

    } catch (error) {
        console.error('[TransactionEmails] Internal: Error sending transaction status email to seller. Raw error:', error);
        let errorMessage = 'An unexpected error occurred while sending the email.';
        let errorCode = 'internal';

        if (error.response && error.response.text) {
            console.error('[TransactionEmails] Internal: Sendinblue API Error Body:', error.response.text);
            errorMessage = `Sendinblue API Error: ${error.response.text}`;
            errorCode = 'third-party-error';
        } else if (error.message) {
            errorMessage = error.message;
        }
        if (error.code && error.code.startsWith('functions.https.HttpsError')) {
            throw error; // Re-throw HttpsError directly
        }
        // For other errors, wrap them in HttpsError
        throw new functions.https.HttpsError(errorCode, errorMessage, { originalError: error.toString() });
    }
};

/**
 * Callable function wrapper for sendTransactionStatusUpdateToSellerInternal
 */
exports.sendTransactionStatusUpdateToSeller = functions.https.onCall(async (data, context) => {
    console.log('[TransactionEmails] Callable: Received request for sendTransactionStatusUpdateToSeller');
    console.log('[TransactionEmails] Callable: Data received:', JSON.stringify(data, null, 2));

    // Basic auth check (optional, but good practice)
    // if (!context.auth) {
    //     throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    // }

    const { transactionData, newStatus, adminNotes } = data;

    if (!transactionData || !newStatus) {
        console.error('[TransactionEmails] Callable: Missing transactionData or newStatus in request.');
        throw new functions.https.HttpsError('invalid-argument', 'The function must be called with transactionData and newStatus arguments.');
    }

    try {
        return await sendTransactionStatusUpdateToSellerInternal(transactionData, newStatus, adminNotes);
    } catch (error) {
        console.error('[TransactionEmails] Callable: Error calling internal email function:', error);
        // Ensure HttpsError is thrown, otherwise Firebase callable will return a generic error
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', error.message || 'Failed to send transaction status email.', { originalError: error.toString() });
    }
});


/**
 * Send transaction notification to admin
 */
const sendTransactionNotificationToAdminInternal = async (transactionData, eventType) => {
    try {
        console.log('[TransactionEmails] Internal: Sending transaction notification to admin:', eventType);
        console.log('[TransactionEmails] Internal: Admin Transaction data:', JSON.stringify(transactionData, null, 2));        // Validate required fields for admin email
        if (!transactionData || !getSafe(() => transactionData.transactionNumber) || !getSafe(() => transactionData.amount, -1 >= 0)) {
            console.error('[TransactionEmails] Internal: Missing required data for admin email. transactionNumber:', getSafe(() => transactionData.transactionNumber), 'amount:', getSafe(() => transactionData.amount));
            throw new functions.https.HttpsError('invalid-argument', 'Missing required transaction data for admin email.');
        }

        // Validate Brevo API key
        if (!brevoApiKey) {
            console.error('[TransactionEmails] Internal: BREVO_API_KEY not found for admin email.');
            throw new functions.https.HttpsError('internal', 'Email API key not configured for admin. Please set functions.config().brevo.apikey or BREVO_API_KEY.');
        }

        const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
        const authenticatedApiClient = SibApiV3Sdk.ApiClient.instance; const apiKeyAuth = authenticatedApiClient.authentications['api-key'];
        apiKeyAuth.apiKey = brevoApiKey; // Explicitly set for admin emails too

        const configuredSenderEmail = '<EMAIL>'; // Fixed sender email
        const targetAdminEmail = functions.config().admin?.email || process.env.ADMIN_EMAIL || '<EMAIL>';

        // Define variables needed for email templates
        const transactionNumber = getSafe(() => transactionData.transactionNumber, 'N/A');
        const amount = `₹${(getSafe(() => transactionData.amount, 0)).toFixed(2)}`;
        const sellerName = getSafe(() => transactionData.sellerName, 'N/A');
        const sellerEmail = getSafe(() => transactionData.sellerEmail, 'N/A');
        const orderId = getSafe(() => transactionData.orderId, 'N/A');
        const itemsCount = getSafe(() => transactionData.itemsCount, 0);
        const processedBy = getSafe(() => transactionData.processedBy, 'System');

        let subject, htmlContent;

        switch (eventType) {
            case 'new_transaction':
                subject = `New Transaction Awaiting Transfer - ${transactionNumber}`;
                htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF9800;">New Transaction Requires Action</h2>
            <p>A new seller transaction is awaiting transfer approval.</p>
            
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Transaction Details:</h3>
              <p><strong>Transaction ID:</strong> ${transactionNumber}</p>
              <p><strong>Seller:</strong> ${sellerName} (${sellerEmail})</p>
              <p><strong>Amount:</strong> ${amount}</p>
              <p><strong>Order ID:</strong> ${orderId}</p>
              <p><strong>Items:</strong> ${itemsCount} item(s)</p>
              <p><strong>Created:</strong> ${new Date().toLocaleString()}</p>
            </div>

            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
              <p><strong>Action Required:</strong> Please review this transaction in the admin dashboard and process the transfer.</p>
            </div>

            <p>Best regards,<br>SwipeSense System</p>
          </div>
        `;
                break;

            case 'transfer_completed':
                subject = `Transaction Transfer Completed - ${transactionNumber}`;
                htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4CAF50;">Transaction Transfer Completed</h2>
            <p>A seller transaction has been successfully processed and transferred.</p>
            
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Transaction Details:</h3>
              <p><strong>Transaction ID:</strong> ${transactionNumber}</p>
              <p><strong>Seller:</strong> ${sellerName} (${sellerEmail})</p>
              <p><strong>Amount Transferred:</strong> ${amount}</p>
              <p><strong>Transfer Method:</strong> ${getSafe(() => transactionData.transactionDetails.method, 'Bank Transfer')}</p>
              <p><strong>Reference Number:</strong> ${getSafe(() => transactionData.transactionDetails.referenceNumber, 'N/A')}</p>
              <p><strong>Processed By:</strong> ${processedBy}</p>
            </div>

            <p>The seller has been notified of the successful transfer.</p>
            <p>Best regards,<br>SwipeSense System</p>
          </div>
        `;
                break; default:
                console.log('[TransactionEmails] Internal: Unknown event type for admin email:', eventType);
                throw new functions.https.HttpsError('invalid-argument', `Unknown event type for admin email: ${eventType}`);
        }

        const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
        sendSmtpEmail.subject = subject;
        sendSmtpEmail.htmlContent = htmlContent;
        sendSmtpEmail.sender = {
            name: 'SwipeSense System',
            email: configuredSenderEmail
        };
        sendSmtpEmail.to = [{
            email: targetAdminEmail, // Use the renamed variable
            name: 'SwipeSense Admin'
        }];
        sendSmtpEmail.replyTo = {
            email: configuredSenderEmail,
            name: 'SwipeSense System Notification'
        };

        // Add headers for better deliverability
        sendSmtpEmail.headers = {
            'X-Mailin-custom': 'admin-transaction-notification',
            'X-Mailin-Tag': 'AdminTransactionNotification'
        };

        // Add tags for better tracking
        sendSmtpEmail.tags = ['admin-notification', `event-${eventType}`];
        if (transactionNumber && transactionNumber !== 'N/A') {
            sendSmtpEmail.tags.push(`transaction-${transactionNumber}`);
        }

        await apiInstance.sendTransacEmail(sendSmtpEmail);
        console.log('[TransactionEmails] Internal: Admin notification email sent successfully');
        return { success: true, message: 'Admin email sent successfully.' };

    } catch (error) {
        console.error('[TransactionEmails] Internal: Error sending admin notification email. Raw error:', error);
        let errorMessage = 'An unexpected error occurred while sending the admin email.';
        let errorCode = 'internal';

        if (error.response && error.response.text) {
            console.error('[TransactionEmails] Internal: Sendinblue API Error Body (Admin):', error.response.text);
            errorMessage = `Sendinblue API Error: ${error.response.text}`;
            errorCode = 'third-party-error';
        } else if (error.message) {
            errorMessage = error.message;
        }
        if (error.code && error.code.startsWith('functions.https.HttpsError')) {
            throw error; // Re-throw HttpsError directly
        }
        throw new functions.https.HttpsError(errorCode, errorMessage, { originalError: error.toString() });
    }
};

/**
 * Callable function wrapper for sendTransactionNotificationToAdminInternal
 */
exports.sendTransactionNotificationToAdmin = functions.https.onCall(async (data, context) => {
    console.log('[TransactionEmails] Callable: Received request for sendTransactionNotificationToAdmin');
    console.log('[TransactionEmails] Callable: Data received for admin:', JSON.stringify(data, null, 2));

    const { transactionData, eventType } = data;

    if (!transactionData || !eventType) {
        console.error('[TransactionEmails] Callable: Missing transactionData or eventType in admin request.');
        throw new functions.https.HttpsError('invalid-argument', 'The function must be called with transactionData and eventType arguments for admin.');
    }

    try {
        return await sendTransactionNotificationToAdminInternal(transactionData, eventType);
    } catch (error) {
        console.error('[TransactionEmails] Callable: Error calling internal admin email function:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', error.message || 'Failed to send admin transaction notification.', { originalError: error.toString() });
    }
});


// Helper function to get display text for status
function getStatusDisplayText(status) {
    switch (status) {
        case 'pending_transfer': return 'Pending Transfer';
        case 'transfer_in_progress': return 'Transfer In Progress';
        case 'transferred': return 'Transferred to Bank';
        case 'completed': return 'Completed & Transferred';
        case 'failed': return 'Transfer Failed';
        case 'cancelled': return 'Cancelled';
        default: return status;
    }
}
