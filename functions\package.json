{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "echo \"No linting configured\""}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^11.8.0", "firebase-functions": "^4.3.1", "razorpay": "^2.9.6", "sib-api-v3-sdk": "^8.5.0"}, "private": true}