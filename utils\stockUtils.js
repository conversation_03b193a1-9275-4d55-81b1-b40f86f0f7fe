import { doc, updateDoc, increment, runTransaction, getDoc } from 'firebase/firestore';
import { db } from '../firebase.config';

/**
 * Stock Management Utilities
 * Handles all stock-related operations with atomic transactions to prevent overselling
 */

/**
 * Check if an item has sufficient stock
 * @param {string} itemId - The item ID to check
 * @param {number} requestedQuantity - The quantity requested
 * @returns {Promise<{available: boolean, currentStock: number, message: string}>}
 */
export const checkStockAvailability = async (itemId, requestedQuantity = 1) => {
    try {
        const itemRef = doc(db, 'clothingItems', itemId);
        const itemDoc = await getDoc(itemRef);

        if (!itemDoc.exists()) {
            return {
                available: false,
                currentStock: 0,
                message: 'Item not found'
            };
        }

        const itemData = itemDoc.data();
        const currentStock = itemData.stock || 0;
        const isAvailable = currentStock >= requestedQuantity;

        return {
            available: isAvailable,
            currentStock,
            message: isAvailable
                ? 'Stock available'
                : `Only ${currentStock} items left in stock`
        };
    } catch (error) {
        console.error('Error checking stock availability:', error);
        return {
            available: false,
            currentStock: 0,
            message: 'Error checking stock'
        };
    }
};

/**
 * Reserve stock for an item (atomic operation)
 * This should be called when adding items to cart or during checkout
 * @param {string} itemId - The item ID
 * @param {number} quantity - Quantity to reserve
 * @returns {Promise<{success: boolean, message: string, newStock: number}>}
 */
export const reserveStock = async (itemId, quantity = 1) => {
    try {
        const itemRef = doc(db, 'clothingItems', itemId);

        const result = await runTransaction(db, async (transaction) => {
            const itemDoc = await transaction.get(itemRef);

            if (!itemDoc.exists()) {
                throw new Error('Item not found');
            }

            const itemData = itemDoc.data();
            const currentStock = itemData.stock || 0;

            if (currentStock < quantity) {
                throw new Error(`Insufficient stock. Only ${currentStock} items available`);
            }

            const newStock = currentStock - quantity;

            // Update stock and increment reserved count
            transaction.update(itemRef, {
                stock: newStock,
                reservedStock: increment(quantity),
                lastStockUpdate: new Date(),
                stockStatus: newStock === 0 ? 'out_of_stock' : newStock <= 5 ? 'low_stock' : 'in_stock'
            });

            return { newStock, success: true };
        });

        return {
            success: true,
            message: 'Stock reserved successfully',
            newStock: result.newStock
        };
    } catch (error) {
        console.error('Error reserving stock:', error);
        return {
            success: false,
            message: error.message || 'Failed to reserve stock',
            newStock: 0
        };
    }
};

/**
 * Release reserved stock (e.g., when removing from cart)
 * @param {string} itemId - The item ID
 * @param {number} quantity - Quantity to release
 * @returns {Promise<{success: boolean, message: string}>}
 */
export const releaseStock = async (itemId, quantity = 1) => {
    try {
        const itemRef = doc(db, 'clothingItems', itemId);

        await runTransaction(db, async (transaction) => {
            const itemDoc = await transaction.get(itemRef);

            if (!itemDoc.exists()) {
                throw new Error('Item not found');
            }

            const itemData = itemDoc.data();
            const currentStock = itemData.stock || 0;
            const reservedStock = itemData.reservedStock || 0;

            const newStock = currentStock + quantity;
            const newReservedStock = Math.max(0, reservedStock - quantity);

            transaction.update(itemRef, {
                stock: newStock,
                reservedStock: newReservedStock,
                lastStockUpdate: new Date(),
                stockStatus: newStock === 0 ? 'out_of_stock' : newStock <= 5 ? 'low_stock' : 'in_stock'
            });
        });

        return {
            success: true,
            message: 'Stock released successfully'
        };
    } catch (error) {
        console.error('Error releasing stock:', error);
        return {
            success: false,
            message: error.message || 'Failed to release stock'
        };
    }
};

/**
 * Confirm stock depletion (called after successful payment)
 * This converts reserved stock to sold stock
 * @param {string} itemId - The item ID
 * @param {number} quantity - Quantity sold
 * @returns {Promise<{success: boolean, message: string}>}
 */
export const confirmStockSale = async (itemId, quantity = 1) => {
    try {
        const itemRef = doc(db, 'clothingItems', itemId);

        await runTransaction(db, async (transaction) => {
            const itemDoc = await transaction.get(itemRef);

            if (!itemDoc.exists()) {
                throw new Error('Item not found');
            }

            const itemData = itemDoc.data();
            const reservedStock = itemData.reservedStock || 0;
            const soldCount = itemData.soldCount || 0;

            const newReservedStock = Math.max(0, reservedStock - quantity);
            const newSoldCount = soldCount + quantity;

            transaction.update(itemRef, {
                reservedStock: newReservedStock,
                soldCount: newSoldCount,
                lastSale: new Date(),
                lastStockUpdate: new Date()
            });
        });

        return {
            success: true,
            message: 'Stock sale confirmed'
        };
    } catch (error) {
        console.error('Error confirming stock sale:', error);
        return {
            success: false,
            message: error.message || 'Failed to confirm stock sale'
        };
    }
};

/**
 * Update stock quantity (for sellers)
 * @param {string} itemId - The item ID
 * @param {number} newStock - New stock quantity
 * @returns {Promise<{success: boolean, message: string}>}
 */
export const updateStock = async (itemId, newStock) => {
    try {
        if (newStock < 0) {
            return {
                success: false,
                message: 'Stock quantity cannot be negative'
            };
        }

        const itemRef = doc(db, 'clothingItems', itemId);

        await updateDoc(itemRef, {
            stock: newStock,
            lastStockUpdate: new Date(),
            stockStatus: newStock === 0 ? 'out_of_stock' : newStock <= 5 ? 'low_stock' : 'in_stock'
        });

        return {
            success: true,
            message: 'Stock updated successfully'
        };
    } catch (error) {
        console.error('Error updating stock:', error);
        return {
            success: false,
            message: error.message || 'Failed to update stock'
        };
    }
};

/**
 * Get stock status for display
 * @param {number} stock - Current stock number
 * @returns {object} Stock status with display info
 */
export const getStockStatus = (stock) => {
    if (stock === 0) {
        return {
            status: 'out_of_stock',
            label: 'Out of Stock',
            color: '#FF3B30',
            available: false
        };
    } else if (stock <= 5) {
        return {
            status: 'low_stock',
            label: `Only ${stock} left!`,
            color: '#FF9500',
            available: true,
            showWarning: true
        };
    } else {
        return {
            status: 'in_stock',
            label: 'In Stock',
            color: '#34C759',
            available: true
        };
    }
};

/**
 * Batch check stock for multiple items (useful for cart validation)
 * @param {Array} items - Array of {itemId, quantity} objects
 * @returns {Promise<Array>} Array of stock check results
 */
export const batchCheckStock = async (items) => {
    try {
        const results = await Promise.all(
            items.map(async (item) => {
                const stockCheck = await checkStockAvailability(item.itemId, item.quantity);
                return {
                    itemId: item.itemId,
                    requestedQuantity: item.quantity,
                    ...stockCheck
                };
            })
        );

        return results;
    } catch (error) {
        console.error('Error in batch stock check:', error);
        return items.map(item => ({
            itemId: item.itemId,
            requestedQuantity: item.quantity,
            available: false,
            currentStock: 0,
            message: 'Error checking stock'
        }));
    }
};

/**
 * Send low stock notification to seller
 * @param {string} itemId - The item ID
 * @param {string} sellerId - The seller ID
 * @param {number} currentStock - Current stock level
 */
export const sendLowStockNotification = async (itemId, sellerId, currentStock) => {
    try {
        // This would typically send an email or push notification
        // For now, we'll log it and could integrate with your existing email system
        console.log(`Low stock alert: Item ${itemId} has ${currentStock} units left`);

        // You could integrate this with your existing email functions
        // await sendEmail({
        //   to: sellerEmail,
        //   subject: 'Low Stock Alert',
        //   template: 'low-stock-notification',
        //   data: { itemId, currentStock }
        // });

        return true;
    } catch (error) {
        console.error('Error sending low stock notification:', error);
        return false;
    }
};