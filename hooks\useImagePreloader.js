import { useState, useCallback } from 'react';
import { Image } from 'react-native';
import { getLowQualityImageUrl } from '../utils/imageUtils';

export const useImagePreloader = () => {
  const [preloadedImages, setPreloadedImages] = useState(new Set());

  const preloadImages = useCallback((itemsToPreload) => {
    if (!itemsToPreload || itemsToPreload.length === 0) return;

    // Only preload images that haven't been preloaded yet
    const imagesToPreload = itemsToPreload
      .filter(item => item && item.imageUrl && !preloadedImages.has(item.imageUrl));

    if (imagesToPreload.length > 0) {
      console.log(`[ImagePreloader] Preloading ${imagesToPreload.length} images`);

      // Mark all images as preloaded immediately to prevent duplicate attempts
      const newPreloadedImages = new Set(preloadedImages);
      imagesToPreload.forEach(item => {
        if (item && item.imageUrl) {
          newPreloadedImages.add(item.imageUrl);
        }
      });
      setPreloadedImages(newPreloadedImages);

      // Ultra-prioritize the first 3 images (next cards) for immediate display
      const ultraPriorityImages = imagesToPreload.slice(0, 3);
      // High priority for the next batch
      const highPriorityImages = imagesToPreload.slice(3, 10);
      // Normal priority for the rest
      const normalPriorityImages = imagesToPreload.slice(10);

      // Use Image.prefetch for all images with different priorities
      console.log('[ImagePreloader] Using optimized Image.prefetch for lightning-fast preloading');

      // Preload ultra-priority images immediately with highest priority
      ultraPriorityImages.forEach(item => {
        if (item && item.imageUrl) {
          // Create a new image object for fastest loading in React Native
          const imgObj = new Object();
          imgObj.src = getLowQualityImageUrl(item.imageUrl);

          // Also use prefetch as a backup
          Image.prefetch(getLowQualityImageUrl(item.imageUrl))
            .catch(error => {
              console.error(`[ImagePreloader] Failed to prefetch ultra-priority image: ${item.imageUrl}`, error);
            });
        }
      });

      // Preload high priority images immediately
      highPriorityImages.forEach(item => {
        if (item && item.imageUrl) {
          Image.prefetch(getLowQualityImageUrl(item.imageUrl))
            .catch(error => {
              console.error(`[ImagePreloader] Failed to prefetch high-priority image: ${item.imageUrl}`, error);
            });
        }
      });

      // Preload normal priority images with slight delay to prioritize the important ones
      setTimeout(() => {
        normalPriorityImages.forEach(item => {
          if (item && item.imageUrl) {
            Image.prefetch(getLowQualityImageUrl(item.imageUrl))
              .catch(error => {
                console.error(`[ImagePreloader] Failed to prefetch normal-priority image: ${item.imageUrl}`, error);
              });
          }
        });
      }, 100);
    }
  }, [preloadedImages]);

  const isImagePreloaded = useCallback((imageUrl) => {
    return preloadedImages.has(imageUrl);
  }, [preloadedImages]);

  const clearPreloadedImages = useCallback(() => {
    setPreloadedImages(new Set());
  }, []);

  return {
    preloadImages,
    isImagePreloaded,
    clearPreloadedImages,
    preloadedImages
  };
};
