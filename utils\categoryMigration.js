import { collection, getDocs, doc, updateDoc, writeBatch } from 'firebase/firestore';
import { db } from '../firebase.config';
import { getBroadCategoryForDetailed, isBroadCategory, isDetailedCategory, getDetailedCategories } from './categoryHierarchy';

/**
 * Migration utility to update existing clothing items with hierarchical categories
 */

// Mapping from old categories to new hierarchical structure
const categoryMappings = {
  // Tops
  'Tops': { broad: 'T-Shirts', detailed: 'Basic Tees' },
  'Shirts': { broad: 'Shirts', detailed: 'Casual Shirts' },
  'T-Shirts': { broad: 'T-Shirts', detailed: 'Basic Tees' },
  'Blouses': { broad: 'Blouses', detailed: 'Casual Blouses' },
  'Sweaters': { broad: 'Sweaters', detailed: 'Pullover Sweaters' },
  'Hoodies': { broad: 'Hoodies', detailed: 'Pullover Hoodies' },

  // Bottoms
  'Bottoms': { broad: 'Pants', detailed: 'Chinos' },
  'Jeans': { broad: 'Jeans', detailed: 'Skinny Jeans' },
  'Pants': { broad: 'Pants', detailed: 'Chinos' },
  'Shorts': { broad: 'Shorts', detailed: 'Chino Shorts' },
  'Skirts': { broad: 'Skirts', detailed: 'A-Line Skirts' },
  'Leggings': { broad: 'Leggings', detailed: 'Casual Leggings' },

  // Dresses
  'Dresses': { broad: 'Casual Dresses', detailed: 'Sundresses' },
  'Casual Dresses': { broad: 'Casual Dresses', detailed: 'Sundresses' },
  'Formal Dresses': { broad: 'Formal Dresses', detailed: 'Cocktail Dresses' },
  'Party Dresses': { broad: 'Party Dresses', detailed: 'Sequin Dresses' },

  // Outerwear
  'Outerwear': { broad: 'Jackets', detailed: 'Casual Jackets' },
  'Jackets': { broad: 'Jackets', detailed: 'Denim Jackets' },
  'Coats': { broad: 'Coats', detailed: 'Trench Coats' },
  'Cardigans': { broad: 'Cardigans', detailed: 'Open Front Cardigans' },

  // Shoes
  'Shoes': { broad: 'Sneakers', detailed: 'Casual Sneakers' },
  'Sneakers': { broad: 'Sneakers', detailed: 'Casual Sneakers' },
  'Boots': { broad: 'Boots', detailed: 'Ankle Boots' },
  'Heels': { broad: 'Heels', detailed: 'Block Heels' },
  'Flats': { broad: 'Flats', detailed: 'Ballet Flats' },
  'Sandals': { broad: 'Sandals', detailed: 'Strappy Sandals' },

  // Accessories
  'Accessories': { broad: 'Bags', detailed: 'Handbags' },
  'Bags': { broad: 'Bags', detailed: 'Handbags' },
  'Jewelry': { broad: 'Jewelry', detailed: 'Necklaces' },
  'Hats': { broad: 'Hats', detailed: 'Baseball Caps' },
  'Scarves': { broad: 'Scarves', detailed: 'Silk Scarves' },
  'Belts': { broad: 'Belts', detailed: 'Leather Belts' },
  'Sunglasses': { broad: 'Sunglasses', detailed: 'Aviator Sunglasses' },
};

/**
 * Infer hierarchical categories from existing category
 */
export const inferHierarchicalCategories = (existingCategory) => {
  if (!existingCategory) {
    return { broad: 'T-Shirts', detailed: 'Basic Tees' };
  }

  // Check if we have a direct mapping
  const mapping = categoryMappings[existingCategory];
  if (mapping) {
    return mapping;
  }

  // Try to find a partial match
  const lowerCategory = existingCategory.toLowerCase();
  for (const [key, value] of Object.entries(categoryMappings)) {
    if (key.toLowerCase().includes(lowerCategory) || lowerCategory.includes(key.toLowerCase())) {
      return value;
    }
  }

  // Check if the existing category is already a broad category
  if (isBroadCategory(existingCategory)) {
    // Find the first detailed category for this broad category
    const detailedCategories = getDetailedCategories(existingCategory);
    return {
      broad: existingCategory,
      detailed: detailedCategories.length > 0 ? detailedCategories[0] : 'Unknown'
    };
  }

  // Check if the existing category is already a detailed category
  const broadCategory = getBroadCategoryForDetailed(existingCategory);
  if (broadCategory) {
    return {
      broad: broadCategory,
      detailed: existingCategory
    };
  }

  // Default fallback
  return { broad: 'T-Shirts', detailed: 'Basic Tees' };
};

/**
 * Extract tags from title and description
 */
export const extractTagsFromItem = (item) => {
  const tags = new Set();

  // Common clothing attributes to look for
  const colorKeywords = ['red', 'blue', 'green', 'yellow', 'black', 'white', 'pink', 'purple', 'orange', 'brown', 'gray', 'grey', 'navy', 'beige', 'khaki'];
  const materialKeywords = ['cotton', 'silk', 'wool', 'denim', 'leather', 'polyester', 'linen', 'cashmere', 'velvet', 'satin'];
  const styleKeywords = ['vintage', 'modern', 'classic', 'casual', 'formal', 'sporty', 'bohemian', 'minimalist', 'trendy', 'elegant'];
  const seasonKeywords = ['summer', 'winter', 'spring', 'fall', 'autumn'];
  const patternKeywords = ['striped', 'polka dot', 'floral', 'geometric', 'solid', 'printed', 'checkered', 'plaid'];

  const allKeywords = [...colorKeywords, ...materialKeywords, ...styleKeywords, ...seasonKeywords, ...patternKeywords];

  // Extract from title and description
  const text = `${item.title || ''} ${item.description || ''}`.toLowerCase();

  allKeywords.forEach(keyword => {
    if (text.includes(keyword)) {
      tags.add(keyword);
    }
  });

  // Extract from brand (if it's a style indicator)
  if (item.brand) {
    const brand = item.brand.toLowerCase();
    if (styleKeywords.includes(brand) || materialKeywords.includes(brand)) {
      tags.add(brand);
    }
  }

  return Array.from(tags);
};

/**
 * Migrate a single clothing item to hierarchical categories
 */
export const migrateClothingItem = async (itemId, itemData) => {
  try {
    const { broad, detailed } = inferHierarchicalCategories(itemData.category);
    const extractedTags = extractTagsFromItem(itemData);

    const updates = {
      broadCategory: broad,
      detailedCategory: detailed,
      tags: extractedTags,
      // Keep the original category for backward compatibility
      category: itemData.category || detailed,
      // Add migration timestamp
      migratedAt: new Date()
    };

    const itemRef = doc(db, 'clothingItems', itemId);
    await updateDoc(itemRef, updates);

    return {
      success: true,
      itemId,
      updates
    };
  } catch (error) {
    console.error(`Error migrating item ${itemId}:`, error);
    return {
      success: false,
      itemId,
      error: error.message
    };
  }
};

/**
 * Migrate all clothing items in batches
 */
export const migrateAllClothingItems = async (batchSize = 50, onProgress = null) => {
  try {
    console.log('Starting clothing items migration...');

    // Get all clothing items
    const clothingItemsRef = collection(db, 'clothingItems');
    const snapshot = await getDocs(clothingItemsRef);

    const totalItems = snapshot.docs.length;
    console.log(`Found ${totalItems} items to migrate`);

    if (totalItems === 0) {
      return { success: true, message: 'No items to migrate', results: [] };
    }

    const results = [];
    let processedCount = 0;

    // Process items in batches
    for (let i = 0; i < snapshot.docs.length; i += batchSize) {
      const batch = writeBatch(db);
      const batchDocs = snapshot.docs.slice(i, i + batchSize);

      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(totalItems / batchSize)}`);

      for (const docSnapshot of batchDocs) {
        const itemData = docSnapshot.data();

        // Skip if already migrated
        if (itemData.broadCategory && itemData.detailedCategory) {
          processedCount++;
          continue;
        }

        const { broad, detailed } = inferHierarchicalCategories(itemData.category);
        const extractedTags = extractTagsFromItem(itemData);

        const updates = {
          broadCategory: broad,
          detailedCategory: detailed,
          tags: extractedTags,
          category: itemData.category || detailed,
          migratedAt: new Date()
        };

        batch.update(doc(db, 'clothingItems', docSnapshot.id), updates);

        results.push({
          success: true,
          itemId: docSnapshot.id,
          updates
        });
      }

      // Commit the batch
      await batch.commit();
      processedCount += batchDocs.length;

      // Call progress callback if provided
      if (onProgress) {
        onProgress({
          processed: processedCount,
          total: totalItems,
          percentage: Math.round((processedCount / totalItems) * 100)
        });
      }

      // Small delay between batches to avoid overwhelming Firestore
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`Migration completed. Processed ${processedCount} items.`);

    return {
      success: true,
      message: `Successfully migrated ${processedCount} clothing items`,
      results,
      totalProcessed: processedCount
    };

  } catch (error) {
    console.error('Error during migration:', error);
    return {
      success: false,
      error: error.message,
      results: []
    };
  }
};

/**
 * Check migration status
 */
export const checkMigrationStatus = async () => {
  try {
    const clothingItemsRef = collection(db, 'clothingItems');
    const snapshot = await getDocs(clothingItemsRef);

    let totalItems = 0;
    let migratedItems = 0;

    snapshot.docs.forEach(doc => {
      totalItems++;
      const data = doc.data();
      if (data.broadCategory && data.detailedCategory) {
        migratedItems++;
      }
    });

    return {
      totalItems,
      migratedItems,
      pendingItems: totalItems - migratedItems,
      migrationComplete: migratedItems === totalItems,
      percentage: totalItems > 0 ? Math.round((migratedItems / totalItems) * 100) : 0
    };
  } catch (error) {
    console.error('Error checking migration status:', error);
    return {
      error: error.message
    };
  }
};
