import {
    collection,
    addDoc,
    serverTimestamp,
    query,
    where,
    getDocs,
    doc,
    getDoc,
    updateDoc
} from 'firebase/firestore';
import { db } from '../firebase.config';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase.config';

// Initialize Firebase functions
const sendTransactionStatusUpdateToSeller = httpsCallable(functions, 'sendTransactionStatusUpdateToSeller');
const sendTransactionNotificationToAdmin = httpsCallable(functions, 'sendTransactionNotificationToAdmin');

/**
 * Creates seller transactions when an order is completed
 * This function should be called when an order status changes to 'delivered' or 'completed'
 */
export const createSellerTransactionsFromOrder = async (orderId, orderData) => {
    try {
        console.log('[TransactionUtils] === DEBUGGING TRANSACTION CREATION ===');
        console.log('[TransactionUtils] Creating seller transactions for order:', orderId);
        console.log('[TransactionUtils] Order data received:', JSON.stringify(orderData, null, 2));
        console.log('[TransactionUtils] Order data keys:', Object.keys(orderData || {}));
        console.log('[TransactionUtils] Order amount field:', orderData?.amount);
        console.log('[TransactionUtils] Order totalAmount field:', orderData?.totalAmount);

        if (!orderData || !orderData.items || !Array.isArray(orderData.items)) {
            console.log('[TransactionUtils] Invalid order data or no items found');
            console.log('[TransactionUtils] orderData:', orderData);
            console.log('[TransactionUtils] orderData.items:', orderData?.items);
            return;
        } console.log('[TransactionUtils] Items in order:');
        orderData.items.forEach((item, index) => {
            console.log(`[TransactionUtils] Item ${index}:`, {
                id: item.id,
                itemId: item.itemId,
                title: item.title,
                price: item.price,
                priceType: typeof item.price,
                quantity: item.quantity,
                quantityType: typeof item.quantity,
                uploaderId: item.uploaderId,
                sellerId: item.sellerId,
                userId: item.userId
            });
        });

        // Group items by seller
        const sellerGroups = {}; for (const item of orderData.items) {
            // Try multiple possible fields for the seller ID
            let sellerId = item.uploaderId || item.sellerId || item.userId;

            // If we still don't have a sellerId, try to fetch it from the original item
            if (!sellerId && item.itemId) {
                try {
                    console.log('[TransactionUtils] Fetching uploaderId from original item:', item.itemId);
                    const originalItemDoc = await getDoc(doc(db, 'clothingItems', item.itemId));
                    if (originalItemDoc.exists()) {
                        const originalItemData = originalItemDoc.data();
                        sellerId = originalItemData.uploaderId;
                        console.log('[TransactionUtils] Found uploaderId from original item:', sellerId);
                    }
                } catch (error) {
                    console.error('[TransactionUtils] Error fetching original item:', error);
                }
            }

            if (!sellerId) {
                console.log('[TransactionUtils] ⚠️ Item without seller ID found:', JSON.stringify(item, null, 2));
                continue; // Skip items without seller ID
            } console.log('[TransactionUtils] Processing item for seller:', sellerId);
            console.log('[TransactionUtils] Item price:', item.price, 'Item quantity:', item.quantity);
            console.log('[TransactionUtils] Calculated item total:', (item.price * item.quantity || 0));

            if (!sellerGroups[sellerId]) {
                sellerGroups[sellerId] = {
                    items: [],
                    totalAmount: 0
                };
            }            // Ensure price and quantity are numbers
            let itemPrice = parseFloat(item.price) || 0;
            const itemQuantity = parseInt(item.quantity) || 1;            // If price is 0 or missing, try to fetch from original item
            if (itemPrice === 0 && item.itemId) {
                try {
                    console.log('[TransactionUtils] Price is 0, fetching from original item:', item.itemId);
                    const originalItemDoc = await getDoc(doc(db, 'clothingItems', item.itemId));
                    if (originalItemDoc.exists()) {
                        const originalItemData = originalItemDoc.data();
                        itemPrice = parseFloat(originalItemData.price) || 0;
                        console.log('[TransactionUtils] Found original price from DB:', itemPrice);

                        // If still no price, check if it's stored as a string
                        if (itemPrice === 0 && originalItemData.price) {
                            const priceString = originalItemData.price.toString().replace(/[^\d.]/g, '');
                            itemPrice = parseFloat(priceString) || 0;
                            console.log('[TransactionUtils] Parsed price from string:', itemPrice);
                        }
                    } else {
                        console.log('[TransactionUtils] ⚠️ Original item not found in database:', item.itemId);
                    }
                } catch (error) {
                    console.error('[TransactionUtils] Error fetching original item price:', error);
                }
            }            // Final check - if we still don't have a price, log critical error
            if (itemPrice === 0) {
                console.error('[TransactionUtils] ⚠️ CRITICAL: No price found for item after all attempts');
                console.error('[TransactionUtils] Item data:', JSON.stringify(item, null, 2));

                // Try one more approach - check if the price field exists but is a string
                if (item.price && typeof item.price === 'string') {
                    const stringPrice = item.price.replace(/[^\d.]/g, '');
                    itemPrice = parseFloat(stringPrice) || 0;
                    console.log('[TransactionUtils] Attempted string price conversion:', itemPrice);
                }

                // If we still have no price, this might be a test item or corrupted data
                if (itemPrice === 0) {
                    console.error('[TransactionUtils] ⚠️ USING FALLBACK: Item will have 0 price - needs manual review');
                }
            }

            const itemTotal = itemPrice * itemQuantity;

            console.log('[TransactionUtils] Final - Price:', itemPrice, 'Quantity:', itemQuantity, 'Total:', itemTotal);

            // Add the sellerId to the item for consistency
            const itemWithSellerId = {
                ...item,
                uploaderId: sellerId,
                price: itemPrice,
                quantity: itemQuantity,
                itemTotal: itemTotal
            };
            sellerGroups[sellerId].items.push(itemWithSellerId);
            sellerGroups[sellerId].totalAmount += itemTotal;
        }

        console.log('[TransactionUtils] Final seller groups summary:');
        Object.entries(sellerGroups).forEach(([sellerId, sellerData]) => {
            console.log(`[TransactionUtils] Seller ${sellerId}:`, {
                itemCount: sellerData.items.length,
                totalAmount: sellerData.totalAmount,
                items: sellerData.items.map(item => ({
                    id: item.itemId,
                    title: item.title,
                    price: item.price,
                    quantity: item.quantity,
                    itemTotal: item.itemTotal
                }))
            });
        });

        if (Object.keys(sellerGroups).length === 0) {
            console.log('[TransactionUtils] ⚠️ No seller groups created - no transactions will be generated');
            return;
        }

        console.log('[TransactionUtils] Found seller groups:', Object.keys(sellerGroups));

        // Create transaction for each seller
        const transactionPromises = Object.entries(sellerGroups).map(async ([sellerId, sellerData]) => {
            try {                // Check if transaction already exists for this order and seller with more detailed logging
                console.log('[TransactionUtils] Checking for existing transactions - Order:', orderId, 'Seller:', sellerId);
                const existingTransactionsQuery = query(
                    collection(db, 'sellerTransactions'),
                    where('orderId', '==', orderId),
                    where('sellerId', '==', sellerId)
                );

                const existingTransactions = await getDocs(existingTransactionsQuery);
                if (!existingTransactions.empty) {
                    console.log('[TransactionUtils] ⚠️ Transaction already exists for seller:', sellerId, '- Skipping creation');
                    console.log('[TransactionUtils] Existing transaction IDs:', existingTransactions.docs.map(doc => doc.id));
                    return existingTransactions.docs[0].id; // Return existing transaction ID
                }

                console.log('[TransactionUtils] ✅ No existing transaction found - proceeding with creation');

                // Get seller information
                let sellerInfo = null;
                try {
                    const sellerDoc = await getDoc(doc(db, 'users', sellerId));
                    if (sellerDoc.exists()) {
                        sellerInfo = sellerDoc.data();
                    }
                } catch (error) {
                    console.error('[TransactionUtils] Error fetching seller info:', error);
                }                // Calculate platform fee (0% for now)
                const platformFeeRate = 0;
                const grossAmount = sellerData.totalAmount || 0;
                const platformFee = grossAmount * platformFeeRate;
                const netAmount = grossAmount - platformFee;

                console.log('[TransactionUtils] Calculated amounts for seller:', sellerId);
                console.log('[TransactionUtils] - Gross amount:', grossAmount);
                console.log('[TransactionUtils] - Platform fee:', platformFee);
                console.log('[TransactionUtils] - Net amount:', netAmount);

                // Generate transaction number
                const transactionNumber = `TXN${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`; const transactionData = {
                    transactionNumber,
                    orderId,
                    sellerId, buyerId: orderData.userId || 'unknown',

                    // Amount details
                    grossAmount: grossAmount || 0,
                    platformFee: platformFee || 0,
                    amount: netAmount || 0, // Amount to be paid to seller

                    // Transaction details
                    status: 'pending_transfer', // pending_transfer -> transfer_in_progress -> transferred
                    description: `Payment for ${sellerData.items.length} item(s) from order #${orderId.slice(-8).toUpperCase()}`,                    // Items information
                    itemsCount: sellerData.items.length || 0,
                    itemsList: (sellerData.items || []).map(item => ({
                        id: item.id || item.itemId || 'unknown',
                        title: item.title || 'Unknown Item',
                        price: item.price || 0,
                        quantity: item.quantity || 1
                    })),

                    // Seller information
                    sellerName: sellerInfo?.name || 'Unknown Seller',
                    sellerEmail: sellerInfo?.email || 'N/A',                    // Order information
                    orderStatus: orderData.status || 'pending',
                    orderTotal: orderData.amount || orderData.totalAmount || grossAmount || 0,

                    // Timestamps
                    createdAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),

                    // Admin fields (will be filled when admin processes payment)
                    adminNotes: '',
                    transactionDetails: null,
                    transferredAt: null,
                    processedBy: null
                }; console.log('[TransactionUtils] Creating transaction for seller:', sellerId, 'Amount:', netAmount);
                console.log('[TransactionUtils] Final transaction data:', JSON.stringify(transactionData, null, 2));

                const docRef = await addDoc(collection(db, 'sellerTransactions'), transactionData);

                // Create audit trail entry
                await createTransactionAuditEntry(
                    docRef.id,
                    null,
                    'pending_transfer',
                    'system',
                    `Transaction created for order ${orderId}`
                );                // Send notification to admin about new transaction
                try {
                    console.log('[TransactionUtils] Attempting to send admin notification...');
                    await sendTransactionNotificationToAdmin({
                        transactionData: {
                            ...transactionData,
                            id: docRef.id
                        },
                        eventType: 'new_transaction'
                    });
                    console.log('[TransactionUtils] Admin notification sent successfully');
                } catch (emailError) {
                    console.error('[TransactionUtils] Failed to send admin notification:', emailError);
                    console.error('[TransactionUtils] This is not critical - transaction was still created successfully');
                    // Don't throw error - transaction creation is more important than email
                }

                console.log('[TransactionUtils] Successfully created transaction:', docRef.id);
                return docRef.id;

            } catch (error) {
                console.error('[TransactionUtils] Error creating transaction for seller:', sellerId, error);
                throw error;
            }
        });

        await Promise.all(transactionPromises);
        console.log('[TransactionUtils] All seller transactions created successfully');

    } catch (error) {
        console.error('[TransactionUtils] Error creating seller transactions:', error);
        throw error;
    }
};

/**
 * Updates a seller transaction when admin transfers payment
 */
export const markTransactionAsCompleted = async (transactionId, transferDetails, finalStatus = 'transferred') => {
    try {
        const transactionRef = doc(db, 'sellerTransactions', transactionId);

        // Get current transaction data
        const transactionDoc = await getDoc(transactionRef);
        if (!transactionDoc.exists()) {
            throw new Error('Transaction not found');
        }

        const currentData = transactionDoc.data();
        const previousStatus = currentData.status;

        // Determine final status - accept both 'completed' and 'transferred'
        const finalTransactionStatus = finalStatus === 'completed' ? 'completed' : 'transferred';

        const updateData = {
            status: finalTransactionStatus,
            transferredAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            processedBy: transferDetails.adminId,
            adminNotes: transferDetails.notes,
            transactionDetails: {
                method: transferDetails.method,
                referenceNumber: transferDetails.referenceNumber,
                bankName: transferDetails.bankName
            }
        };

        await updateDoc(transactionRef, updateData);

        // Create audit trail entry
        await createTransactionAuditEntry(
            transactionId,
            previousStatus,
            finalTransactionStatus,
            transferDetails.adminId,
            `Transfer completed: ${transferDetails.notes || 'No additional notes'}`
        );        // Send email notifications
        const updatedTransactionData = { ...currentData, ...updateData, id: transactionId }; try {
            // Notify seller - use the same email template for both statuses
            await sendTransactionStatusUpdateToSeller({
                transactionData: updatedTransactionData,
                newStatus: finalTransactionStatus,
                adminNotes: transferDetails.notes || ''
            });

            // Notify admin
            await sendTransactionNotificationToAdmin({
                transactionData: updatedTransactionData,
                eventType: 'transfer_completed'
            });
        } catch (emailError) {
            console.error('[TransactionUtils] Failed to send notifications. Raw error object:', emailError);
            if (emailError.code) {
                console.error('[TransactionUtils] Email error code:', emailError.code);
            }
            if (emailError.message) {
                console.error('[TransactionUtils] Email error message:', emailError.message);
            }
            if (emailError.details) {
                console.error('[TransactionUtils] Email error details:', JSON.stringify(emailError.details, null, 2));
            }
            // Don't throw error - transaction update is more important than email
        }

        console.log('[TransactionUtils] Transaction marked as', finalTransactionStatus, ':', transactionId);

    } catch (error) {
        console.error('[TransactionUtils] Error marking transaction as completed:', error);
        throw error;
    }
};

/**
 * Updates a seller transaction status to transfer_in_progress
 */
export const markTransactionInProgress = async (transactionId, adminId) => {
    try {
        const transactionRef = doc(db, 'sellerTransactions', transactionId);

        // Get current transaction data
        const transactionDoc = await getDoc(transactionRef);
        if (!transactionDoc.exists()) {
            throw new Error('Transaction not found');
        }

        const currentData = transactionDoc.data();
        const previousStatus = currentData.status;

        const updateData = {
            status: 'transfer_in_progress',
            transferStartedAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            processedBy: adminId
        };

        await updateDoc(transactionRef, updateData);

        // Create audit trail entry
        await createTransactionAuditEntry(
            transactionId,
            previousStatus,
            'transfer_in_progress',
            adminId,
            'Transfer initiated by admin'
        );        // Send email notification to seller
        try {
            await sendTransactionStatusUpdateToSeller({
                transactionData: {
                    ...currentData,
                    ...updateData,
                    id: transactionId
                },
                newStatus: 'transfer_in_progress',
                adminNotes: ''
            });
        } catch (emailError) {
            console.error('[TransactionUtils] Failed to send seller notification:', emailError);
            // Don't throw error - transaction update is more important than email
        }

        console.log('[TransactionUtils] Transaction marked as in progress:', transactionId);

    } catch (error) {
        console.error('[TransactionUtils] Error marking transaction as in progress:', error);
        throw error;
    }
};

/**
 * Creates an audit trail entry for transaction status changes
 */
export const createTransactionAuditEntry = async (transactionId, previousStatus, newStatus, actionBy, notes = '') => {
    try {
        const auditData = {
            transactionId,
            previousStatus,
            newStatus,
            actionBy,
            notes,
            timestamp: serverTimestamp(),
            actionType: 'status_change'
        };

        await addDoc(collection(db, 'transactionAudit'), auditData);
        console.log('[TransactionUtils] Audit entry created for transaction:', transactionId);

    } catch (error) {
        console.error('[TransactionUtils] Error creating audit entry:', error);
        throw error;
    }
};

/**
 * Gets audit trail for a transaction
 */
export const getTransactionAuditTrail = async (transactionId) => {
    try {
        const auditQuery = query(
            collection(db, 'transactionAudit'),
            where('transactionId', '==', transactionId)
        );

        const auditSnapshot = await getDocs(auditQuery);
        const auditTrail = [];

        auditSnapshot.forEach(doc => {
            auditTrail.push({
                id: doc.id,
                ...doc.data()
            });
        });

        // Sort by timestamp (most recent first)
        auditTrail.sort((a, b) => {
            if (a.timestamp && b.timestamp) {
                return b.timestamp.toDate() - a.timestamp.toDate();
            }
            return 0;
        });

        return auditTrail;

    } catch (error) {
        console.error('[TransactionUtils] Error getting audit trail:', error);
        throw error;
    }
};

export default {
    createSellerTransactionsFromOrder,
    markTransactionAsCompleted,
    markTransactionInProgress,
    createTransactionAuditEntry,
    getTransactionAuditTrail
};
